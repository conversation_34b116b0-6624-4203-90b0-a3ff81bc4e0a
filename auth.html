<!DOCTYPE html>
<!--
  Copyright (c) 2023 Ali MESSAOUDI
  Tous droits réservés.
  Ce code est protégé par les lois sur le droit d'auteur et ne peut être reproduit,
  distribué ou utilisé sans autorisation explicite de l'auteur.
-->
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Connexion - smartDiv</title>
    <link rel="stylesheet" href="css/styles.css" />
    <link rel="stylesheet" href="css/auth-styles.css" />
    <link rel="stylesheet" href="css/modern-nav.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <header>
      <div class="header-content">
        <a href="index.html" class="logo-link">
          <img
            src="images/logo.jpg"
            alt="Logo smartDiv"
            class="logo-image"
            width="60"
            height="60"
          />
        </a>
        <div class="header-controls">
          <button
            id="theme-toggle"
            class="btn-icon"
            aria-label="Changer de thème"
            title="Changer de thème"
          >
            <i class="fas fa-moon" aria-hidden="true"></i>
            <span class="visually-hidden">Mode sombre</span>
          </button>
        </div>
      </div>
      <nav class="main-nav" aria-label="Navigation principale">
        <div class="main-nav-container">
          <button
            class="mobile-menu-toggle"
            aria-label="Menu"
            aria-expanded="false"
            id="mobile-menu-toggle"
          >
            <div class="hamburger">
              <span></span>
              <span></span>
              <span></span>
              <span></span>
            </div>
          </button>

          <ul>
            <li>
              <a href="index.html"><i class="fas fa-calculator"></i> Accueil</a>
            </li>
            <li>
              <a href="#" id="nav-aide"
                ><i class="fas fa-question-circle"></i> Aide</a
              >
            </li>
            <li>
              <a href="#" id="nav-a-propos"
                ><i class="fas fa-info-circle"></i> À propos</a
              >
            </li>
          </ul>
        </div>
      </nav>
    </header>

    <main class="auth-main">
      <div class="auth-container">
        <!-- Panneau de gauche - Informations -->
        <div class="auth-info-panel">
          <div class="auth-info-content">
            <h2>Bienvenue sur smartDiv</h2>
            <p class="auth-tagline">
              Apprenez la division euclidienne de manière interactive et ludique
            </p>

            <div class="auth-features">
              <div class="auth-feature">
                <div class="auth-feature-icon">
                  <i class="fas fa-chalkboard"></i>
                </div>
                <div class="auth-feature-text">
                  <h3>Apprentissage interactif</h3>
                  <p>Visualisez chaque étape de la division euclidienne</p>
                </div>
              </div>

              <div class="auth-feature">
                <div class="auth-feature-icon">
                  <i class="fas fa-chart-line"></i>
                </div>
                <div class="auth-feature-text">
                  <h3>Suivi de progression</h3>
                  <p>Suivez votre évolution et identifiez vos points forts</p>
                </div>
              </div>

              <div class="auth-feature">
                <div class="auth-feature-icon">
                  <i class="fas fa-graduation-cap"></i>
                </div>
                <div class="auth-feature-text">
                  <h3>Adapté à votre niveau</h3>
                  <p>Exercices personnalisés selon votre progression</p>
                </div>
              </div>
            </div>

            <div class="auth-testimonials">
              <h3>Ce que disent nos utilisateurs</h3>
              <div class="auth-testimonial">
                <p>
                  "Mes élèves ont fait d'énormes progrès grâce à cette
                  application !"
                </p>
                <div class="auth-testimonial-author">
                  <span>Marie D.</span>
                  <span class="auth-testimonial-role">Enseignante en CM2</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Panneau de droite - Formulaires -->
        <div class="auth-form-panel">
          <div class="auth-tabs">
            <button class="auth-tab active" data-tab="login-tab">
              <i class="fas fa-sign-in-alt"></i> Connexion
            </button>
            <button class="auth-tab" data-tab="register-tab">
              <i class="fas fa-user-plus"></i> Inscription
            </button>
          </div>

          <!-- Onglet de connexion -->
          <div id="login-tab" class="auth-tab-content active">
            <h2>Connexion</h2>
            <p class="auth-intro">
              Connectez-vous pour accéder à votre espace personnel et suivre
              votre progression.
            </p>

            <form id="login-form" class="auth-form">
              <div class="form-group">
                <label for="login-email">Email</label>
                <div class="input-with-icon">
                  <i class="fas fa-envelope"></i>
                  <input
                    type="email"
                    id="login-email"
                    name="email"
                    placeholder="Votre adresse email"
                    required
                    autocomplete="email"
                  />
                </div>
                <div class="form-feedback"></div>
              </div>

              <div class="form-group">
                <label for="login-password">Mot de passe</label>
                <div class="input-with-icon">
                  <i class="fas fa-lock"></i>
                  <input
                    type="password"
                    id="login-password"
                    name="password"
                    placeholder="Votre mot de passe"
                    required
                    autocomplete="current-password"
                  />
                  <button
                    type="button"
                    class="toggle-password"
                    aria-label="Afficher/masquer le mot de passe"
                  >
                    <i class="fas fa-eye"></i>
                  </button>
                </div>
                <div class="form-feedback"></div>
              </div>

              <div class="form-group remember-me">
                <input type="checkbox" id="remember" name="remember" />
                <label for="remember">Se souvenir de moi</label>
              </div>

              <div class="form-group">
                <button type="submit" class="btn-primary btn-auth-submit">
                  <i class="fas fa-sign-in-alt"></i> Se connecter
                  <span class="spinner hidden"
                    ><i class="fas fa-circle-notch fa-spin"></i
                  ></span>
                </button>
              </div>

              <div class="auth-links">
                <a href="#" class="forgot-password">Mot de passe oublié ?</a>
                <span class="separator">|</span>
                <a href="#" class="switch-tab" data-tab="register-tab"
                  >Créer un compte</a
                >
              </div>

              <div class="auth-feedback hidden"></div>
            </form>

            <div class="auth-separator">
              <span>ou continuer avec</span>
            </div>

            <div class="social-login">
              <div class="social-login-row">
                <button class="btn-google">
                  <i class="fab fa-google"></i> Google
                </button>
                <button class="btn-facebook">
                  <i class="fab fa-facebook-f"></i> Facebook
                </button>
              </div>
              <div class="social-login-row">
                <button class="btn-apple">
                  <i class="fab fa-apple"></i> Apple
                </button>
                <button class="btn-microsoft">
                  <i class="fab fa-microsoft"></i> Microsoft
                </button>
              </div>
            </div>
          </div>

          <!-- Onglet d'inscription -->
          <div id="register-tab" class="auth-tab-content">
            <h2>Créer un compte</h2>
            <p class="auth-intro">
              Rejoignez smartDiv pour profiter de toutes les fonctionnalités
              d'apprentissage.
            </p>

            <form id="register-form" class="auth-form">
              <div class="form-group">
                <label>Type d'utilisateur</label>
                <div class="user-type-selector">
                  <div class="user-type-option">
                    <input
                      type="radio"
                      id="student"
                      name="user-type"
                      value="student"
                      checked
                    />
                    <label for="student">
                      <i class="fas fa-user-graduate"></i>
                      <span>Élève</span>
                    </label>
                  </div>
                  <div class="user-type-option">
                    <input
                      type="radio"
                      id="teacher"
                      name="user-type"
                      value="teacher"
                    />
                    <label for="teacher">
                      <i class="fas fa-chalkboard-teacher"></i>
                      <span>Enseignant</span>
                    </label>
                  </div>
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="firstname">Prénom</label>
                  <div class="input-with-icon">
                    <i class="fas fa-user"></i>
                    <input
                      type="text"
                      id="firstname"
                      name="firstname"
                      placeholder="Votre prénom"
                      required
                    />
                  </div>
                  <div class="form-feedback"></div>
                </div>

                <div class="form-group">
                  <label for="lastname">Nom</label>
                  <div class="input-with-icon">
                    <i class="fas fa-user"></i>
                    <input
                      type="text"
                      id="lastname"
                      name="lastname"
                      placeholder="Votre nom"
                      required
                    />
                  </div>
                  <div class="form-feedback"></div>
                </div>
              </div>

              <div class="form-group">
                <label for="register-email">Email</label>
                <div class="input-with-icon">
                  <i class="fas fa-envelope"></i>
                  <input
                    type="email"
                    id="register-email"
                    name="email"
                    placeholder="Votre adresse email"
                    required
                    autocomplete="email"
                  />
                </div>
                <div class="form-feedback"></div>
              </div>

              <div class="form-group">
                <label for="register-password">Mot de passe</label>
                <div class="input-with-icon">
                  <i class="fas fa-lock"></i>
                  <input
                    type="password"
                    id="register-password"
                    name="password"
                    placeholder="Créez un mot de passe"
                    required
                    autocomplete="new-password"
                  />
                  <button
                    type="button"
                    class="toggle-password"
                    aria-label="Afficher/masquer le mot de passe"
                  >
                    <i class="fas fa-eye"></i>
                  </button>
                </div>
                <div class="password-strength">
                  <div class="strength-meter">
                    <div class="strength-segment"></div>
                    <div class="strength-segment"></div>
                    <div class="strength-segment"></div>
                    <div class="strength-segment"></div>
                  </div>
                  <span class="strength-text">Force du mot de passe</span>
                </div>
                <div class="password-tips">
                  <small
                    >Utilisez au moins 8 caractères avec des lettres, chiffres
                    et symboles</small
                  >
                </div>
                <div class="form-feedback"></div>
              </div>

              <div class="form-group">
                <label for="confirm-password">Confirmer le mot de passe</label>
                <div class="input-with-icon">
                  <i class="fas fa-lock"></i>
                  <input
                    type="password"
                    id="confirm-password"
                    name="confirm-password"
                    placeholder="Confirmez votre mot de passe"
                    required
                    autocomplete="new-password"
                  />
                </div>
                <div class="form-feedback"></div>
              </div>

              <div class="form-group teacher-fields hidden">
                <label for="school">Établissement</label>
                <div class="input-with-icon">
                  <i class="fas fa-school"></i>
                  <input
                    type="text"
                    id="school"
                    name="school"
                    placeholder="Nom de votre établissement"
                  />
                </div>
                <div class="form-feedback"></div>
              </div>

              <div class="form-group student-fields">
                <label for="grade">Niveau scolaire</label>
                <div class="input-with-icon">
                  <i class="fas fa-graduation-cap"></i>
                  <select id="grade" name="grade">
                    <option value="">Sélectionnez votre niveau</option>
                    <option value="ce1">CE1</option>
                    <option value="ce2">CE2</option>
                    <option value="cm1">CM1</option>
                    <option value="cm2">CM2</option>
                    <option value="6eme">6ème</option>
                    <option value="5eme">5ème</option>
                    <option value="4eme">4ème</option>
                    <option value="3eme">3ème</option>
                  </select>
                </div>
                <div class="form-feedback"></div>
              </div>

              <div class="form-group terms">
                <input type="checkbox" id="terms" name="terms" required />
                <label for="terms">
                  J'accepte les <a href="#">conditions d'utilisation</a> et la
                  <a href="#">politique de confidentialité</a>
                </label>
                <div class="form-feedback"></div>
              </div>

              <div class="form-group">
                <button type="submit" class="btn-primary btn-auth-submit">
                  <i class="fas fa-user-plus"></i> Créer mon compte
                  <span class="spinner hidden"
                    ><i class="fas fa-circle-notch fa-spin"></i
                  ></span>
                </button>
              </div>

              <div class="auth-links">
                <span>Vous avez déjà un compte ?</span>
                <a href="#" class="switch-tab" data-tab="login-tab"
                  >Se connecter</a
                >
              </div>

              <div class="auth-feedback hidden"></div>
            </form>

            <div class="auth-separator">
              <span>ou s'inscrire avec</span>
            </div>

            <div class="social-login">
              <div class="social-login-row">
                <button class="btn-google">
                  <i class="fab fa-google"></i> Google
                </button>
                <button class="btn-facebook">
                  <i class="fab fa-facebook-f"></i> Facebook
                </button>
              </div>
              <div class="social-login-row">
                <button class="btn-apple">
                  <i class="fab fa-apple"></i> Apple
                </button>
                <button class="btn-microsoft">
                  <i class="fab fa-microsoft"></i> Microsoft
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <footer>
      <div class="footer-content">
        <div class="footer-section">
          <h3>smartDiv</h3>
          <p>
            Une application éducative pour apprendre la division euclidienne.
          </p>
        </div>
        <div class="footer-section">
          <h3>Liens utiles</h3>
          <ul>
            <li><a href="index.html">Accueil</a></li>
            <li><a href="auth.html">Connexion</a></li>
            <li><a href="auth.html#register">Inscription</a></li>
          </ul>
        </div>
        <div class="footer-section">
          <h3>Contact</h3>
          <p><i class="fas fa-envelope"></i> <EMAIL></p>
        </div>
      </div>
      <div class="footer-bottom">
        <p>
          &copy; <span id="current-year">2023</span> Ali MESSAOUDI - smartDiv -
          Tous droits réservés
        </p>
        <p class="footer-version">
          <a href="LICENSE.txt" id="footer-license" aria-label="Licence"
            >Licence</a
          >
        </p>
      </div>
    </footer>

    <div id="toast-container" class="toast-container"></div>

    <script src="js/modern-nav.js"></script>
    <script src="js/auth-page.js"></script>
    <script>
      // Mettre à jour l'année automatiquement et gérer les fragments d'URL
      document.addEventListener("DOMContentLoaded", function () {
        // Mettre à jour l'année
        const yearElement = document.getElementById("current-year");
        if (yearElement) {
          yearElement.textContent = new Date().getFullYear();
        }

        // Vérifier si on doit afficher l'onglet d'inscription
        if (window.location.hash === "#register") {
          const registerTab = document.querySelector(
            '.auth-tab[data-tab="register-tab"]'
          );
          if (registerTab) {
            registerTab.click();
          }
        }
      });
    </script>
  </body>
</html>
