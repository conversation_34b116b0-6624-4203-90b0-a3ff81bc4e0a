/**
 * dashboard.js
 *
 * Copyright (c) 2023 Ali MESSAOUDI
 * Tous droits réservés.
 * Ce code est protégé par les lois sur le droit d'auteur et ne peut être reproduit,
 * distribué ou utilisé sans autorisation explicite de l'auteur.
 *
 * Système de tableau de bord pour DivisionSim
 * Gère les fonctionnalités des tableaux de bord élève et enseignant
 */
class DashboardSystem {
  constructor() {
    // Éléments DOM - Menu utilisateur
    this.userButton = document.getElementById('user-button');
    this.userMenu = document.getElementById('user-menu');
    this.userName = document.getElementById('user-name');
    this.userNameDisplay = document.getElementById('user-name-display');
    this.userRole = document.getElementById('user-role');
    this.welcomeName = document.getElementById('welcome-name');
    this.logoutLink = document.getElementById('logout-link');

    // Éléments DOM - Thème
    this.themeToggle = document.getElementById('theme-toggle');

    // État de l'utilisateur
    this.currentUser = null;

    // Initialiser le système
    this.init();
  }

  /**
   * Initialise le système de tableau de bord
   */
  init() {
    // Charger l'utilisateur depuis le stockage local
    this.loadUserFromStorage();

    // Ajouter les écouteurs d'événements
    this.addEventListeners();

    // Initialiser le thème
    this.initTheme();

    // Mettre à jour l'interface utilisateur
    this.updateUI();
  }

  /**
   * Charge l'utilisateur depuis le stockage local
   */
  loadUserFromStorage() {
    const storedUser = localStorage.getItem('currentUser');
    if (storedUser) {
      try {
        this.currentUser = JSON.parse(storedUser);
      } catch (error) {
        console.error('Erreur lors du chargement de l\'utilisateur:', error);
        localStorage.removeItem('currentUser');
        // Rediriger vers la page de connexion si l'utilisateur n'est pas connecté
        window.location.href = 'auth.html';
      }
    } else {
      // Rediriger vers la page de connexion si l'utilisateur n'est pas connecté
      window.location.href = 'auth.html';
    }
  }

  /**
   * Initialise le thème
   */
  initTheme() {
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
      document.body.classList.add('dark-theme');
      this.themeToggle.querySelector('i').classList.remove('fa-moon');
      this.themeToggle.querySelector('i').classList.add('fa-sun');
    }
  }

  /**
   * Ajoute tous les écouteurs d'événements nécessaires
   */
  addEventListeners() {
    // Menu utilisateur
    if (this.userButton) {
      this.userButton.addEventListener('click', () => {
        this.toggleUserMenu();
      });
    }

    // Déconnexion
    if (this.logoutLink) {
      this.logoutLink.addEventListener('click', (e) => {
        e.preventDefault();
        this.logout();
      });
    }

    // Thème
    if (this.themeToggle) {
      this.themeToggle.addEventListener('click', () => {
        this.toggleTheme();
      });
    }

    // Fermeture du menu utilisateur en cliquant en dehors
    document.addEventListener('click', (e) => {
      if (this.userMenu &&
          this.userMenu.classList.contains('visible') &&
          !this.userMenu.contains(e.target) &&
          !this.userButton.contains(e.target)) {
        this.userMenu.classList.remove('visible');
      }
    });

    // Ajouter des écouteurs pour les boutons d'action
    this.addActionListeners();
  }

  /**
   * Ajoute des écouteurs pour les boutons d'action
   */
  addActionListeners() {
    // Exemple d'écouteur pour les boutons d'action
    const actionButtons = document.querySelectorAll('.btn-primary, .btn-secondary');
    actionButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        // Empêcher la navigation par défaut pour les liens
        if (button.tagName === 'A') {
          e.preventDefault();
        }

        // Afficher un message toast pour indiquer que la fonctionnalité est en cours de développement
        if (!button.classList.contains('implemented')) {
          this.showToast('Cette fonctionnalité est en cours de développement.', 'info');
        }
      });
    });
  }

  /**
   * Met à jour l'interface utilisateur en fonction de l'état de connexion
   */
  updateUI() {
    if (this.currentUser) {
      // Mettre à jour le nom d'utilisateur
      if (this.userNameDisplay) {
        this.userNameDisplay.textContent = this.currentUser.firstname;
      }

      if (this.userName) {
        this.userName.textContent = `${this.currentUser.firstname} ${this.currentUser.lastname}`;
      }

      if (this.welcomeName) {
        this.welcomeName.textContent = this.currentUser.firstname;
      }

      // Mettre à jour le rôle
      if (this.userRole) {
        this.userRole.textContent = this.currentUser.role === 'teacher' ? 'Enseignant' : 'Élève';
      }

      // Vérifier si l'utilisateur est sur le bon tableau de bord
      this.checkCorrectDashboard();
    } else {
      // Rediriger vers la page de connexion si l'utilisateur n'est pas connecté
      window.location.href = 'auth.html';
    }
  }

  /**
   * Vérifie si l'utilisateur est sur le bon tableau de bord
   */
  checkCorrectDashboard() {
    const currentPage = window.location.pathname.split('/').pop();
    const isTeacher = this.currentUser.role === 'teacher';

    if (isTeacher && currentPage === 'student-dashboard.html') {
      // Rediriger l'enseignant vers son tableau de bord
      window.location.href = 'teacher-dashboard.html';
    } else if (!isTeacher && currentPage === 'teacher-dashboard.html') {
      // Rediriger l'élève vers son tableau de bord
      window.location.href = 'student-dashboard.html';
    }
  }

  /**
   * Affiche ou masque le menu utilisateur
   */
  toggleUserMenu() {
    this.userMenu.classList.toggle('visible');
  }

  /**
   * Bascule entre le thème clair et sombre
   */
  toggleTheme() {
    const isDark = document.body.classList.toggle('dark-theme');
    const icon = this.themeToggle.querySelector('i');

    if (isDark) {
      icon.classList.remove('fa-moon');
      icon.classList.add('fa-sun');
      localStorage.setItem('theme', 'dark');
    } else {
      icon.classList.remove('fa-sun');
      icon.classList.add('fa-moon');
      localStorage.setItem('theme', 'light');
    }
  }

  /**
   * Déconnecte l'utilisateur
   */
  logout() {
    // Supprimer l'utilisateur du stockage local
    localStorage.removeItem('currentUser');

    // Rediriger vers la page de connexion
    window.location.href = 'auth.html';
  }

  /**
   * Affiche un message toast
   * @param {string} message - Le message à afficher
   * @param {string} type - Le type de message (success, error, info, warning)
   */
  showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;

    // Ajouter le toast au conteneur
    const container = document.getElementById('toast-container');
    container.appendChild(toast);

    // Afficher le toast
    setTimeout(() => {
      toast.classList.add('show');
    }, 10);

    // Supprimer le toast après 3 secondes
    setTimeout(() => {
      toast.classList.remove('show');
      setTimeout(() => {
        toast.remove();
      }, 300);
    }, 3000);
  }
}

// Initialiser le système de tableau de bord lorsque le DOM est chargé
document.addEventListener('DOMContentLoaded', () => {
  new DashboardSystem();
});
