/*
 * smartDiv - Styles modernisés
 * Ce fichier importe tous les fichiers CSS modernisés
 */

/* Importation des polices Google Fonts */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Montserrat:wght@500;600;700&family=Fira+Code:wght@400;500&display=swap");

/* Importation des fichiers CSS modernisés */
@import "modern-design-system.css";
@import "modern-components.css";
@import "modern-navigation.css";
/* modern-simulation.css a été intégré dans styles.css */
@import "modern-excel.css";
@import "modern-modals.css";
@import "modern-footer.css";

/* Styles supplémentaires et overrides */
html {
  scroll-behavior: smooth;
}

body {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
}

/* Styles d'impression */
@media print {
  header,
  footer,
  .btn-tool,
  .excel-actions,
  .excel-toolbar,
  .excel-footer {
    display: none !important;
  }

  body {
    background-color: white;
  }

  .excel-sheet-wrapper {
    box-shadow: none;
    border: none;
  }

  .excel-container {
    overflow: visible;
  }

  #excel-table {
    page-break-inside: avoid;
  }
}

/* Styles pour les lecteurs d'écran */
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
