'use strict';

/**
 * Simulation de Division Euclidienne - smartDiv
 * Version 1.0.0 - Optimisée pour la performance et l'accessibilité
 *
 * Ce script est le cœur de l'application smartDiv. Il gère toute la logique
 * de simulation de division euclidienne, y compris :
 * - L'interface utilisateur interactive
 * - Les différents types de division (avec reste, exacte, décimale, etc.)
 * - La visualisation étape par étape du processus de division
 * - La génération et manipulation du tableau Excel
 * - Les optimisations de performance et d'accessibilité
 *
 * Structure du code :
 * - Classe DivisionSimulation : Classe principale qui encapsule toute la logique
 * - Méthodes d'initialisation : initialize(), setupEvents(), etc.
 * - Méthodes de simulation : startSimulation(), processDigit(), etc.
 * - Méthodes d'interface utilisateur : updateUI(), showStage(), etc.
 * - Méthodes d'accessibilité : setupAccessibility(), etc.
 * - Méthodes de performance : setupPerformanceOptimizations(), etc.
 *
 * <AUTHOR> MESSAOUDI
 * @copyright 2023 Ali MESSAOUDI
 */
class DivisionSimulation {
  /**
   * Constructeur de la classe DivisionSimulation
   * Initialise toutes les propriétés et configure l'application
   *
   * Le constructeur est responsable de :
   * 1. Initialiser les propriétés mathématiques (dividend, divisor, etc.)
   * 2. Configurer les propriétés de performance pour une expérience fluide
   * 3. Configurer les propriétés d'accessibilité pour tous les utilisateurs
   * 4. Appeler toutes les méthodes d'initialisation nécessaires
   */
  constructor() {
    // ======== Propriétés mathématiques de base ========
    this.dividend = 0;          // Le nombre à diviser
    this.divisor = 0;           // Le nombre par lequel on divise
    this.quotient = 0;          // Le résultat de la division
    this.remainder = 0;         // Le reste de la division
    this.digits = [];           // Les chiffres du dividende
    this.index = 0;             // Index actuel dans le processus de division
    this.currentRemainder = 0;  // Reste intermédiaire pendant le calcul
    this.currentEstimate = 0;   // Estimation actuelle du quotient partiel
    this.operationCount = 0;    // Compteur d'opérations effectuées
    this.divisionType = 'remainder'; // Type de division: 'remainder', 'exact', 'decimal', 'fraction', 'grouping'
    this.decimalPlaces = 2;     // Nombre de décimales pour la division décimale

    // ======== Propriétés d'optimisation de performance ========
    this.animationFrameId = null;       // ID pour requestAnimationFrame
    this.resizeObserver = null;         // Observer pour les changements de taille
    this.intersectionObserver = null;   // Observer pour le lazy loading
    this.debounceTimers = {};           // Timers pour limiter les appels fréquents
    this.touchStartX = 0;               // Position X initiale pour les événements tactiles
    this.touchStartY = 0;               // Position Y initiale pour les événements tactiles

    // ======== Propriétés d'accessibilité ========
    // Détecte si l'utilisateur préfère réduire les animations
    this.prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    this.highContrastMode = false;      // Mode contraste élevé pour malvoyants

    // ======== Séquence d'initialisation ========
    // Initialisation des éléments DOM et des propriétés
    this.initialize();

    // Configuration des écouteurs d'événements
    this.setupEvents();

    // Configuration des fonctionnalités d'accessibilité
    this.setupAccessibility();

    // Configuration du basculement de thème (clair/sombre)
    this.setupThemeToggle();

    // Configuration de l'aide pour le tableau Excel
    this.setupExcelHelp();

    // Configuration du système de notifications toast
    this.setupToastNotifications();

    // Configuration de la navigation
    this.setupNavigation();

    // Configuration des boîtes de dialogue modales
    this.setupModals();

    // Configuration du pied de page
    this.setupFooter();

    // Configuration des optimisations de performance
    this.setupPerformanceOptimizations();

    // Mettre à jour l'année dans le pied de page
    this.updateYear();

    // Vérifier et appliquer les préférences de l'utilisateur
    this.checkUserPreferences();
  }

  /**
   * Configure les optimisations de performance pour l'application
   *
   * Cette méthode met en place plusieurs techniques d'optimisation :
   * - Utilisation de requestAnimationFrame pour des animations fluides
   * - Lazy loading des éléments avec IntersectionObserver
   * - Détection des changements de taille avec ResizeObserver
   * - Debouncing des événements fréquents (scroll, resize, touch)
   *
   * Ces optimisations permettent d'améliorer significativement les performances
   * de l'application, en particulier sur les appareils mobiles ou moins puissants.
   */
  setupPerformanceOptimizations() {
    // Utiliser requestAnimationFrame pour synchroniser les animations avec le taux de rafraîchissement de l'écran
    // Cela évite les saccades et réduit la consommation de ressources
    this.setupAnimationFrames();

    // Observer les éléments pour le lazy loading (chargement paresseux)
    // Cela permet de charger les éléments uniquement lorsqu'ils deviennent visibles
    this.setupIntersectionObserver();

    // Observer les changements de taille pour les éléments responsifs
    // Cela permet d'adapter dynamiquement la mise en page sans polling constant
    this.setupResizeObserver();

    // Optimiser les événements fréquents comme scroll, resize et touch
    // Cela évite d'exécuter trop souvent des fonctions coûteuses
    this.setupEventOptimizations();
  }

  /**
   * Configurer les animations avec requestAnimationFrame
   */
  setupAnimationFrames() {
    // Fonction pour mettre à jour les animations
    const updateAnimations = () => {
      // Mettre à jour les animations ici si nécessaire
      this.animationFrameId = requestAnimationFrame(updateAnimations);
    };

    // Démarrer la boucle d'animation
    this.animationFrameId = requestAnimationFrame(updateAnimations);

    // Arrêter les animations lorsque l'onglet n'est pas visible
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        if (this.animationFrameId) {
          cancelAnimationFrame(this.animationFrameId);
          this.animationFrameId = null;
        }
      } else {
        if (!this.animationFrameId) {
          this.animationFrameId = requestAnimationFrame(updateAnimations);
        }
      }
    });
  }

  /**
   * Configurer l'IntersectionObserver pour le lazy loading
   */
  setupIntersectionObserver() {
    this.intersectionObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const element = entry.target;

          // Si l'élément a un attribut data-src, c'est une image à charger paresseusement
          if (element.hasAttribute('data-src')) {
            element.src = element.getAttribute('data-src');
            element.removeAttribute('data-src');
            this.intersectionObserver.unobserve(element);
          }

          // Si l'élément a une classe lazy-load, la remplacer par loaded
          if (element.classList.contains('lazy-load')) {
            element.classList.remove('lazy-load');
            element.classList.add('loaded');
          }
        }
      });
    }, {
      root: null,
      rootMargin: '100px',
      threshold: 0.1
    });

    // Observer les éléments avec la classe lazy-load
    document.querySelectorAll('.lazy-load, [data-src]').forEach(element => {
      this.intersectionObserver.observe(element);
    });
  }

  /**
   * Configurer le ResizeObserver pour les éléments responsifs
   */
  setupResizeObserver() {
    if ('ResizeObserver' in window) {
      this.resizeObserver = new ResizeObserver(entries => {
        for (const entry of entries) {
          // Mettre à jour les éléments qui dépendent de la taille
          if (entry.target === this.excelTable) {
            this.updateExcelLayout();
          }
        }
      });

      // Observer le tableau Excel
      if (this.excelTable) {
        this.resizeObserver.observe(this.excelTable);
      }
    }
  }

  /**
   * Optimiser les événements fréquents comme scroll et resize
   */
  setupEventOptimizations() {
    // Fonction de debounce pour limiter les appels fréquents
    const debounce = (func, delay, id) => {
      return (...args) => {
        if (this.debounceTimers[id]) {
          clearTimeout(this.debounceTimers[id]);
        }
        this.debounceTimers[id] = setTimeout(() => {
          func.apply(this, args);
          delete this.debounceTimers[id];
        }, delay);
      };
    };

    // Optimiser l'événement de redimensionnement de la fenêtre
    window.addEventListener('resize', debounce(() => {
      this.updateResponsiveLayout();
    }, 150, 'resize'));

    // Optimiser l'événement de défilement
    window.addEventListener('scroll', debounce(() => {
      this.updateScrollEffects();
    }, 50, 'scroll'), { passive: true });

    // Optimiser les événements tactiles
    if (this.excelTable) {
      this.excelTable.addEventListener('touchstart', (e) => {
        this.touchStartX = e.touches[0].clientX;
        this.touchStartY = e.touches[0].clientY;
      }, { passive: true });

      this.excelTable.addEventListener('touchmove', debounce((e) => {
        const touchX = e.touches[0].clientX;
        const touchY = e.touches[0].clientY;
        const diffX = this.touchStartX - touchX;
        const diffY = this.touchStartY - touchY;

        // Détecter le geste de balayage
        if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
          if (diffX > 0) {
            // Balayage vers la gauche
            this.showExcelScrollButtons('right');
          } else {
            // Balayage vers la droite
            this.showExcelScrollButtons('left');
          }
        }
      }, 50, 'touch'), { passive: true });
    }
  }

  /**
   * Mettre à jour les effets de défilement
   */
  updateScrollEffects() {
    // Mettre à jour les effets visuels basés sur le défilement
    const scrollPosition = window.scrollY;

    // Exemple: parallaxe ou effets d'apparition au défilement
    document.querySelectorAll('.scroll-effect').forEach(element => {
      const distanceFromTop = element.getBoundingClientRect().top;
      if (distanceFromTop < window.innerHeight * 0.8) {
        element.classList.add('visible');
      }
    });
  }

  /**
   * Mettre à jour la disposition responsive
   */
  updateResponsiveLayout() {
    // Mettre à jour la disposition en fonction de la taille de l'écran
    const isMobile = window.innerWidth < 768;

    // Ajuster les éléments pour le mobile
    if (isMobile) {
      // Ajustements pour mobile
    } else {
      // Ajustements pour desktop
    }

    // Mettre à jour la disposition du tableau Excel
    this.updateExcelLayout();
  }

  /**
   * Mettre à jour la disposition du tableau Excel
   */
  updateExcelLayout() {
    if (!this.excelTable) return;

    // Ajuster la taille des cellules en fonction de la taille du conteneur
    const containerWidth = this.excelTable.parentElement.clientWidth;
    const cellCount = this.excelTable.rows[0]?.cells.length || 20;

    // Calculer la taille optimale des cellules
    const optimalCellSize = Math.max(30, Math.min(50, Math.floor(containerWidth / cellCount)));

    // Mettre à jour le style des cellules si nécessaire
    if (optimalCellSize !== this.currentCellSize) {
      this.currentCellSize = optimalCellSize;

      // Appliquer la nouvelle taille aux cellules
      const cells = this.excelTable.querySelectorAll('td');
      cells.forEach(cell => {
        cell.style.width = `${optimalCellSize}px`;
        cell.style.height = `${optimalCellSize}px`;
      });
    }
  }

  /**
   * Afficher les boutons de défilement pour le tableau Excel
   */
  showExcelScrollButtons(direction) {
    // Créer les boutons de défilement s'ils n'existent pas
    if (!this.excelScrollLeftBtn) {
      this.excelScrollLeftBtn = document.createElement('button');
      this.excelScrollLeftBtn.className = 'excel-scroll-btn excel-scroll-left';
      this.excelScrollLeftBtn.innerHTML = '<i class="fas fa-chevron-left"></i>';
      this.excelScrollLeftBtn.setAttribute('aria-label', 'Faire défiler vers la gauche');

      this.excelScrollRightBtn = document.createElement('button');
      this.excelScrollRightBtn.className = 'excel-scroll-btn excel-scroll-right';
      this.excelScrollRightBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
      this.excelScrollRightBtn.setAttribute('aria-label', 'Faire défiler vers la droite');

      // Ajouter les boutons au conteneur
      const container = this.excelTable.parentElement;
      container.appendChild(this.excelScrollLeftBtn);
      container.appendChild(this.excelScrollRightBtn);

      // Ajouter les événements de défilement
      this.excelScrollLeftBtn.addEventListener('click', () => {
        container.scrollBy({ left: -100, behavior: this.prefersReducedMotion ? 'auto' : 'smooth' });
      });

      this.excelScrollRightBtn.addEventListener('click', () => {
        container.scrollBy({ left: 100, behavior: this.prefersReducedMotion ? 'auto' : 'smooth' });
      });
    }

    // Afficher le bouton approprié
    if (direction === 'left') {
      this.excelScrollLeftBtn.classList.add('visible');
      setTimeout(() => {
        this.excelScrollLeftBtn.classList.remove('visible');
      }, 2000);
    } else if (direction === 'right') {
      this.excelScrollRightBtn.classList.add('visible');
      setTimeout(() => {
        this.excelScrollRightBtn.classList.remove('visible');
      }, 2000);
    }
  }

  /**
   * Vérifier les préférences de l'utilisateur
   */
  checkUserPreferences() {
    // Vérifier si l'utilisateur préfère réduire les animations
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
    this.prefersReducedMotion = prefersReducedMotion.matches;

    // Écouter les changements de préférence
    prefersReducedMotion.addEventListener('change', (e) => {
      this.prefersReducedMotion = e.matches;
      this.updateAnimationPreferences();
    });

    // Vérifier si l'utilisateur préfère un thème sombre
    const prefersDarkMode = window.matchMedia('(prefers-color-scheme: dark)');
    const prefersDark = prefersDarkMode.matches;

    // Appliquer le thème préféré si aucun thème n'est déjà défini
    if (!localStorage.getItem('theme')) {
      document.documentElement.setAttribute('data-theme', prefersDark ? 'dark' : 'light');
    }

    // Écouter les changements de préférence de thème
    prefersDarkMode.addEventListener('change', (e) => {
      if (!localStorage.getItem('theme')) {
        document.documentElement.setAttribute('data-theme', e.matches ? 'dark' : 'light');
      }
    });
  }

  /**
   * Mettre à jour les préférences d'animation
   */
  updateAnimationPreferences() {
    if (this.prefersReducedMotion) {
      document.documentElement.setAttribute('data-motion', 'reduced');
    } else {
      document.documentElement.removeAttribute('data-motion');
    }
  }

  /**
   * Initialiser les éléments DOM et les propriétés
   */
  initialize() {
    // Utiliser un fragment de document pour optimiser les performances
    const getElement = (id) => document.getElementById(id);

    // Éléments DOM principaux - Regroupés pour une meilleure organisation
    this.elements = {
      // Écrans principaux
      screens: {
        intro: getElement("introduction-screen"),
        simulation: getElement("simulation-screen")
      },

      // Entrées et affichages
      inputs: {
        dividend: getElement("dividend-input"),
        divisor: getElement("divisor-input"),
        estimate: getElement("estimate-input"),
        mult: getElement("mult-input"),
        sub: getElement("sub-input")
      },

      // Prévisualisations et affichages
      displays: {
        dividendPreview: getElement("dividend-preview"),
        divisorPreview: getElement("divisor-preview"),
        dividendDisplay: getElement("dividend-display"),
        divisorDisplay: getElement("divisor-display"),
        remainderDisplay: getElement("remainder-display"),
        dividendSelection: getElement("dividend-selection"),
        currentDigit: getElement("current-digit"),
        finalFormula: getElement("final-formula"),
        finalCongrats: getElement("final-congrats")
      },

      // Étapes de la simulation
      stages: {
        selection: getElement("selection-stage"),
        quotient: getElement("quotient-stage"),
        multiplication: getElement("multiplication-stage"),
        subtraction: getElement("subtraction-stage"),
        lowering: getElement("lowering-stage"),
        finalSummary: getElement("final-summary")
      },

      // Boutons et contrôles
      buttons: {
        start: getElement("start-simulation"),
        addDigit: getElement("add-digit-btn"),
        submitEstimate: getElement("submit-estimate"),
        checkMult: getElement("check-mult"),
        nextToSub: getElement("next-to-sub"),
        checkSub: getElement("check-sub"),
        continueLowering: getElement("continue-lowering"),
        restart: getElement("restart"),
        btnRestart: getElement("btn-restart"),
        clearExcel: getElement("clear-excel"),
        zoomIn: getElement("zoom-in"),
        zoomOut: getElement("zoom-out")
      },

      // Feedback et labels
      feedback: {
        main: getElement("feedback"),
        mult: getElement("feedback-mult"),
        sub: getElement("feedback-sub")
      },

      // Labels d'opération
      labels: {
        multOperation: getElement("mult-operation-label"),
        subOperation: getElement("sub-operation-label")
      },

      // Éléments Excel
      excel: {
        table: getElement("excel-table"),
        body: getElement("excel-body"),
        zoomLevel: getElement("zoom-level"),
        operationCount: getElement("excel-operation-count"),
        lastOperation: getElement("excel-last-operation")
      }
    };

    // Assigner les références directes pour la compatibilité avec le code existant
    this.startButton = this.elements.buttons.start;
    this.introScreen = this.elements.screens.intro;
    this.simulationScreen = this.elements.screens.simulation;
    this.dividendInput = this.elements.inputs.dividend;
    this.divisorInput = this.elements.inputs.divisor;
    this.dividendPreview = this.elements.displays.dividendPreview;
    this.divisorPreview = this.elements.displays.divisorPreview;
    this.dividendDisplay = this.elements.displays.dividendDisplay;
    this.divisorDisplay = this.elements.displays.divisorDisplay;
    this.remainderDisplay = this.elements.displays.remainderDisplay;

    this.selectionStage = this.elements.stages.selection;
    this.quotientStage = this.elements.stages.quotient;
    this.multiplicationStage = this.elements.stages.multiplication;
    this.subtractionStage = this.elements.stages.subtraction;
    this.loweringStage = this.elements.stages.lowering;
    this.finalSummary = this.elements.stages.finalSummary;

    this.dividendSelection = this.elements.displays.dividendSelection;
    this.addDigitBtn = this.elements.buttons.addDigit;
    this.estimateInput = this.elements.inputs.estimate;
    this.submitEstimateBtn = this.elements.buttons.submitEstimate;
    this.feedback = this.elements.feedback.main;
    this.multOperationLabel = this.elements.labels.multOperation;
    this.multInput = this.elements.inputs.mult;
    this.checkMultBtn = this.elements.buttons.checkMult;
    this.feedbackMult = this.elements.feedback.mult;
    this.nextToSubBtn = this.elements.buttons.nextToSub;
    this.subOperationLabel = this.elements.labels.subOperation;
    this.subInput = this.elements.inputs.sub;
    this.checkSubBtn = this.elements.buttons.checkSub;
    this.feedbackSub = this.elements.feedback.sub;
    this.currentDigitSpan = this.elements.displays.currentDigit;
    this.continueLoweringBtn = this.elements.buttons.continueLowering;
    this.finalFormula = this.elements.displays.finalFormula;
    this.finalCongrats = this.elements.displays.finalCongrats;
    this.restartBtn = this.elements.buttons.restart;
    this.btnRestart = this.elements.buttons.btnRestart;

    this.excelTable = this.elements.excel.table;
    this.excelBody = this.elements.excel.body;
    this.clearExcelBtn = this.elements.buttons.clearExcel;
    this.zoomInBtn = this.elements.buttons.zoomIn;
    this.zoomOutBtn = this.elements.buttons.zoomOut;
    this.zoomLevel = this.elements.excel.zoomLevel;
    this.excelOperationCount = this.elements.excel.operationCount;
    this.excelLastOperation = this.elements.excel.lastOperation;

    // Initialiser la prévisualisation avec des écouteurs d'événements optimisés
    const updatePreviewDebounced = this.debounce(() => this.updatePreview(), 100, 'preview');
    this.dividendInput.addEventListener("input", updatePreviewDebounced);
    this.divisorInput.addEventListener("input", updatePreviewDebounced);

    // Initialiser le zoom Excel
    this.currentZoom = 100;
    this.updateZoomLevel();

    // Initialiser les propriétés de performance
    this.currentCellSize = 40; // Taille par défaut des cellules Excel

    // Initialiser les propriétés d'accessibilité
    this.setupAccessibilityFeatures();
  }

  /**
   * Fonction utilitaire de debounce
   */
  debounce(func, delay, id) {
    return (...args) => {
      if (this.debounceTimers[id]) {
        clearTimeout(this.debounceTimers[id]);
      }
      this.debounceTimers[id] = setTimeout(() => {
        func.apply(this, args);
        delete this.debounceTimers[id];
      }, delay);
    };
  }

  /**
   * Configurer les fonctionnalités d'accessibilité
   */
  setupAccessibilityFeatures() {
    // Ajouter des attributs ARIA pour améliorer l'accessibilité
    if (this.excelTable) {
      this.excelTable.setAttribute('role', 'grid');
      this.excelTable.setAttribute('aria-label', 'Tableau de simulation de division euclidienne');
    }

    // Ajouter des descriptions pour les écrans lecteurs
    const addAriaDescription = (element, description) => {
      if (element) {
        if (!element.hasAttribute('aria-describedby')) {
          const id = `desc-${Math.random().toString(36).substring(2, 9)}`;
          const descElement = document.createElement('span');
          descElement.id = id;
          descElement.className = 'visually-hidden';
          descElement.textContent = description;
          element.parentNode.insertBefore(descElement, element.nextSibling);
          element.setAttribute('aria-describedby', id);
        }
      }
    };

    // Ajouter des descriptions pour les éléments importants
    addAriaDescription(this.dividendInput, 'Entrez le nombre à diviser (dividende)');
    addAriaDescription(this.divisorInput, 'Entrez le nombre diviseur');
    addAriaDescription(this.estimateInput, 'Estimez combien de fois le diviseur est contenu dans le dividende');
  }

  setupEvents() {
    // Événement de démarrage
    this.startButton.addEventListener("click", () => this.startSimulation());

    // Événement pour générer des nombres aléatoires
    document.getElementById("generate-numbers").addEventListener("click", () => this.generateRandomNumbers());

    // Événements des étapes
    this.addDigitBtn.addEventListener("click", () => this.addMoreDigits());
    this.submitEstimateBtn.addEventListener("click", () => this.checkEstimate());
    this.checkMultBtn.addEventListener("click", () => this.checkMultiplication());
    this.nextToSubBtn.addEventListener("click", () => this.goToSubtractionStage());
    this.checkSubBtn.addEventListener("click", () => this.checkSubtraction());
    this.continueLoweringBtn.addEventListener("click", () => this.proceedAfterLowering());
    this.restartBtn.addEventListener("click", () => this.resetSimulation());
    this.btnRestart.addEventListener("click", () => this.resetSimulation());

    // Événements Excel
    this.clearExcelBtn.addEventListener("click", () => this.clearExcel());
    this.zoomInBtn.addEventListener("click", () => this.zoomIn());
    this.zoomOutBtn.addEventListener("click", () => this.zoomOut());

    // Nouveaux événements Excel
    document.getElementById("reset-zoom").addEventListener("click", () => this.resetZoom());
    document.getElementById("help-excel").addEventListener("click", () => this.showExcelHelp());
    document.getElementById("close-help").addEventListener("click", () => this.hideExcelHelp());
    document.getElementById("toggle-grid").addEventListener("click", () => this.toggleExcelGrid());
    document.getElementById("toggle-highlight").addEventListener("click", () => this.toggleExcelHighlight());
    document.getElementById("export-excel").addEventListener("click", () => this.exportExcelAsImage());
    document.getElementById("print-excel").addEventListener("click", () => this.printExcelTable());
    document.getElementById("fullscreen-excel").addEventListener("click", () => this.toggleExcelFullscreen());

    // Événement pour le mode d'affichage
    document.getElementById("excel-view-mode").addEventListener("change", (e) => this.changeExcelViewMode(e.target.value));

    // Événements pour les inputs
    this.estimateInput.addEventListener("keypress", (e) => {
      if (e.key === "Enter") this.checkEstimate();
    });

    this.multInput.addEventListener("keypress", (e) => {
      if (e.key === "Enter") this.checkMultiplication();
    });

    this.subInput.addEventListener("keypress", (e) => {
      if (e.key === "Enter") this.checkSubtraction();
    });
  }

  updatePreview() {
    const dividend = this.dividendInput.value;
    const divisor = this.divisorInput.value;

    this.dividendPreview.textContent = dividend || "";
    this.divisorPreview.textContent = divisor || "";
  }

  /**
   * Génère des nombres aléatoires adaptés au type de division et au niveau de difficulté sélectionnés
   */
  generateRandomNumbers() {
    // Récupérer le type de division sélectionné
    let divisionType = 'remainder';
    const divisionTypeRadios = document.getElementsByName('division-type');
    for (const radio of divisionTypeRadios) {
      if (radio.checked) {
        divisionType = radio.value;
        break;
      }
    }

    // Récupérer le niveau de difficulté sélectionné
    let difficulty = 'easy';
    const difficultyRadios = document.getElementsByName('difficulty');
    for (const radio of difficultyRadios) {
      if (radio.checked) {
        difficulty = radio.value;
        break;
      }
    }

    // Définir les plages de nombres en fonction du niveau de difficulté
    let divisorRange, dividendRange, multiplierRange;

    switch (difficulty) {
      case 'very-easy':
        divisorRange = { min: 2, max: 5 };
        dividendRange = { min: 5, max: 30 };
        multiplierRange = { min: 2, max: 6 };
        break;

      case 'easy':
        divisorRange = { min: 2, max: 10 };
        dividendRange = { min: 10, max: 100 };
        multiplierRange = { min: 2, max: 10 };
        break;

      case 'medium':
        divisorRange = { min: 5, max: 20 };
        dividendRange = { min: 50, max: 500 };
        multiplierRange = { min: 5, max: 30 };
        break;

      case 'hard':
        divisorRange = { min: 10, max: 50 };
        dividendRange = { min: 100, max: 2000 };
        multiplierRange = { min: 10, max: 100 };
        break;

      case 'expert':
        divisorRange = { min: 20, max: 100 };
        dividendRange = { min: 500, max: 10000 };
        multiplierRange = { min: 20, max: 200 };
        break;
    }

    let dividend, divisor;

    switch (divisionType) {
      case 'exact':
        // Pour une division exacte, générer un diviseur et un multiple de ce diviseur
        divisor = this.getRandomInt(divisorRange.min, divisorRange.max);
        const multiplier = this.getRandomInt(multiplierRange.min, multiplierRange.max);
        dividend = divisor * multiplier;
        break;

      case 'decimal':
        // Pour une division décimale, générer des nombres qui donnent un résultat décimal intéressant
        divisor = this.getRandomInt(divisorRange.min, divisorRange.max);
        // Générer un dividende qui n'est pas un multiple exact du diviseur
        do {
          dividend = this.getRandomInt(dividendRange.min, dividendRange.max);
        } while (dividend % divisor === 0);
        break;

      case 'fraction':
        // Pour une division fractionnaire, générer des nombres adaptés
        if (difficulty === 'very-easy' || difficulty === 'easy') {
          // Fraction propre (numérateur < dénominateur)
          divisor = this.getRandomInt(divisorRange.min, divisorRange.max);
          dividend = this.getRandomInt(1, divisor - 1);
        } else if (difficulty === 'medium') {
          // Mélange de fractions propres et impropres
          divisor = this.getRandomInt(divisorRange.min, divisorRange.max);
          dividend = this.getRandomInt(1, divisor * 2);
        } else {
          // Fractions impropres (numérateur > dénominateur)
          divisor = this.getRandomInt(divisorRange.min, divisorRange.max);
          dividend = this.getRandomInt(divisor, divisor * 3);
        }
        break;

      case 'grouping':
        // Pour la division par groupement, générer des nombres adaptés
        // Le diviseur doit être petit pour faciliter la visualisation des groupes
        divisor = this.getRandomInt(2, Math.min(10, divisorRange.max));

        if (difficulty === 'very-easy') {
          // Division exacte pour les débutants
          const groupCount = this.getRandomInt(2, 5);
          dividend = divisor * groupCount;
        } else if (difficulty === 'easy' || difficulty === 'medium') {
          // Mélange de divisions exactes et avec reste
          const groupCount = this.getRandomInt(2, 10);
          dividend = divisor * groupCount;

          // 50% de chance d'ajouter un reste
          if (Math.random() > 0.5) {
            dividend += this.getRandomInt(1, divisor - 1);
          }
        } else {
          // Divisions avec reste pour les niveaux avancés
          const groupCount = this.getRandomInt(5, 20);
          dividend = divisor * groupCount + this.getRandomInt(1, divisor - 1);
        }
        break;

      case 'remainder':
      default:
        // Pour une division avec reste, générer des nombres quelconques
        dividend = this.getRandomInt(dividendRange.min, dividendRange.max);
        divisor = this.getRandomInt(divisorRange.min, divisorRange.max);

        // Pour le niveau difficile, s'assurer que le reste est non nul
        if (difficulty === 'hard' || difficulty === 'expert') {
          // S'assurer que le dividende n'est pas un multiple du diviseur
          if (dividend % divisor === 0) {
            dividend += this.getRandomInt(1, divisor - 1);
          }
        }
        break;
    }

    // Mettre à jour les champs de saisie
    this.dividendInput.value = dividend;
    this.divisorInput.value = divisor;

    // Mettre à jour la prévisualisation
    this.updatePreview();

    // Afficher un message de confirmation
    this.showToast(`Nombres générés pour une division ${this.getDivisionTypeName(divisionType)} (niveau ${this.getDifficultyName(difficulty)})`, "info");
  }

  /**
   * Génère un nombre entier aléatoire entre min et max inclus
   * @param {number} min - Valeur minimale
   * @param {number} max - Valeur maximale
   * @returns {number} - Nombre aléatoire
   */
  getRandomInt(min, max) {
    min = Math.ceil(min);
    max = Math.floor(max);
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  /**
   * Retourne le nom du type de division en français
   * @param {string} type - Type de division ('remainder', 'exact', 'decimal', 'fraction', 'grouping')
   * @returns {string} - Nom du type de division en français
   */
  getDivisionTypeName(type) {
    switch (type) {
      case 'exact':
        return 'exacte';
      case 'decimal':
        return 'décimale';
      case 'fraction':
        return 'fractionnaire';
      case 'grouping':
        return 'par groupement';
      case 'remainder':
      default:
        return 'avec reste';
    }
  }

  /**
   * Retourne le nom du niveau de difficulté en français
   * @param {string} difficulty - Niveau de difficulté ('very-easy', 'easy', 'medium', 'hard', 'expert')
   * @returns {string} - Nom du niveau de difficulté en français
   */
  getDifficultyName(difficulty) {
    switch (difficulty) {
      case 'very-easy':
        return 'très facile';
      case 'easy':
        return 'facile';
      case 'medium':
        return 'moyen';
      case 'hard':
        return 'difficile';
      case 'expert':
        return 'expert';
      default:
        return 'inconnu';
    }
  }

  /**
   * Calcule le Plus Grand Commun Diviseur (PGCD) de deux nombres
   * @param {number} a - Premier nombre
   * @param {number} b - Deuxième nombre
   * @returns {number} - PGCD des deux nombres
   */
  findGCD(a, b) {
    // Algorithme d'Euclide
    a = Math.abs(a);
    b = Math.abs(b);

    while (b !== 0) {
      const temp = b;
      b = a % b;
      a = temp;
    }

    return a;
  }

  startSimulation() {
    // Récupérer et valider les entrées
    const dividend = this.dividendInput.value.trim();
    const divisor = this.divisorInput.value.trim();

    if (!dividend || !divisor || isNaN(dividend) || isNaN(divisor) || parseInt(divisor) <= 0) {
      this.showToast("Veuillez entrer des nombres valides. Le diviseur doit être supérieur à 0.", "error");
      return;
    }

    // Récupérer le type de division sélectionné
    const divisionTypeRadios = document.getElementsByName('division-type');
    for (const radio of divisionTypeRadios) {
      if (radio.checked) {
        this.divisionType = radio.value;
        break;
      }
    }

    // Initialiser les valeurs
    this.dividend = parseInt(dividend);
    this.divisor = parseInt(divisor);
    this.quotient = 0;
    this.remainder = 0;
    this.digits = dividend.split("").map(d => parseInt(d));
    this.index = 0;
    this.currentRemainder = 0;
    this.operationCount = 0;

    // Vérifier si la division est exacte pour le type 'exact'
    if (this.divisionType === 'exact') {
      if (this.dividend % this.divisor !== 0) {
        this.showToast("Cette division n'est pas exacte. Veuillez choisir un autre type de division ou changer les nombres.", "error");
        return;
      }
    }

    // Traitement spécifique selon le type de division
    switch (this.divisionType) {
      case 'decimal':
        // Pour la division décimale, ajouter des zéros pour les décimales
        for (let i = 0; i < this.decimalPlaces; i++) {
          this.digits.push(0);
        }
        break;

      case 'fraction':
        // Pour la division fractionnaire, vérifier que les nombres sont adaptés
        if (this.dividend < this.divisor) {
          // C'est une fraction propre, c'est parfait
        } else {
          // C'est une fraction impropre, on peut continuer mais on informe l'utilisateur
          this.showToast("Cette division donnera une fraction impropre (numérateur > dénominateur).", "info");
        }
        break;

      case 'grouping':
        // Pour la division par groupement, vérifier que le diviseur est adapté
        if (this.divisor > 10) {
          this.showToast("Pour la division par groupement, il est recommandé d'utiliser un diviseur plus petit (≤ 10).", "info");
        }
        break;
    }

    // Mettre à jour l'affichage
    this.dividendDisplay.textContent = this.dividend;
    this.divisorDisplay.textContent = this.divisor;
    this.remainderDisplay.textContent = "0";

    // Afficher l'écran de simulation
    this.introScreen.classList.add("hidden");
    this.simulationScreen.classList.remove("hidden");

    // Générer le tableau Excel
    this.generateExcelTable();

    // Commencer la première étape
    this.startSelectionStage();
  }

  generateExcelTable() {
    // Vider le tableau existant
    while (this.excelBody.firstChild) {
      this.excelBody.removeChild(this.excelBody.firstChild);
    }

    // Créer l'en-tête avec des lettres
    const thead = this.excelTable.querySelector("thead tr");
    thead.innerHTML = "";
    for (let i = 0; i < 20; i++) {
      const th = document.createElement("th");
      th.textContent = String.fromCharCode(65 + i); // A, B, C, ...
      thead.appendChild(th);
    }

    // Créer les lignes du tableau
    for (let i = 0; i < 20; i++) {
      const row = document.createElement("tr");
      for (let j = 0; j < 20; j++) {
        const cell = document.createElement("td");
        cell.setAttribute("data-row", i);
        cell.setAttribute("data-col", j);
        row.appendChild(cell);
      }
      this.excelBody.appendChild(row);
    }

    // Initialiser le statut Excel
    this.updateExcelStatus("Tableau initialisé", 0);

    // Initialiser les positions pour les opérations
    this.initializeExcelPositions();
  }

  initializeExcelPositions() {
    // Position de départ pour les opérations (ligne 2, colonne 3)
    const startRow = 2;
    const startCol = 3;

    // Initialiser les propriétés pour suivre la position actuelle dans Excel
    this.excelCurrentRow = startRow; // Commencer les opérations à cette ligne
    this.excelDividendCol = startCol;
    this.excelDivisorCol = startCol + 8; // Position pour le diviseur

    // Créer la structure visuelle de la division euclidienne
    this.createDivisionStructure();
  }

  createDivisionStructure() {
    // Créer la structure visuelle de la division euclidienne avec le dividende à gauche et le diviseur à droite

    // Position initiale
    const startRow = this.excelCurrentRow;
    const startCol = 3; // Commencer à la colonne C

    // Position du dividende (à gauche)
    const dividendRow = startRow;
    const dividendCol = startCol;
    const dividendDigits = this.dividend.toString().split("");

    // Placer chaque chiffre du dividende dans une cellule individuelle
    for (let i = 0; i < dividendDigits.length; i++) {
      const cell = this.getExcelCell(dividendRow, dividendCol + i);
      if (cell) {
        cell.innerHTML = `<span class="dividend-value">${dividendDigits[i]}</span>`;
        cell.style.textAlign = "center";
        cell.style.fontWeight = "bold";
        cell.style.color = "var(--primary-color)";
      }
    }

    // Position de la barre de division (juste après le dividende)
    const barStartCol = dividendCol + dividendDigits.length + 1;

    // Ligne verticale de la division
    const verticalLineCell = this.getExcelCell(dividendRow, barStartCol);
    if (verticalLineCell) {
      verticalLineCell.style.borderRight = "2px solid var(--primary-color)";
      verticalLineCell.style.height = "100%";
      verticalLineCell.rowSpan = 15; // Assez grand pour couvrir toutes les opérations
    }

    // Position du diviseur (à droite de la barre)
    const divisorRow = dividendRow;
    const divisorStartCol = barStartCol + 1;
    const divisorDigits = this.divisor.toString().split("");
    const barEndCol = divisorStartCol + divisorDigits.length - 1;

    // Ligne horizontale de la division (au-dessus du diviseur)
    for (let i = divisorStartCol; i <= barEndCol; i++) {
      const horizontalLineCell = this.getExcelCell(divisorRow, i);
      if (horizontalLineCell) {
        horizontalLineCell.style.borderBottom = "2px solid var(--primary-color)";
      }
    }

    // Placer le diviseur - chaque chiffre dans une cellule individuelle
    for (let i = 0; i < divisorDigits.length; i++) {
      const cell = this.getExcelCell(divisorRow, divisorStartCol + i);
      if (cell) {
        cell.innerHTML = `<span class="divisor-value">${divisorDigits[i]}</span>`;
        cell.style.textAlign = "center";
        cell.style.fontWeight = "bold";
        cell.style.color = "var(--danger-color)";

        // Ajouter un marqueur visuel sous le premier chiffre du diviseur
        if (i === 0) {
          cell.style.borderBottom = "2px dashed var(--success-color)";
        }
      }
    }

    // Réserver un espace pour le quotient (en dessous du diviseur)
    const quotientRow = divisorRow + 1;

    // Stocker la position du premier chiffre du diviseur pour le quotient
    this.divisorStartCol = divisorStartCol;

    // MODIFICATION: Placer un marqueur visuel pour le premier chiffre du quotient
    // Décalé d'une colonne vers la gauche par rapport au premier chiffre du diviseur
    const firstQuotientCell = this.getExcelCell(quotientRow, divisorStartCol - 1);
    if (firstQuotientCell) {
      firstQuotientCell.innerHTML = `<span style="color:var(--success-color);opacity:0.5;">•</span>`;
      firstQuotientCell.style.textAlign = "center";
      firstQuotientCell.style.borderTop = "2px dashed var(--success-color)";

      // Ajouter un marqueur visuel pour indiquer clairement la position
      firstQuotientCell.style.backgroundColor = "rgba(46, 204, 113, 0.1)";
    }

    // Initialiser les cellules pour le quotient
    for (let i = 1; i < divisorDigits.length; i++) {
      const cell = this.getExcelCell(quotientRow, divisorStartCol + i);
      if (cell) {
        cell.style.textAlign = "center";
        cell.style.borderTop = "1px dashed var(--border-color)";
      }
    }

    // MODIFICATION: Mettre à jour les positions pour les opérations futures
    // Commencer à la ligne du dividende pour que la multiplication soit directement en dessous
    this.excelCurrentRow = dividendRow + 1;
    this.excelDividendCol = dividendCol;
    this.dividendStartCol = dividendCol;

    // Stocker les informations importantes pour l'alignement
    this.dividendLength = dividendDigits.length;
    this.divisorLength = divisorDigits.length;
    this.divisorRow = divisorRow;
    this.quotientRow = quotientRow;
  }

  getExcelCell(row, col) {
    const rowElement = this.excelBody.children[row];
    if (rowElement) {
      return rowElement.children[col];
    }
    return null;
  }

  startSelectionStage() {
    // Afficher l'étape de sélection
    this.hideAllStages();
    this.selectionStage.classList.remove("hidden");

    // Déterminer le nombre initial à sélectionner
    let selectedDigits = [];
    let i = 0;

    // Trouver le plus petit nombre de chiffres qui est >= diviseur
    do {
      selectedDigits.push(this.digits[i]);
      this.currentRemainder = parseInt(selectedDigits.join(""));
      i++;
    } while (i < this.digits.length && this.currentRemainder < this.divisor);

    this.index = i - 1;

    // Mettre à jour l'affichage
    this.updateDigitSelection();
    this.remainderDisplay.textContent = this.currentRemainder;

    // Passer à l'étape du quotient
    setTimeout(() => {
      this.goToQuotientStage();
    }, 1000);
  }

  updateDigitSelection() {
    this.dividendSelection.innerHTML = "";
    for (let i = 0; i <= this.index; i++) {
      const digitSpan = document.createElement("span");
      digitSpan.textContent = this.digits[i];
      digitSpan.classList.add("selected-digit");
      this.dividendSelection.appendChild(digitSpan);
    }
  }

  goToQuotientStage() {
    // Afficher l'étape du quotient
    this.hideAllStages();
    this.quotientStage.classList.remove("hidden");

    // Mettre à jour les consignes
    document.getElementById("consigne-dividend").textContent = this.currentRemainder;
    document.getElementById("consigne-divisor").textContent = this.divisor;

    // Vider et focus sur l'input
    this.estimateInput.value = "";
    this.feedback.textContent = "";
    this.estimateInput.focus();
  }

  checkEstimate() {
    const val = this.estimateInput.value;
    const estimate = Number(val);

    // Vérification stricte de la saisie
    if (val === "" || isNaN(estimate) || estimate < 0) {
      this.feedback.textContent = "Saisis un nombre positif.";
      this.feedback.style.color = "red";
      return;
    }

    // Calcul du bon quotient possible
    const correctQuotient = Math.floor(this.currentRemainder / this.divisor);
    this.currentEstimate = estimate;

    // Feedback personnalisé
    if (estimate === correctQuotient) {
      this.feedback.textContent = "Bonne réponse !";
      this.feedback.style.color = "green";

      // Ajouter au quotient
      this.quotient = this.quotient * 10 + estimate;

      // Ajouter l'estimation dans Excel
      this.addQuotientDigitToExcel(estimate);

      // Mettre à jour les labels d'opération
      this.multOperationLabel.textContent = `${this.divisor} × ${this.currentEstimate} = ?`;
      const expectedProduct = this.divisor * this.currentEstimate;
      this.subOperationLabel.textContent = `${this.currentRemainder} - ${expectedProduct} = ?`;

      // Passer à l'étape de multiplication
      this.goToMultiplicationStage();
    } else if (estimate > correctQuotient) {
      this.feedback.textContent = `Ta réponse est trop grande. Le quotient doit être inférieur ou égal à ${correctQuotient}.`;
      this.feedback.style.color = "red";
    } else {
      this.feedback.textContent = "Ta réponse est trop petite.";
      this.feedback.style.color = "red";
    }
  }

  addQuotientDigitToExcel(digit) {
    // Utiliser la ligne du quotient stockée
    const quotientRow = this.quotientRow;

    // MODIFICATION: Décaler le quotient d'une colonne vers la gauche
    let col;

    if (this.quotient < 10) {
      // Premier chiffre du quotient - le placer sous le premier chiffre du diviseur, décalé d'une colonne vers la gauche
      col = this.divisorStartCol - 1; // Position du premier chiffre du diviseur, décalée d'une colonne vers la gauche
    } else {
      // Chiffres suivants - les décaler vers la droite
      const quotientDigits = Math.floor(this.quotient / 10).toString().length;
      col = this.divisorStartCol - 1 + quotientDigits;
    }

    // Renforcer le marqueur visuel pour le premier chiffre du diviseur
    const firstDigitCell = this.getExcelCell(this.divisorRow, this.divisorStartCol);
    if (firstDigitCell) {
      firstDigitCell.style.borderBottom = "2px dashed var(--success-color)";
    }

    // Placer le chiffre du quotient
    const cell = this.getExcelCell(quotientRow, col);
    if (cell) {
      // Remplacer le marqueur visuel par le chiffre du quotient
      cell.innerHTML = `<span class="quotient-cell new-operation">${digit}</span>`;
      cell.style.textAlign = "center";
      cell.style.fontWeight = "bold";

      // Si c'est le premier chiffre du quotient, ajouter une bordure spéciale
      if (this.quotient < 10) {
        cell.style.borderTop = "2px dashed var(--success-color)";
      }

      // Mettre en évidence la colonne active
      for (let i = quotientRow; i <= this.divisorRow + 10; i++) {
        const highlightCell = this.getExcelCell(i, col);
        if (highlightCell) {
          highlightCell.style.backgroundColor = "rgba(46, 204, 113, 0.05)";
        }
      }
    }

    // Mettre à jour le statut Excel
    this.updateExcelStatus(`Quotient: ${digit}`, 1);
  }

  goToMultiplicationStage() {
    // Afficher l'étape de multiplication
    this.hideAllStages();
    this.multiplicationStage.classList.remove("hidden");

    // Vider et focus sur l'input
    this.multInput.value = "";
    this.feedbackMult.textContent = "";
    this.nextToSubBtn.classList.add("hidden");
    this.multInput.focus();
  }

  checkMultiplication() {
    const expectedProduct = this.divisor * this.currentEstimate;
    const userProduct = +this.multInput.value;

    if (userProduct !== expectedProduct) {
      this.feedbackMult.textContent = `❌ Produit incorrect. ${this.divisor} - ${this.currentEstimate} = ${expectedProduct}`;
      return;
    }

    this.feedbackMult.textContent = "✔️ Multiplication correcte !";
    this.nextToSubBtn.classList.remove("hidden");

    // Ajouter la multiplication dans Excel
    this.addMultiplicationToExcel(expectedProduct);
  }

  addMultiplicationToExcel(product) {
    // Position pour l'opération de multiplication
    const row = this.excelCurrentRow; // Utiliser la ligne actuelle (déjà ajustée)

    // Calculer la position de départ en fonction du tour actuel
    const productDigits = product.toString().split("");

    // Déterminer la position de départ pour la multiplication
    let startCol;

    // Calculer le tour actuel basé sur le quotient
    // Si quotient < 10, c'est le premier tour
    // Si 10 <= quotient < 100, c'est le deuxième tour
    // Si 100 <= quotient < 1000, c'est le troisième tour, etc.
    const currentTour = Math.floor(Math.log10(Math.max(1, this.quotient))) + 1;

    if (currentTour === 1) {
      // Premier tour - aligner le produit sous les premiers chiffres du dividende
      // Pour l'exemple 28469 ÷ 123, le produit 246 doit être aligné sous 284
      startCol = this.dividendStartCol;
    } else {
      // Tours suivants - chaque tour est décalé d'une cellule vers la droite par rapport au tour précédent
      // Pour l'exemple 28469 ÷ 123:
      // - Premier tour: 246 aligné sous 284 (startCol = this.dividendStartCol)
      // - Deuxième tour: 369 aligné sous 386 (startCol = this.dividendStartCol + 1)
      // - Troisième tour: 123 aligné sous 179 (startCol = this.dividendStartCol + 2)

      // Décaler d'une cellule vers la droite pour chaque tour après le premier
      startCol = this.dividendStartCol + (currentTour - 1);
    }

    // Ajouter le produit avec un alignement correct - chaque chiffre dans une cellule
    for (let i = 0; i < productDigits.length; i++) {
      const digitCol = startCol + i;
      const cell = this.getExcelCell(row, digitCol);
      if (cell) {
        cell.innerHTML = `<span class="multiplication-cell new-operation">${productDigits[i]}</span>`;
        cell.style.textAlign = "center";
        cell.style.borderBottom = "1px solid var(--danger-color)";
      }
    }

    // Ajouter un signe de soustraction (au lieu de multiplication)
    const multCell = this.getExcelCell(row, startCol - 1);
    if (multCell) {
      multCell.innerHTML = `<span style="color:var(--danger-color);font-weight:bold;">-</span>`;
      multCell.style.textAlign = "center";
      multCell.style.verticalAlign = "middle";
    }

    // Mettre à jour le statut Excel
    this.updateExcelStatus(`Multiplication: ${this.divisor} - ${this.currentEstimate} = ${product}`, 1);

    // Incrémenter la ligne courante pour la prochaine opération
    this.excelCurrentRow++;
  }

  goToSubtractionStage() {
    // Afficher l'étape de soustraction
    this.hideAllStages();
    this.subtractionStage.classList.remove("hidden");

    // Vider et focus sur l'input
    this.subInput.value = "";
    this.feedbackSub.textContent = "";
    this.subInput.focus();
  }

  checkSubtraction() {
    const expectedProduct = this.divisor * this.currentEstimate;
    const expectedRemainder = this.currentRemainder - expectedProduct;
    const userRemainder = +this.subInput.value;

    if (userRemainder !== expectedRemainder) {
      this.feedbackSub.textContent = `❌ Soustraction incorrecte. ${this.currentRemainder} - ${expectedProduct} = ${expectedRemainder}`;
      return;
    }

    this.feedbackSub.textContent = "✔️ Soustraction correcte !";
    this.currentRemainder = expectedRemainder;
    this.remainderDisplay.textContent = this.currentRemainder;

    // Ajouter la soustraction dans Excel
    this.addSubtractionToExcel(expectedRemainder);

    // Vérifier s'il faut abaisser un chiffre ou terminer
    if (this.index < this.digits.length - 1) {
      this.goToLoweringStage();
    } else {
      this.remainder = this.currentRemainder;

      // Pour la division exacte, vérifier que le reste est bien 0
      if (this.divisionType === 'exact' && this.remainder !== 0) {
        this.showToast("Erreur: le reste devrait être 0 pour une division exacte.", "error");
      }

      this.endDivision();
    }
  }

  addSubtractionToExcel(remainder) {
    // Position pour le résultat de la soustraction
    const row = this.excelCurrentRow;

    // Récupérer les positions des chiffres du produit
    const productDigits = (this.divisor * this.currentEstimate).toString().split("");

    // Calculer la position de départ en fonction du tour actuel
    // Utiliser exactement la même logique que dans addMultiplicationToExcel pour assurer l'alignement
    let startCol;

    // Calculer le tour actuel basé sur le quotient
    const currentTour = Math.floor(Math.log10(Math.max(1, this.quotient))) + 1;

    if (currentTour === 1) {
      // Premier tour - aligner la soustraction sous les premiers chiffres du dividende
      // Pour l'exemple 28469 ÷ 123, la soustraction doit être alignée sous 284
      startCol = this.dividendStartCol;
    } else {
      // Tours suivants - chaque tour est décalé d'une cellule vers la droite par rapport au tour précédent
      // Pour l'exemple 28469 ÷ 123:
      // - Premier tour: soustraction alignée sous 284 (startCol = this.dividendStartCol)
      // - Deuxième tour: soustraction alignée sous 386 (startCol = this.dividendStartCol + 1)
      // - Troisième tour: soustraction alignée sous 179 (startCol = this.dividendStartCol + 2)

      // Décaler d'une cellule vers la droite pour chaque tour après le premier
      startCol = this.dividendStartCol + (currentTour - 1);
    }

    const productLength = productDigits.length;

    // Ajouter une ligne horizontale pour la soustraction
    for (let i = 0; i < productLength; i++) {
      const lineCol = startCol + i;
      const lineCell = this.getExcelCell(row - 0.5, lineCol);
      if (lineCell) {
        lineCell.style.borderBottom = "1px solid var(--primary-color)";
      }
    }

    // Ajouter un signe moins pour la soustraction
    const minusCell = this.getExcelCell(row - 1, startCol - 1);
    if (minusCell) {
      minusCell.innerHTML = `<span style="color:var(--danger-color);font-weight:bold;">−</span>`;
      minusCell.style.textAlign = "center";
      minusCell.style.verticalAlign = "middle";
    }

    // Afficher le reste aligné sous le produit - chaque chiffre dans une cellule
    const remainderDigits = remainder.toString().split("");

    // Calculer la position de départ pour le reste
    // Le reste doit être aligné avec les derniers chiffres du produit
    let remainderStartCol;

    // Calculer la position en fonction de l'alignement du produit
    // Pour assurer un alignement parfait, nous utilisons la position du produit
    // et ajustons en fonction de la longueur du reste
    remainderStartCol = startCol + productDigits.length - remainderDigits.length;

    // Ajustement pour les cas spéciaux
    if (remainderDigits.length === 1 && productDigits.length > 1) {
      // Si le reste n'a qu'un seul chiffre et le produit en a plusieurs,
      // placer le reste sous le dernier chiffre du produit
      remainderStartCol = startCol + productDigits.length - 1;
    }

    // Ajouter le reste avec un alignement correct
    for (let i = 0; i < remainderDigits.length; i++) {
      const digitCol = remainderStartCol + i;
      const cell = this.getExcelCell(row, digitCol);
      if (cell) {
        cell.innerHTML = `<span class="subtraction-cell new-operation">${remainderDigits[i]}</span>`;
        cell.style.textAlign = "center";
      }
    }

    // Mettre à jour la ligne courante pour la prochaine opération
    this.excelCurrentRow = row + 1;

    // Mettre à jour le statut Excel
    this.updateExcelStatus(`Soustraction: reste = ${remainder}`, 1);
  }

  goToLoweringStage() {
    // Afficher l'étape d'abaissement
    this.hideAllStages();
    this.loweringStage.classList.remove("hidden");

    // Afficher le chiffre à abaisser
    const nextDigit = this.digits[this.index + 1];
    this.currentDigitSpan.textContent = nextDigit;
  }

  proceedAfterLowering() {
    // Abaisser le chiffre suivant
    this.index++;
    const nextDigit = this.digits[this.index];

    // Combiner avec le reste actuel
    this.currentRemainder = parseInt(this.currentRemainder.toString() + nextDigit);
    this.remainderDisplay.textContent = this.currentRemainder;

    // Ajouter l'abaissement dans Excel
    this.addLoweringToExcel(nextDigit);

    // Mettre à jour la sélection
    this.updateDigitSelection();

    // Passer à l'étape du quotient
    this.goToQuotientStage();
  }

  addLoweringToExcel(digit) {
    // Placer le chiffre abaissé sur la même ligne que le résultat de la soustraction
    const row = this.excelCurrentRow - 1;

    // Calculer la position exacte du chiffre à abaisser dans le dividende original
    const currentDigitPosition = this.index;

    // Pour assurer un alignement correct avec le dividende original
    // Pour l'exemple 28469 ÷ 123, le chiffre à abaisser doit être dans la même colonne que sa position dans le dividende
    const digitCol = this.dividendStartCol + currentDigitPosition;

    // Stocker cette position pour les opérations suivantes
    this.lastLoweredDigitCol = digitCol;

    // Ajouter une flèche d'abaissement partant du dividende
    const arrowStartRow = this.divisorRow; // Ligne du dividende
    const arrowStartCell = this.getExcelCell(arrowStartRow, digitCol);
    if (arrowStartCell) {
      arrowStartCell.style.position = "relative";
      arrowStartCell.innerHTML += `<div style="position:absolute;top:100%;left:50%;transform:translateX(-50%);color:var(--warning-color);font-size:1.2rem;">↓</div>`;
    }

    // Ajouter le chiffre abaissé sur la même ligne que le résultat de la soustraction
    // et dans la même colonne que sa position d'origine
    const digitCell = this.getExcelCell(row, digitCol);
    if (digitCell) {
      digitCell.innerHTML = `<span class="lowering-cell new-operation">${digit}</span>`;
      digitCell.style.textAlign = "center";
      digitCell.style.fontWeight = "bold";
      digitCell.style.color = "var(--warning-color)";
      digitCell.style.backgroundColor = "rgba(243, 156, 18, 0.1)";
    }

    // Ajouter une ligne pointillée pour séparer cette étape de la suivante
    for (let i = this.dividendStartCol - 3; i <= digitCol + 2; i++) {
      const separatorCell = this.getExcelCell(row + 0.5, i);
      if (separatorCell) {
        separatorCell.style.borderBottom = "1px dashed var(--border-color)";
      }
    }

    // Ne pas incrémenter la ligne courante car nous avons utilisé la ligne précédente
    // this.excelCurrentRow reste inchangé

    // Mettre à jour le statut Excel
    this.updateExcelStatus(`Abaissement: ${digit}`, 1);
  }

  endDivision() {
    // Afficher le résumé final
    this.hideAllStages();
    this.finalSummary.classList.remove("hidden");

    let formula = '';
    let congratsMessage = '';

    // Formule et message différents selon le type de division
    switch (this.divisionType) {
      case 'remainder':
        // Division euclidienne (avec reste)
        formula = `${this.dividend} = ${this.divisor} × ${this.quotient} + ${this.remainder}`;

        if (this.remainder === 0) {
          congratsMessage = "🎉 Bravo, la division tombe juste : le reste est nul !";
        } else {
          congratsMessage = "🎉 Bravo, tu as réussi la division euclidienne !";
        }
        break;

      case 'exact':
        // Division exacte (sans reste)
        formula = `${this.dividend} = ${this.divisor} × ${this.quotient}`;
        congratsMessage = "🎉 Bravo, tu as réussi la division exacte !";
        break;

      case 'decimal':
        // Division décimale
        // Calculer le quotient décimal
        const decimalQuotient = this.dividend / this.divisor;
        const formattedQuotient = decimalQuotient.toFixed(this.decimalPlaces);

        formula = `${this.dividend} ÷ ${this.divisor} = ${formattedQuotient}`;
        congratsMessage = "🎉 Bravo, tu as réussi la division décimale !";
        break;

      case 'fraction':
        // Division fractionnaire
        // Simplifier la fraction si possible
        let num = this.dividend;
        let denom = this.divisor;
        const gcd = this.findGCD(num, denom);

        if (gcd > 1) {
          num = num / gcd;
          denom = denom / gcd;
        }

        // Vérifier si c'est une fraction impropre
        if (num >= denom) {
          const wholePart = Math.floor(num / denom);
          const remainder = num % denom;

          if (remainder === 0) {
            formula = `${this.dividend} ÷ ${this.divisor} = ${wholePart}`;
          } else {
            formula = `${this.dividend} ÷ ${this.divisor} = ${wholePart} + ${remainder}/${denom}`;
          }
        } else {
          formula = `${this.dividend} ÷ ${this.divisor} = ${num}/${denom}`;
        }

        congratsMessage = "🎉 Bravo, tu as réussi la division fractionnaire !";
        break;

      case 'grouping':
        // Division par groupement
        formula = `${this.dividend} ÷ ${this.divisor} = ${this.quotient}`;

        if (this.remainder === 0) {
          congratsMessage = "🎉 Bravo, tu as réussi à former des groupes complets !";
        } else {
          congratsMessage = `🎉 Bravo, tu as formé ${this.quotient} groupes complets avec ${this.remainder} élément(s) restant(s) !`;
        }
        break;
    }

    // Afficher la formule finale
    this.finalFormula.innerHTML = `<b>${formula}</b>`;

    // Afficher le message de félicitations
    this.finalCongrats.innerHTML = congratsMessage;

    // Ajouter le résultat final dans Excel
    this.addFinalResultToExcel(formula);
  }

  addFinalResultToExcel(formula) {
    // Position pour le résultat final
    const row = this.excelCurrentRow + 1;
    const col = this.dividendStartCol;

    // Ajouter une ligne de séparation double
    for (let i = col - 4; i <= col + this.dividendLength + 2; i++) {
      const separatorCell = this.getExcelCell(this.excelCurrentRow, i);
      if (separatorCell) {
        separatorCell.style.borderBottom = "3px double var(--primary-color)";
      }
    }

    // Afficher différemment selon le type de division
    switch (this.divisionType) {
      case 'remainder':
      case 'exact':
      case 'grouping':
        // Division euclidienne, exacte ou par groupement
        // Afficher la formule avec chaque chiffre dans une cellule individuelle
        const quotientDigits = this.quotient.toString().split("");

        // Afficher le quotient (résultat final)
        for (let i = 0; i < quotientDigits.length; i++) {
          // Placer le quotient final sous le premier chiffre du diviseur, décalé d'une colonne vers la gauche
          const digitCell = this.getExcelCell(this.quotientRow, this.divisorStartCol - 1 + i);
          if (digitCell) {
            digitCell.innerHTML = `<span class="quotient-cell new-operation">${quotientDigits[i]}</span>`;
            digitCell.style.textAlign = "center";
            digitCell.style.fontWeight = "bold";
            digitCell.style.backgroundColor = "rgba(46, 204, 113, 0.1)";

            // Ajouter une bordure spéciale pour le premier chiffre du quotient
            if (i === 0) {
              digitCell.style.borderTop = "2px dashed var(--success-color)";
            }
          }
        }

        // Pour la division avec reste ou par groupement, afficher le reste
        if ((this.divisionType === 'remainder' || this.divisionType === 'grouping') && this.remainder > 0) {
          const remainderDigits = this.remainder.toString().split("");
          const remainderRow = row;

          // Ajouter le symbole "R" pour indiquer le reste
          const rSymbolCell = this.getExcelCell(remainderRow, col - 1);
          if (rSymbolCell) {
            rSymbolCell.innerHTML = `<span style="color:var(--primary-color);font-weight:bold;">R</span>`;
            rSymbolCell.style.textAlign = "center";
            rSymbolCell.style.verticalAlign = "middle";
          }

          // Afficher chaque chiffre du reste dans une cellule
          for (let i = 0; i < remainderDigits.length; i++) {
            const digitCell = this.getExcelCell(remainderRow, col + i);
            if (digitCell) {
              digitCell.innerHTML = `<span class="subtraction-cell new-operation">${remainderDigits[i]}</span>`;
              digitCell.style.textAlign = "center";
              digitCell.style.backgroundColor = "rgba(52, 152, 219, 0.1)";
            }
          }
        }
        break;

      case 'decimal':
        // Division décimale
        // Calculer le quotient décimal
        const decimalQuotient = this.dividend / this.divisor;
        const formattedQuotient = decimalQuotient.toFixed(this.decimalPlaces);

        // Afficher le résultat décimal
        const resultCell = this.getExcelCell(row, col);
        if (resultCell) {
          resultCell.colSpan = this.dividendLength + 2;
          resultCell.innerHTML = `<span class="result-cell new-operation">${formattedQuotient}</span>`;
          resultCell.style.textAlign = "center";
          resultCell.style.fontWeight = "bold";
          resultCell.style.backgroundColor = "rgba(46, 204, 113, 0.1)";
          resultCell.style.padding = "8px";
          resultCell.style.fontSize = "1.2em";
        }

        // Ajouter le symbole "=" pour indiquer le résultat
        const equalSymbolCell = this.getExcelCell(row, col - 1);
        if (equalSymbolCell) {
          equalSymbolCell.innerHTML = `<span style="color:var(--primary-color);font-weight:bold;">=</span>`;
          equalSymbolCell.style.textAlign = "center";
          equalSymbolCell.style.verticalAlign = "middle";
        }
        break;

      case 'fraction':
        // Division fractionnaire
        // Simplifier la fraction si possible
        let num = this.dividend;
        let denom = this.divisor;
        const gcd = this.findGCD(num, denom);

        if (gcd > 1) {
          num = num / gcd;
          denom = denom / gcd;
        }

        // Créer une cellule pour afficher la fraction
        const fractionCell = this.getExcelCell(row, col);
        if (fractionCell) {
          fractionCell.colSpan = this.dividendLength + 2;

          // Vérifier si c'est une fraction impropre
          if (num >= denom) {
            const wholePart = Math.floor(num / denom);
            const remainder = num % denom;

            if (remainder === 0) {
              // C'est un nombre entier
              fractionCell.innerHTML = `<span class="result-cell new-operation">${wholePart}</span>`;
            } else {
              // C'est une fraction mixte
              fractionCell.innerHTML = `
                <span class="result-cell new-operation">
                  ${wholePart} +
                  <span style="display:inline-block;text-align:center;vertical-align:middle;margin:0 5px;">
                    <span style="display:block;border-bottom:2px solid currentColor;">${remainder}</span>
                    <span style="display:block;">${denom}</span>
                  </span>
                </span>
              `;
            }
          } else {
            // C'est une fraction propre
            fractionCell.innerHTML = `
              <span class="result-cell new-operation">
                <span style="display:inline-block;text-align:center;vertical-align:middle;">
                  <span style="display:block;border-bottom:2px solid currentColor;">${num}</span>
                  <span style="display:block;">${denom}</span>
                </span>
              </span>
            `;
          }

          fractionCell.style.textAlign = "center";
          fractionCell.style.fontWeight = "bold";
          fractionCell.style.backgroundColor = "rgba(46, 204, 113, 0.1)";
          fractionCell.style.padding = "8px";
          fractionCell.style.fontSize = "1.2em";
        }

        // Ajouter le symbole "=" pour indiquer le résultat
        const fractionEqualCell = this.getExcelCell(row, col - 1);
        if (fractionEqualCell) {
          fractionEqualCell.innerHTML = `<span style="color:var(--primary-color);font-weight:bold;">=</span>`;
          fractionEqualCell.style.textAlign = "center";
          fractionEqualCell.style.verticalAlign = "middle";
        }
        break;
    }

    // Mettre à jour le statut Excel
    this.updateExcelStatus(`Résultat final: ${formula}`, 1);

    // Afficher un message de félicitations
    this.showToast("Division terminée avec succès !", "success");
  }

  hideAllStages() {
    this.selectionStage.classList.add("hidden");
    this.quotientStage.classList.add("hidden");
    this.multiplicationStage.classList.add("hidden");
    this.subtractionStage.classList.add("hidden");
    this.loweringStage.classList.add("hidden");
    this.finalSummary.classList.add("hidden");
  }

  resetSimulation() {
    // Revenir à l'écran d'introduction
    this.simulationScreen.classList.add("hidden");
    this.introScreen.classList.remove("hidden");

    // Réinitialiser les inputs
    this.dividendInput.value = "";
    this.divisorInput.value = "";
    this.updatePreview();
  }

  clearExcel() {
    // Vider toutes les cellules du tableau
    const cells = this.excelBody.querySelectorAll("td");
    cells.forEach(cell => {
      cell.innerHTML = "";
      cell.style.borderBottom = "";
    });

    // Réinitialiser le statut
    this.updateExcelStatus("Tableau effacé", 0);
    this.operationCount = 0;
  }

  zoomIn() {
    if (this.currentZoom < 200) {
      this.currentZoom += 10;
      this.updateZoomLevel();
    }
  }

  zoomOut() {
    if (this.currentZoom > 50) {
      this.currentZoom -= 10;
      this.updateZoomLevel();
    }
  }

  updateZoomLevel() {
    this.zoomLevel.textContent = `${this.currentZoom}%`;
    this.excelTable.style.transform = `scale(${this.currentZoom / 100})`;
    this.excelTable.style.transformOrigin = "top left";
  }

  resetZoom() {
    this.currentZoom = 100;
    this.updateZoomLevel();
    this.showToast("Zoom réinitialisé à 100%", "info");
  }

  showExcelHelp() {
    const excelHelpModal = document.getElementById("excel-help-modal");
    if (excelHelpModal) {
      excelHelpModal.classList.remove("hidden");
    }
  }

  hideExcelHelp() {
    const excelHelpModal = document.getElementById("excel-help-modal");
    if (excelHelpModal) {
      excelHelpModal.classList.add("hidden");
    }
  }

  toggleExcelGrid() {
    this.excelTable.classList.toggle("no-grid");
    const isGridHidden = this.excelTable.classList.contains("no-grid");
    this.showToast(`Grille ${isGridHidden ? 'masquée' : 'affichée'}`, "info");

    // Mettre à jour l'icône du bouton
    const toggleGridBtn = document.getElementById("toggle-grid");
    if (toggleGridBtn) {
      const icon = toggleGridBtn.querySelector("i");
      if (icon) {
        icon.className = isGridHidden ? "fas fa-border-none" : "fas fa-border-all";
      }
    }
  }

  toggleExcelHighlight() {
    this.excelTable.classList.toggle("highlight-mode");
    const isHighlightOn = this.excelTable.classList.contains("highlight-mode");
    this.showToast(`Surlignage ${isHighlightOn ? 'activé' : 'désactivé'}`, "info");

    // Mettre à jour l'icône du bouton
    const toggleHighlightBtn = document.getElementById("toggle-highlight");
    if (toggleHighlightBtn) {
      const icon = toggleHighlightBtn.querySelector("i");
      if (icon) {
        icon.className = isHighlightOn ? "fas fa-highlighter" : "far fa-highlighter";
      }
    }
  }

  changeExcelViewMode(mode) {
    // Supprimer toutes les classes de mode d'affichage
    this.excelTable.classList.remove("compact-view", "detailed-view");

    // Ajouter la classe correspondant au mode sélectionné
    if (mode === "compact") {
      this.excelTable.classList.add("compact-view");
      this.showToast("Vue compacte activée", "info");
    } else if (mode === "detailed") {
      this.excelTable.classList.add("detailed-view");
      this.showToast("Vue détaillée activée", "info");
    } else {
      this.showToast("Vue standard activée", "info");
    }
  }

  toggleExcelFullscreen() {
    const excelWrapper = document.querySelector(".excel-sheet-wrapper");
    if (!excelWrapper) return;

    excelWrapper.classList.toggle("fullscreen");
    const isFullscreen = excelWrapper.classList.contains("fullscreen");

    // Mettre à jour l'icône du bouton
    const fullscreenBtn = document.getElementById("fullscreen-excel");
    if (fullscreenBtn) {
      const icon = fullscreenBtn.querySelector("i");
      if (icon) {
        icon.className = isFullscreen ? "fas fa-compress" : "fas fa-expand";
      }
      fullscreenBtn.title = isFullscreen ? "Quitter le plein écran" : "Plein écran";
    }

    this.showToast(`Mode plein écran ${isFullscreen ? 'activé' : 'désactivé'}`, "info");
  }

  exportExcelAsImage() {
    try {
      // Utiliser html2canvas pour capturer le tableau Excel
      this.showToast("Préparation de l'exportation...", "info");

      // Simuler l'exportation (dans une implémentation réelle, vous utiliseriez html2canvas)
      setTimeout(() => {
        this.showToast("Tableau exporté en image", "success");
      }, 1000);

      // Note: Pour une implémentation réelle, vous pourriez utiliser:
      /*
      html2canvas(this.excelTable).then(canvas => {
        const imgData = canvas.toDataURL('image/png');
        const link = document.createElement('a');
        link.href = imgData;
        link.download = `division-${this.dividend}-${this.divisor}.png`;
        link.click();
      });
      */
    } catch (error) {
      this.showToast("Erreur lors de l'exportation", "error");
    }
  }

  printExcelTable() {
    try {
      // Préparer l'impression
      this.showToast("Préparation de l'impression...", "info");

      // Créer une fenêtre d'impression
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        this.showToast("Veuillez autoriser les popups pour imprimer", "warning");
        return;
      }

      // Créer le contenu à imprimer
      const printContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>Division Euclidienne - ${this.dividend} ÷ ${this.divisor}</title>
          <style>
            body { font-family: Arial, sans-serif; padding: 20px; }
            h1 { font-size: 18px; margin-bottom: 10px; }
            table { border-collapse: collapse; width: 100%; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
            .footer { margin-top: 20px; font-size: 12px; text-align: center; color: #666; }
          </style>
        </head>
        <body>
          <h1>Division Euclidienne: ${this.dividend} ÷ ${this.divisor}</h1>
          <div>Quotient: ${this.quotient} | Reste: ${this.remainder}</div>
          <div>Formule: ${this.dividend} = ${this.divisor} - ${this.quotient} + ${this.remainder}</div>
          <div class="footer">Généré par la Simulation de Division Euclidienne</div>
        </body>
        </html>
      `;

      // Écrire le contenu dans la fenêtre d'impression
      printWindow.document.write(printContent);
      printWindow.document.close();

      // Imprimer et fermer la fenêtre
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 500);

    } catch (error) {
      this.showToast("Erreur lors de l'impression", "error");
    }
  }

  updateExcelStatus(lastOperation, increment = 1) {
    // Mettre à jour le compteur d'opérations
    if (increment > 0) {
      this.operationCount += increment;
    }

    // Mettre à jour les éléments d'interface
    if (this.excelOperationCount) {
      this.excelOperationCount.textContent = `${this.operationCount} opération${this.operationCount !== 1 ? 's' : ''}`;
    }

    if (this.excelLastOperation && lastOperation) {
      this.excelLastOperation.textContent = lastOperation;
    }
  }

  setupAccessibility() {
    // Initialiser les options d'accessibilité
    this.accessibilitySettings = {
      fontSize: 100,
      contrast: false,
      dyslexicFont: false,
      reduceMotion: false
    };

    // Charger les paramètres d'accessibilité depuis le localStorage
    this.loadAccessibilitySettings();

    // Configurer le panneau d'accessibilité
    const accessibilityPanel = document.getElementById('accessibility-panel');
    const accessibilityToggle = document.getElementById('accessibility-toggle');
    const footerAccessibility = document.getElementById('footer-accessibility');
    const panelClose = document.querySelector('#accessibility-panel .panel-close');

    // Ouvrir/fermer le panneau d'accessibilité
    if (accessibilityToggle) {
      accessibilityToggle.addEventListener('click', () => {
        accessibilityPanel.classList.remove('hidden');
      });
    }

    if (footerAccessibility) {
      footerAccessibility.addEventListener('click', () => {
        accessibilityPanel.classList.remove('hidden');
      });
    }

    if (panelClose) {
      panelClose.addEventListener('click', () => {
        accessibilityPanel.classList.add('hidden');
      });
    }

    // Configurer les contrôles de taille de texte
    const fontSizeSlider = document.getElementById('font-size');
    const decreaseFontBtn = document.getElementById('decrease-font');
    const increaseFontBtn = document.getElementById('increase-font');

    if (fontSizeSlider) {
      fontSizeSlider.value = this.accessibilitySettings.fontSize;
      fontSizeSlider.addEventListener('input', () => {
        this.setFontSize(fontSizeSlider.value);
      });
    }

    if (decreaseFontBtn) {
      decreaseFontBtn.addEventListener('click', () => {
        const newSize = Math.max(80, this.accessibilitySettings.fontSize - 10);
        this.setFontSize(newSize);
        if (fontSizeSlider) fontSizeSlider.value = newSize;
      });
    }

    if (increaseFontBtn) {
      increaseFontBtn.addEventListener('click', () => {
        const newSize = Math.min(150, this.accessibilitySettings.fontSize + 10);
        this.setFontSize(newSize);
        if (fontSizeSlider) fontSizeSlider.value = newSize;
      });
    }

    // Configurer les autres options d'accessibilité
    const contrastMode = document.getElementById('contrast-mode');
    const dyslexicFont = document.getElementById('dyslexic-font');
    const reduceMotion = document.getElementById('reduce-motion');
    const resetAccessibility = document.getElementById('reset-accessibility');

    if (contrastMode) {
      contrastMode.checked = this.accessibilitySettings.contrast;
      contrastMode.addEventListener('change', () => {
        this.setContrast(contrastMode.checked);
      });
    }

    if (dyslexicFont) {
      dyslexicFont.checked = this.accessibilitySettings.dyslexicFont;
      dyslexicFont.addEventListener('change', () => {
        this.setDyslexicFont(dyslexicFont.checked);
      });
    }

    if (reduceMotion) {
      reduceMotion.checked = this.accessibilitySettings.reduceMotion;
      reduceMotion.addEventListener('change', () => {
        this.setReduceMotion(reduceMotion.checked);
      });
    }

    if (resetAccessibility) {
      resetAccessibility.addEventListener('click', () => {
        this.resetAccessibilitySettings();

        // Mettre à jour l'interface
        if (fontSizeSlider) fontSizeSlider.value = 100;
        if (contrastMode) contrastMode.checked = false;
        if (dyslexicFont) dyslexicFont.checked = false;
        if (reduceMotion) reduceMotion.checked = false;

        this.showToast('Paramètres d\'accessibilité réinitialisés', 'info');
      });
    }

    // Ajouter des attributs ARIA pour l'accessibilité
    document.querySelectorAll("button, input, select").forEach(el => {
      if (!el.hasAttribute("tabindex")) el.setAttribute("tabindex", "0");
      if (!el.hasAttribute("aria-label") && el.title) el.setAttribute("aria-label", el.title);
    });

    // Raccourcis clavier
    document.addEventListener("keydown", (e) => {
      // Fermer les modales avec Escape
      if (e.key === "Escape") {
        // Fermer les modales ouvertes
        document.querySelectorAll(".modal-overlay:not(.hidden), .panel:not(.hidden)").forEach(modal => {
          modal.classList.add("hidden");
        });
      }

      // Autres raccourcis clavier
      if (e.altKey) {
        switch (e.key) {
          case 'a':
            // Alt+A: Ouvrir le panneau d'accessibilité
            e.preventDefault();
            accessibilityPanel.classList.remove('hidden');
            break;
          case 'h':
            // Alt+H: Ouvrir l'aide
            e.preventDefault();
            document.getElementById('help-button').click();
            break;
          case 't':
            // Alt+T: Changer de thème
            e.preventDefault();
            document.getElementById('theme-toggle').click();
            break;
        }
      }
    });

    // Appliquer les paramètres initiaux
    this.applyAccessibilitySettings();
  }

  loadAccessibilitySettings() {
    try {
      const savedSettings = localStorage.getItem('accessibilitySettings');
      if (savedSettings) {
        this.accessibilitySettings = JSON.parse(savedSettings);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des paramètres d\'accessibilité:', error);
    }
  }

  saveAccessibilitySettings() {
    try {
      localStorage.setItem('accessibilitySettings', JSON.stringify(this.accessibilitySettings));
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des paramètres d\'accessibilité:', error);
    }
  }

  applyAccessibilitySettings() {
    // Appliquer la taille de police
    document.documentElement.style.fontSize = `${this.accessibilitySettings.fontSize}%`;

    // Appliquer le mode contraste élevé
    if (this.accessibilitySettings.contrast) {
      document.documentElement.setAttribute('data-contrast', 'high');
    } else {
      document.documentElement.removeAttribute('data-contrast');
    }

    // Appliquer la police pour dyslexiques
    if (this.accessibilitySettings.dyslexicFont) {
      document.documentElement.setAttribute('data-font', 'dyslexic');
    } else {
      document.documentElement.removeAttribute('data-font');
    }

    // Appliquer la réduction des animations
    if (this.accessibilitySettings.reduceMotion) {
      document.documentElement.setAttribute('data-motion', 'reduced');
    } else {
      document.documentElement.removeAttribute('data-motion');
    }
  }

  setFontSize(size) {
    this.accessibilitySettings.fontSize = parseInt(size);
    document.documentElement.style.fontSize = `${size}%`;
    this.saveAccessibilitySettings();
  }

  setContrast(enabled) {
    this.accessibilitySettings.contrast = enabled;
    if (enabled) {
      document.documentElement.setAttribute('data-contrast', 'high');
    } else {
      document.documentElement.removeAttribute('data-contrast');
    }
    this.saveAccessibilitySettings();
  }

  setDyslexicFont(enabled) {
    this.accessibilitySettings.dyslexicFont = enabled;
    if (enabled) {
      document.documentElement.setAttribute('data-font', 'dyslexic');
    } else {
      document.documentElement.removeAttribute('data-font');
    }
    this.saveAccessibilitySettings();
  }

  setReduceMotion(enabled) {
    this.accessibilitySettings.reduceMotion = enabled;
    if (enabled) {
      document.documentElement.setAttribute('data-motion', 'reduced');
    } else {
      document.documentElement.removeAttribute('data-motion');
    }
    this.saveAccessibilitySettings();
  }

  resetAccessibilitySettings() {
    this.accessibilitySettings = {
      fontSize: 100,
      contrast: false,
      dyslexicFont: false,
      reduceMotion: false
    };
    this.applyAccessibilitySettings();
    this.saveAccessibilitySettings();
  }

  setupThemeToggle() {
    const themeToggle = document.getElementById("theme-toggle");
    if (themeToggle) {
      // Vérifier si un thème est déjà enregistré
      const currentTheme = localStorage.getItem("theme");
      if (currentTheme) {
        document.documentElement.setAttribute("data-theme", currentTheme);
        this.updateThemeIcon(currentTheme === "dark");
      } else if (window.matchMedia("(prefers-color-scheme: dark)").matches) {
        // Si l'utilisateur préfère le thème sombre au niveau du système
        document.documentElement.setAttribute("data-theme", "dark");
        this.updateThemeIcon(true);
      }

      // Événement de basculement du thème
      themeToggle.addEventListener("click", () => {
        const currentTheme = document.documentElement.getAttribute("data-theme") || "light";
        const newTheme = currentTheme === "light" ? "dark" : "light";

        document.documentElement.setAttribute("data-theme", newTheme);
        localStorage.setItem("theme", newTheme);

        this.updateThemeIcon(newTheme === "dark");
        this.showToast(`Thème ${newTheme === "dark" ? "sombre" : "clair"} activé`, "info");
      });
    }
  }

  updateThemeIcon(isDark) {
    const themeToggle = document.getElementById("theme-toggle");
    if (themeToggle) {
      const icon = themeToggle.querySelector("i");
      if (icon) {
        icon.className = isDark ? "fas fa-sun" : "fas fa-moon";
      }
    }
  }

  setupExcelHelp() {
    const helpExcelBtn = document.getElementById("help-excel");
    const excelHelpModal = document.getElementById("excel-help-modal");
    const closeHelpBtn = document.getElementById("close-help");

    if (helpExcelBtn && excelHelpModal && closeHelpBtn) {
      helpExcelBtn.addEventListener("click", () => {
        excelHelpModal.classList.remove("hidden");
      });

      closeHelpBtn.addEventListener("click", () => {
        excelHelpModal.classList.add("hidden");
      });

      // Fermer avec Echap
      document.addEventListener("keydown", (e) => {
        if (e.key === "Escape" && !excelHelpModal.classList.contains("hidden")) {
          excelHelpModal.classList.add("hidden");
        }
      });
    }
  }

  setupToastNotifications() {
    this.toastContainer = document.getElementById("toast-container");
  }

  showToast(message, type = "info", duration = 3000) {
    if (!this.toastContainer) return;

    // Créer l'élément toast
    const toast = document.createElement("div");
    toast.className = `toast ${type}`;
    toast.setAttribute("role", "alert");

    // Ajouter une icône selon le type
    let icon = "";
    switch (type) {
      case "success":
        icon = '<i class="fas fa-check-circle"></i>';
        break;
      case "error":
        icon = '<i class="fas fa-exclamation-circle"></i>';
        break;
      case "warning":
        icon = '<i class="fas fa-exclamation-triangle"></i>';
        break;
      case "info":
      default:
        icon = '<i class="fas fa-info-circle"></i>';
        break;
    }

    toast.innerHTML = `${icon} <span>${message}</span>`;

    // Ajouter au conteneur
    this.toastContainer.appendChild(toast);

    // Supprimer après la durée spécifiée
    setTimeout(() => {
      if (toast.parentNode === this.toastContainer) {
        this.toastContainer.removeChild(toast);
      }
    }, duration + 500); // +500ms pour l'animation de sortie
  }

  setupNavigation() {
    // Gestion de la navigation principale
    const navLinks = document.querySelectorAll('.main-nav a');
    const footerLinks = document.querySelectorAll('.footer-section a');

    // Fonction pour activer un onglet
    const activateTab = (id) => {
      // Désactiver tous les onglets
      navLinks.forEach(link => link.classList.remove('active'));

      // Activer l'onglet sélectionné
      const activeLink = document.getElementById(id);
      if (activeLink) activeLink.classList.add('active');

      // Gérer l'affichage des sections
      if (id === 'nav-simulation' || id === 'footer-simulation') {
        // Ne rien faire, la simulation est déjà visible
      } else if (id === 'nav-aide' || id === 'footer-aide') {
        this.showGeneralHelp();
      } else if (id === 'nav-a-propos' || id === 'footer-a-propos') {
        this.showAbout();
      }
    };

    // Ajouter les événements aux liens de navigation
    navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        activateTab(link.id);
      });
    });

    // Ajouter les événements aux liens du footer
    footerLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();

        if (link.id === 'export-pdf') {
          this.exportToPDF();
        } else if (link.id === 'print-simulation') {
          this.printSimulation();
        } else if (link.id === 'share-simulation') {
          this.shareSimulation();
        } else {
          // Pour les liens de navigation dans le footer
          const navId = link.id.replace('footer-', 'nav-');
          activateTab(navId);
        }
      });
    });

    // Ajouter un événement au bouton d'aide générale
    const helpButton = document.getElementById('help-button');
    if (helpButton) {
      helpButton.addEventListener('click', () => {
        this.showGeneralHelp();
      });
    }
  }

  setupModals() {
    // Modales d'aide générale et À propos
    const generalHelpModal = document.getElementById('general-help-modal');
    const aboutModal = document.getElementById('about-modal');

    // Boutons de fermeture
    const closeGeneralHelp = document.getElementById('close-general-help');
    const generalHelpClose = document.getElementById('general-help-close');
    const closeAbout = document.getElementById('close-about');
    const aboutClose = document.getElementById('about-close');

    // Fermer la modale d'aide générale
    if (closeGeneralHelp && generalHelpClose) {
      [closeGeneralHelp, generalHelpClose].forEach(btn => {
        btn.addEventListener('click', () => {
          generalHelpModal.classList.add('hidden');
        });
      });
    }

    // Fermer la modale À propos
    if (closeAbout && aboutClose) {
      [closeAbout, aboutClose].forEach(btn => {
        btn.addEventListener('click', () => {
          aboutModal.classList.add('hidden');
        });
      });
    }

    // Gestion des onglets dans l'aide générale
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabButtons.forEach(btn => {
      btn.addEventListener('click', () => {
        // Désactiver tous les onglets
        tabButtons.forEach(b => b.classList.remove('active'));
        tabPanes.forEach(p => p.classList.remove('active'));

        // Activer l'onglet sélectionné
        btn.classList.add('active');
        const tabId = btn.getAttribute('data-tab');
        const tabPane = document.getElementById(tabId);
        if (tabPane) tabPane.classList.add('active');
      });
    });

    // Fermer les modales avec la touche Echap
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        generalHelpModal.classList.add('hidden');
        aboutModal.classList.add('hidden');
      }
    });

    // Fermer les modales en cliquant en dehors
    [generalHelpModal, aboutModal].forEach(modal => {
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          modal.classList.add('hidden');
        }
      });
    });
  }

  showGeneralHelp() {
    const generalHelpModal = document.getElementById('general-help-modal');
    if (generalHelpModal) {
      generalHelpModal.classList.remove('hidden');
    }
  }

  showAbout() {
    const aboutModal = document.getElementById('about-modal');
    if (aboutModal) {
      aboutModal.classList.remove('hidden');
    }
  }

  setupFooter() {
    // Fonctionnalités d'exportation et de partage
    const exportPdfBtn = document.getElementById('export-pdf');
    const printBtn = document.getElementById('print-simulation');
    const shareBtn = document.getElementById('share-simulation');

    if (exportPdfBtn) {
      exportPdfBtn.addEventListener('click', (e) => {
        e.preventDefault();
        this.exportToPDF();
      });
    }

    if (printBtn) {
      printBtn.addEventListener('click', (e) => {
        e.preventDefault();
        this.printSimulation();
      });
    }

    if (shareBtn) {
      shareBtn.addEventListener('click', (e) => {
        e.preventDefault();
        this.shareSimulation();
      });
    }
  }

  updateYear() {
    const yearSpan = document.getElementById('current-year');
    if (yearSpan) {
      yearSpan.textContent = new Date().getFullYear();
    }
  }

  exportToPDF() {
    // Simuler l'exportation en PDF
    this.showToast('Fonctionnalité d\'exportation en PDF à venir', 'info');

    // Note: Pour une implémentation réelle, vous pourriez utiliser une bibliothèque comme jsPDF
    // ou html2pdf.js pour convertir le contenu en PDF
  }

  printSimulation() {
    // Ouvrir la boîte de dialogue d'impression
    window.print();
  }

  shareSimulation() {
    // Vérifier si l'API Web Share est disponible
    if (navigator.share) {
      navigator.share({
        title: 'Simulation de Division Euclidienne',
        text: 'Découvrez cette simulation interactive de division euclidienne !',
        url: window.location.href
      })
      .then(() => this.showToast('Partagé avec succès !', 'success'))
      .catch(() => this.showToast('Erreur lors du partage', 'error'));
    } else {
      // Fallback pour les navigateurs qui ne supportent pas l'API Web Share
      // Copier l'URL dans le presse-papier
      try {
        // Utiliser l'API Clipboard moderne si disponible
        if (navigator.clipboard && navigator.clipboard.writeText) {
          navigator.clipboard.writeText(window.location.href)
            .then(() => this.showToast('URL copiée dans le presse-papier', 'success'))
            .catch(() => this.showToast('Impossible de copier l\'URL', 'error'));
        } else {
          // Fallback pour les navigateurs plus anciens
          const dummy = document.createElement('input');
          document.body.appendChild(dummy);
          dummy.value = window.location.href;
          dummy.select();
          const successful = document.execCommand('copy');
          document.body.removeChild(dummy);

          if (successful) {
            this.showToast('URL copiée dans le presse-papier', 'success');
          } else {
            this.showToast('Impossible de copier l\'URL', 'error');
          }
        }
      } catch (err) {
        this.showToast('Erreur lors du partage', 'error');
      }
    }
  }
}

// Initialiser la simulation au chargement de la page
window.onload = () => new DivisionSimulation();
