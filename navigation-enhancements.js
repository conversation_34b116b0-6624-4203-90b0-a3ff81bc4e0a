/**
 * Améliorations pour la navigation
 * Ce script ajoute des fonctionnalités pour améliorer l'expérience de navigation
 */

document.addEventListener('DOMContentLoaded', function() {
  // Amélioration de la navigation principale
  enhanceMainNavigation();
  
  // Amélioration des liens d'ancrage
  enhanceAnchorLinks();
  
  // Amélioration de l'accessibilité des liens
  enhanceAccessibility();
});

/**
 * Améliore la navigation principale avec des indicateurs de défilement
 */
function enhanceMainNavigation() {
  const mainNav = document.querySelector('.main-nav');
  if (!mainNav) return;
  
  // Vérifier si la navigation a besoin de défilement
  function checkScrollIndicators() {
    if (mainNav.scrollWidth > mainNav.clientWidth) {
      // Il y a du contenu qui déborde, vérifier la position de défilement
      const isScrollLeft = mainNav.scrollLeft > 10;
      const isScrollRight = mainNav.scrollLeft < (mainNav.scrollWidth - mainNav.clientWidth - 10);
      
      mainNav.classList.toggle('scroll-left', isScrollLeft);
      mainNav.classList.toggle('scroll-right', isScrollRight);
    } else {
      // Pas de défilement nécessaire
      mainNav.classList.remove('scroll-left', 'scroll-right');
    }
  }
  
  // Vérifier au chargement et lors du redimensionnement
  checkScrollIndicators();
  window.addEventListener('resize', checkScrollIndicators);
  
  // Vérifier lors du défilement
  mainNav.addEventListener('scroll', checkScrollIndicators);
  
  // Ajouter des boutons de défilement pour la navigation mobile
  const navContainer = mainNav.parentElement;
  
  // Créer les boutons de défilement
  const scrollLeftBtn = document.createElement('button');
  scrollLeftBtn.className = 'nav-scroll-btn nav-scroll-left';
  scrollLeftBtn.innerHTML = '<i class="fas fa-chevron-left" aria-hidden="true"></i>';
  scrollLeftBtn.setAttribute('aria-label', 'Faire défiler vers la gauche');
  
  const scrollRightBtn = document.createElement('button');
  scrollRightBtn.className = 'nav-scroll-btn nav-scroll-right';
  scrollRightBtn.innerHTML = '<i class="fas fa-chevron-right" aria-hidden="true"></i>';
  scrollRightBtn.setAttribute('aria-label', 'Faire défiler vers la droite');
  
  // Ajouter les boutons au conteneur
  navContainer.appendChild(scrollLeftBtn);
  navContainer.appendChild(scrollRightBtn);
  
  // Ajouter les événements de défilement
  scrollLeftBtn.addEventListener('click', () => {
    mainNav.scrollBy({ left: -100, behavior: 'smooth' });
  });
  
  scrollRightBtn.addEventListener('click', () => {
    mainNav.scrollBy({ left: 100, behavior: 'smooth' });
  });
  
  // Mettre à jour la visibilité des boutons
  function updateScrollButtonsVisibility() {
    scrollLeftBtn.classList.toggle('visible', mainNav.scrollLeft > 10);
    scrollRightBtn.classList.toggle('visible', mainNav.scrollLeft < (mainNav.scrollWidth - mainNav.clientWidth - 10));
  }
  
  // Vérifier au chargement et lors du défilement
  updateScrollButtonsVisibility();
  mainNav.addEventListener('scroll', updateScrollButtonsVisibility);
  window.addEventListener('resize', updateScrollButtonsVisibility);
}

/**
 * Améliore les liens d'ancrage avec un défilement fluide
 */
function enhanceAnchorLinks() {
  // Sélectionner tous les liens qui pointent vers des ancres
  const anchorLinks = document.querySelectorAll('a[href^="#"]:not([href="#"])');
  
  anchorLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      // Vérifier si l'utilisateur préfère réduire les animations
      const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
      
      const targetId = this.getAttribute('href').substring(1);
      const targetElement = document.getElementById(targetId);
      
      if (targetElement) {
        e.preventDefault();
        
        // Calculer l'offset pour tenir compte des éléments fixes
        const headerHeight = document.querySelector('header')?.offsetHeight || 0;
        const offset = headerHeight + 20; // 20px de marge supplémentaire
        
        const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - offset;
        
        // Utiliser scrollTo avec ou sans animation selon les préférences
        window.scrollTo({
          top: targetPosition,
          behavior: prefersReducedMotion ? 'auto' : 'smooth'
        });
        
        // Mettre le focus sur l'élément cible pour l'accessibilité
        targetElement.setAttribute('tabindex', '-1');
        targetElement.focus({ preventScroll: true });
        
        // Mettre à jour l'URL sans recharger la page
        history.pushState(null, null, `#${targetId}`);
      }
    });
  });
}

/**
 * Améliore l'accessibilité des liens
 */
function enhanceAccessibility() {
  // Ajouter des attributs aria-current aux liens de navigation actifs
  const updateActiveLinks = () => {
    const currentPath = window.location.pathname;
    const currentHash = window.location.hash;
    
    // Mettre à jour les liens de navigation
    document.querySelectorAll('nav a').forEach(link => {
      const isCurrentPage = link.pathname === currentPath;
      const isCurrentSection = currentHash && link.hash === currentHash;
      
      if (isCurrentPage || isCurrentSection) {
        link.setAttribute('aria-current', isCurrentSection ? 'location' : 'page');
      } else {
        link.removeAttribute('aria-current');
      }
    });
  };
  
  // Mettre à jour au chargement et lors des changements d'URL
  updateActiveLinks();
  window.addEventListener('hashchange', updateActiveLinks);
  
  // Améliorer les liens externes
  document.querySelectorAll('a[href^="http"]').forEach(link => {
    // Ne pas modifier les liens qui ont déjà ces attributs
    if (!link.hasAttribute('rel')) {
      link.setAttribute('rel', 'noopener noreferrer');
    }
    
    // Ajouter une indication visuelle pour les liens externes
    if (!link.querySelector('.external-link-icon')) {
      const icon = document.createElement('span');
      icon.className = 'external-link-icon';
      icon.innerHTML = '<i class="fas fa-external-link-alt" aria-hidden="true"></i>';
      icon.style.marginLeft = '0.25em';
      icon.style.fontSize = '0.8em';
      
      // Ajouter l'icône seulement si le lien n'a pas déjà un contenu complexe
      if (!link.querySelector('img') && !link.querySelector('svg')) {
        link.appendChild(icon);
      }
      
      // Ajouter une indication pour les lecteurs d'écran si elle n'existe pas déjà
      if (!link.getAttribute('aria-label')) {
        const originalText = link.textContent.trim();
        link.setAttribute('aria-label', `${originalText} (s'ouvre dans un nouvel onglet)`);
      }
    }
  });
}
