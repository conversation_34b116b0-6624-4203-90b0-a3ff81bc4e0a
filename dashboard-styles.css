/**
 * dashboard-styles.css
 *
 * Copyright (c) 2023 Ali MESSAOUDI
 * Tous droits réservés.
 * Ce code est protégé par les lois sur le droit d'auteur et ne peut être reproduit,
 * distribué ou utilisé sans autorisation explicite de l'auteur.
 */

/* Styles pour les tableaux de bord */

/* Variables */
:root {
  --primary-color: #3498db;
  --primary-color-dark: #2980b9;
  --primary-color-light: #e1f0fa;
  --secondary-color: #2ecc71;
  --secondary-color-dark: #27ae60;
  --danger-color: #e74c3c;
  --warning-color: #f39c12;
  --info-color: #3498db;
  --success-color: #2ecc71;
  --text-color: #333;
  --text-color-light: #777;
  --border-color: #ddd;
  --card-background: #fff;
  --background-color: #f5f7fa;
  --shadow-color: rgba(0, 0, 0, 0.1);
}

/* Conteneur principal */
.dashboard-main {
  background-color: var(--background-color);
  min-height: calc(100vh - 200px);
  padding: 40px 20px;
}

.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* En-tête du tableau de bord */
.dashboard-header {
  margin-bottom: 30px;
}

.dashboard-header h2 {
  font-size: 2rem;
  margin-bottom: 8px;
  color: var(--text-color);
}

.welcome-message {
  font-size: 1.1rem;
  color: var(--text-color-light);
}

#welcome-name {
  font-weight: 600;
  color: var(--primary-color);
}

/* Statistiques */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background-color: var(--card-background);
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 10px var(--shadow-color);
  display: flex;
  align-items: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 15px var(--shadow-color);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--primary-color-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-right: 15px;
}

.stat-content h3 {
  font-size: 0.9rem;
  margin-bottom: 5px;
  color: var(--text-color-light);
}

.stat-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0;
}

/* Actions du tableau de bord */
.dashboard-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 30px;
}

.dashboard-actions .btn-primary {
  padding: 12px 20px;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  gap: 8px;
  width: auto;
}

/* Sections du tableau de bord */
.dashboard-sections {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 30px;
  margin-bottom: 30px;
}

.dashboard-section {
  background-color: var(--card-background);
  border-radius: 10px;
  box-shadow: 0 4px 10px var(--shadow-color);
  overflow: hidden;
}

.section-header {
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-header h3 {
  font-size: 1.2rem;
  margin: 0;
  color: var(--text-color);
}

.section-link {
  color: var(--primary-color);
  font-size: 0.9rem;
  text-decoration: none;
  transition: color 0.2s ease;
}

.section-link:hover {
  color: var(--primary-color-dark);
  text-decoration: underline;
}

.section-content {
  padding: 20px;
}

/* Liste d'exercices */
.exercise-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.exercise-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  background-color: var(--background-color);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.exercise-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--shadow-color);
}

.exercise-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-color-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  margin-right: 15px;
}

.exercise-details {
  flex: 1;
}

.exercise-details h4 {
  font-size: 1rem;
  margin: 0 0 5px 0;
  color: var(--text-color);
}

.exercise-details p {
  font-size: 0.85rem;
  color: var(--text-color-light);
  margin: 0 0 8px 0;
}

.exercise-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  font-size: 0.8rem;
  color: var(--text-color-light);
}

.exercise-date,
.exercise-score,
.exercise-completion {
  display: flex;
  align-items: center;
}

.exercise-actions {
  display: flex;
  gap: 8px;
}

.btn-secondary {
  padding: 8px 12px;
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  color: var(--text-color);
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background-color: var(--primary-color-light);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.btn-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  color: var(--text-color-light);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-icon:hover {
  background-color: var(--primary-color-light);
  color: var(--primary-color);
  border-color: var(--primary-color);
}

/* Graphique de progression */
.progress-chart {
  height: 250px;
  position: relative;
}

.chart-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-bars {
  flex: 1;
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  padding: 0 20px;
  gap: 20px;
}

.chart-bar {
  flex: 1;
  background: linear-gradient(
    to top,
    var(--primary-color),
    var(--primary-color-dark)
  );
  border-radius: 4px 4px 0 0;
  position: relative;
  max-width: 60px;
}

.chart-value {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--text-color);
}

.chart-labels {
  display: flex;
  justify-content: space-around;
  padding: 15px 0 0 0;
  font-size: 0.8rem;
  color: var(--text-color-light);
  text-align: center;
}

.chart-labels span {
  flex: 1;
  padding: 0 5px;
  max-width: 100px;
}

/* Liste d'élèves */
.students-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.student-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  background-color: var(--background-color);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.student-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--shadow-color);
}

.student-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-color-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  margin-right: 15px;
}

.student-details {
  flex: 1;
}

.student-details h4 {
  font-size: 1rem;
  margin: 0 0 5px 0;
  color: var(--text-color);
}

.student-details p {
  font-size: 0.85rem;
  color: var(--text-color-light);
  margin: 0 0 8px 0;
}

.student-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  font-size: 0.8rem;
  color: var(--text-color-light);
}

.student-actions {
  display: flex;
  gap: 8px;
}

/* Badges et récompenses */
.badges-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 15px;
}

.badge-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  background-color: var(--background-color);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.badge-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--shadow-color);
}

.badge-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--primary-color-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-bottom: 10px;
}

.badge-name {
  font-size: 0.85rem;
  text-align: center;
  color: var(--text-color);
}

.badge-locked {
  opacity: 0.6;
}

.badge-locked .badge-icon {
  background-color: var(--text-color-light);
  color: white;
}

/* Recommandations */
.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.recommendation-item {
  display: flex;
  padding: 15px;
  border-radius: 8px;
  background-color: var(--background-color);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.recommendation-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--shadow-color);
}

.recommendation-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-color-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  margin-right: 15px;
}

.recommendation-content {
  flex: 1;
}

.recommendation-content h4 {
  font-size: 1rem;
  margin: 0 0 5px 0;
  color: var(--text-color);
}

.recommendation-content p {
  font-size: 0.9rem;
  color: var(--text-color-light);
  margin: 0 0 15px 0;
}

/* Élèves nécessitant de l'attention */
.attention-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.attention-item {
  display: flex;
  padding: 15px;
  border-radius: 8px;
  background-color: var(--background-color);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.attention-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--shadow-color);
}

.attention-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  margin-right: 15px;
}

.attention-icon.warning {
  background-color: rgba(243, 156, 18, 0.2);
  color: var(--warning-color);
}

.attention-icon.danger {
  background-color: rgba(231, 76, 60, 0.2);
  color: var(--danger-color);
}

.attention-icon.info {
  background-color: rgba(52, 152, 219, 0.2);
  color: var(--info-color);
}

.attention-content {
  flex: 1;
}

.attention-content h4 {
  font-size: 1rem;
  margin: 0 0 5px 0;
  color: var(--text-color);
}

.attention-content p {
  font-size: 0.9rem;
  color: var(--text-color-light);
  margin: 0 0 15px 0;
}

/* Thème sombre */
body.dark-theme {
  --text-color: #e1e1e1;
  --text-color-light: #a0a0a0;
  --border-color: #444;
  --card-background: #2a2a2a;
  --background-color: #1a1a1a;
  --shadow-color: rgba(0, 0, 0, 0.3);
}

/* Responsive */
@media (max-width: 992px) {
  .dashboard-sections {
    grid-template-columns: 1fr;
  }

  .dashboard-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .dashboard-main {
    padding: 20px 15px;
  }

  .dashboard-stats {
    grid-template-columns: 1fr;
  }

  .exercise-meta,
  .student-meta {
    flex-direction: column;
    gap: 5px;
  }

  .badges-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 576px) {
  .dashboard-actions {
    flex-direction: column;
  }

  .dashboard-actions .btn-primary {
    width: 100%;
  }

  .recommendation-item,
  .attention-item {
    flex-direction: column;
  }

  .recommendation-icon,
  .attention-icon {
    margin-bottom: 10px;
    margin-right: 0;
  }

  .badges-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
