/**
 * Excel Math Operations
 * Ce script améliore l'affichage et la gestion des opérations mathématiques
 * dans la feuille Excel pour la division euclidienne
 */

class ExcelMathOperations {
  constructor() {
    // Éléments DOM
    this.excelTable = document.getElementById('excel-table');
    this.excelBody = document.getElementById('excel-body');
    
    // Variables d'état
    this.operationCount = 0;
    this.lastOperation = '';
    this.divisor = 0;
    this.dividend = 0;
    this.quotient = '';
    this.remainder = 0;
    
    // Initialisation
    this.init();
  }
  
  /**
   * Initialise les améliorations des opérations mathématiques
   */
  init() {
    // Observer les changements dans le tableau Excel
    this.setupMutationObserver();
    
    // Récupérer les valeurs initiales
    this.getInitialValues();
    
    // Mettre à jour le compteur d'opérations
    this.updateOperationCount();
  }
  
  /**
   * Configure un observateur de mutations pour détecter les nouvelles opérations
   */
  setupMutationObserver() {
    // Créer un observateur de mutations
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // Parcourir les nœuds ajoutés
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // Si c'est une ligne de tableau
              if (node.tagName === 'TR') {
                this.processNewRow(node);
              }
              // Rechercher les cellules à l'intérieur du nœud ajouté
              else {
                const cells = node.querySelectorAll('td span');
                if (cells.length > 0) {
                  this.processNewCells(cells);
                }
              }
            }
          });
        }
      });
    });
    
    // Observer les changements dans le corps du tableau
    if (this.excelBody) {
      observer.observe(this.excelBody, { childList: true, subtree: true });
    }
  }
  
  /**
   * Récupère les valeurs initiales
   */
  getInitialValues() {
    // Récupérer le diviseur et le dividende
    const divisorDisplay = document.getElementById('divisor-display');
    const dividendDisplay = document.getElementById('dividend-display');
    
    if (divisorDisplay) {
      this.divisor = parseInt(divisorDisplay.textContent) || 0;
    }
    
    if (dividendDisplay) {
      this.dividend = parseInt(dividendDisplay.textContent) || 0;
    }
    
    // Compter les opérations existantes
    this.countExistingOperations();
  }
  
  /**
   * Compte les opérations existantes
   */
  countExistingOperations() {
    if (this.excelTable) {
      // Compter les cellules de quotient
      const quotientCells = this.excelTable.querySelectorAll('.quotient-cell');
      // Compter les cellules de multiplication
      const multiplicationCells = this.excelTable.querySelectorAll('.multiplication-cell');
      // Compter les cellules de soustraction
      const subtractionCells = this.excelTable.querySelectorAll('.subtraction-cell');
      // Compter les cellules d'abaissement
      const loweringCells = this.excelTable.querySelectorAll('.lowering-cell');
      
      // Calculer le nombre total d'opérations
      this.operationCount = quotientCells.length + multiplicationCells.length + 
                           subtractionCells.length + loweringCells.length;
      
      // Mettre à jour le compteur d'opérations
      this.updateOperationCount();
    }
  }
  
  /**
   * Traite une nouvelle ligne
   * @param {HTMLElement} row - La nouvelle ligne
   */
  processNewRow(row) {
    // Vérifier si la ligne contient des opérations
    const cells = row.querySelectorAll('td span');
    if (cells.length > 0) {
      this.processNewCells(cells);
    }
  }
  
  /**
   * Traite de nouvelles cellules
   * @param {NodeList} cells - Les nouvelles cellules
   */
  processNewCells(cells) {
    let hasNewOperation = false;
    
    // Parcourir les cellules
    cells.forEach(span => {
      // Vérifier le type de cellule
      if (span.classList.contains('quotient-cell')) {
        this.processQuotientCell(span);
        hasNewOperation = true;
      } else if (span.classList.contains('multiplication-cell')) {
        this.processMultiplicationCell(span);
        hasNewOperation = true;
      } else if (span.classList.contains('subtraction-cell')) {
        this.processSubtractionCell(span);
        hasNewOperation = true;
      } else if (span.classList.contains('lowering-cell')) {
        this.processLoweringCell(span);
        hasNewOperation = true;
      } else if (span.classList.contains('result-cell')) {
        this.processResultCell(span);
      }
    });
    
    // Mettre à jour le compteur si une nouvelle opération a été détectée
    if (hasNewOperation) {
      this.operationCount++;
      this.updateOperationCount();
    }
  }
  
  /**
   * Traite une cellule de quotient
   * @param {HTMLElement} span - La cellule de quotient
   */
  processQuotientCell(span) {
    // Récupérer la valeur du quotient
    const value = span.textContent.trim();
    if (/^[0-9]+$/.test(value)) {
      // Ajouter le chiffre au quotient
      this.quotient += value;
      
      // Mettre à jour la dernière opération
      this.lastOperation = `Ajout du chiffre ${value} au quotient`;
      this.updateLastOperation();
    }
  }
  
  /**
   * Traite une cellule de multiplication
   * @param {HTMLElement} span - La cellule de multiplication
   */
  processMultiplicationCell(span) {
    // Récupérer la valeur de la multiplication
    const value = span.textContent.trim();
    if (/^[0-9]+$/.test(value)) {
      // Mettre à jour la dernière opération
      this.lastOperation = `Multiplication: ${this.divisor} × ${this.quotient.slice(-1)} = ${value}`;
      this.updateLastOperation();
    }
  }
  
  /**
   * Traite une cellule de soustraction
   * @param {HTMLElement} span - La cellule de soustraction
   */
  processSubtractionCell(span) {
    // Récupérer la valeur de la soustraction
    const value = span.textContent.trim();
    if (/^[0-9]+$/.test(value)) {
      // Mettre à jour le reste
      this.remainder = parseInt(value);
      
      // Mettre à jour la dernière opération
      this.lastOperation = `Soustraction: Reste = ${value}`;
      this.updateLastOperation();
    }
  }
  
  /**
   * Traite une cellule d'abaissement
   * @param {HTMLElement} span - La cellule d'abaissement
   */
  processLoweringCell(span) {
    // Récupérer la valeur de l'abaissement
    const value = span.textContent.trim();
    if (/^[0-9]+$/.test(value)) {
      // Mettre à jour le reste
      this.remainder = this.remainder * 10 + parseInt(value);
      
      // Mettre à jour la dernière opération
      this.lastOperation = `Abaissement du chiffre ${value}`;
      this.updateLastOperation();
    }
  }
  
  /**
   * Traite une cellule de résultat
   * @param {HTMLElement} span - La cellule de résultat
   */
  processResultCell(span) {
    // Récupérer la valeur du résultat
    const value = span.textContent.trim();
    if (/^[0-9]+$/.test(value)) {
      // Mettre à jour la dernière opération
      this.lastOperation = `Résultat final: Quotient = ${this.quotient}, Reste = ${value}`;
      this.updateLastOperation();
      
      // Ajouter une vérification
      this.addVerification();
    }
  }
  
  /**
   * Ajoute une vérification du résultat
   */
  addVerification() {
    // Vérifier si la formule est correcte
    const calculatedValue = this.divisor * parseInt(this.quotient) + this.remainder;
    const isCorrect = calculatedValue === this.dividend;
    
    // Créer une nouvelle ligne pour la vérification
    const row = document.createElement('tr');
    row.className = 'verification-row';
    
    // Créer une cellule pour la vérification
    const cell = document.createElement('td');
    cell.colSpan = 10; // Ajuster selon la largeur du tableau
    
    // Créer le contenu de la vérification
    const verificationSpan = document.createElement('span');
    verificationSpan.className = 'verification-cell';
    verificationSpan.innerHTML = `Vérification: ${this.divisor} × ${this.quotient} + ${this.remainder} = ${calculatedValue}`;
    
    if (isCorrect) {
      verificationSpan.innerHTML += ' ✓';
      verificationSpan.style.borderColor = 'var(--success-color)';
    } else {
      verificationSpan.innerHTML += ' ✗';
      verificationSpan.style.borderColor = 'var(--danger-color)';
    }
    
    // Ajouter le span à la cellule
    cell.appendChild(verificationSpan);
    
    // Ajouter la cellule à la ligne
    row.appendChild(cell);
    
    // Ajouter la ligne au tableau
    if (this.excelBody) {
      this.excelBody.appendChild(row);
    }
  }
  
  /**
   * Met à jour le compteur d'opérations
   */
  updateOperationCount() {
    // Mettre à jour le compteur dans l'interface
    const operationCount = document.getElementById('excel-operation-count');
    if (operationCount) {
      operationCount.textContent = `${this.operationCount} opération${this.operationCount !== 1 ? 's' : ''}`;
    }
    
    // Mettre à jour le compteur dans l'enhancer si disponible
    if (window.excelEnhancer) {
      window.excelEnhancer.updateOperationCount(this.operationCount);
    }
  }
  
  /**
   * Met à jour la dernière opération
   */
  updateLastOperation() {
    // Mettre à jour la dernière opération dans l'interface
    const lastOperation = document.getElementById('excel-last-operation');
    if (lastOperation) {
      lastOperation.textContent = this.lastOperation;
    }
    
    // Mettre à jour le statut dans l'enhancer si disponible
    if (window.excelEnhancer) {
      window.excelEnhancer.updateStatus(this.lastOperation);
    }
  }
}

// Initialiser le gestionnaire d'opérations mathématiques lorsque le DOM est chargé
document.addEventListener('DOMContentLoaded', () => {
  // Attendre un peu pour s'assurer que tous les éléments sont chargés
  setTimeout(() => {
    const excelMathOperations = new ExcelMathOperations();
    
    // Exposer l'instance pour une utilisation ultérieure
    window.excelMathOperations = excelMathOperations;
  }, 1500);
});
