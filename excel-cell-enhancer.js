/**
 * Excel Cell Enhancer
 * Ce script améliore l'affichage et le comportement des cellules Excel
 * pour la division euclidienne
 */

class ExcelCellEnhancer {
  constructor() {
    // Éléments DOM
    this.excelTable = document.getElementById('excel-table');
    this.excelBody = document.getElementById('excel-body');
    
    // Initialisation
    this.init();
  }
  
  /**
   * Initialise les améliorations des cellules
   */
  init() {
    // Observer les changements dans le tableau Excel
    this.setupMutationObserver();
    
    // Ajouter les événements
    this.setupEvents();
    
    // Améliorer les cellules existantes
    this.enhanceExistingCells();
  }
  
  /**
   * Configure un observateur de mutations pour détecter les nouvelles cellules
   */
  setupMutationObserver() {
    // Créer un observateur de mutations
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // Parcourir les nœuds ajoutés
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // Si c'est une ligne de tableau
              if (node.tagName === 'TR') {
                this.enhanceRow(node);
              }
              // Si c'est une cellule
              else if (node.tagName === 'TD') {
                this.enhanceCell(node);
              }
              // Rechercher les cellules à l'intérieur du nœud ajouté
              else {
                const cells = node.querySelectorAll('td');
                cells.forEach(cell => this.enhanceCell(cell));
              }
            }
          });
        }
      });
    });
    
    // Observer les changements dans le corps du tableau
    if (this.excelBody) {
      observer.observe(this.excelBody, { childList: true, subtree: true });
    }
  }
  
  /**
   * Configure les événements pour les cellules
   */
  setupEvents() {
    // Ajouter un événement de survol pour les cellules
    if (this.excelTable) {
      this.excelTable.addEventListener('mouseover', (e) => {
        const cell = e.target.closest('td');
        if (cell) {
          this.highlightRelatedCells(cell);
        }
      });
      
      this.excelTable.addEventListener('mouseout', (e) => {
        const cell = e.target.closest('td');
        if (cell) {
          this.removeHighlightFromRelatedCells(cell);
        }
      });
      
      // Ajouter un événement de clic pour les cellules
      this.excelTable.addEventListener('click', (e) => {
        const cell = e.target.closest('td');
        if (cell) {
          this.showCellDetails(cell);
        }
      });
    }
  }
  
  /**
   * Améliore les cellules existantes
   */
  enhanceExistingCells() {
    if (this.excelTable) {
      // Améliorer toutes les lignes
      const rows = this.excelTable.querySelectorAll('tbody tr');
      rows.forEach(row => this.enhanceRow(row));
      
      // Améliorer toutes les cellules
      const cells = this.excelTable.querySelectorAll('td');
      cells.forEach(cell => this.enhanceCell(cell));
    }
  }
  
  /**
   * Améliore une ligne du tableau
   * @param {HTMLElement} row - La ligne à améliorer
   */
  enhanceRow(row) {
    // Identifier les opérations dans la ligne
    this.identifyOperationsInRow(row);
    
    // Ajouter des lignes de séparation si nécessaire
    this.addSeparationLines(row);
  }
  
  /**
   * Identifie les opérations dans une ligne
   * @param {HTMLElement} row - La ligne à analyser
   */
  identifyOperationsInRow(row) {
    // Compter les différents types de cellules
    let hasMultiplication = false;
    let hasSubtraction = false;
    let hasQuotient = false;
    let hasLowering = false;
    
    // Parcourir les cellules de la ligne
    const cells = row.querySelectorAll('td');
    cells.forEach(cell => {
      if (cell.querySelector('.multiplication-cell')) hasMultiplication = true;
      if (cell.querySelector('.subtraction-cell')) hasSubtraction = true;
      if (cell.querySelector('.quotient-cell')) hasQuotient = true;
      if (cell.querySelector('.lowering-cell')) hasLowering = true;
    });
    
    // Ajouter des classes à la ligne en fonction des opérations
    if (hasMultiplication) row.classList.add('multiplication-row');
    if (hasSubtraction) row.classList.add('subtraction-row');
    if (hasQuotient) row.classList.add('quotient-row');
    if (hasLowering) row.classList.add('lowering-row');
  }
  
  /**
   * Ajoute des lignes de séparation si nécessaire
   * @param {HTMLElement} row - La ligne à améliorer
   */
  addSeparationLines(row) {
    // Ajouter une ligne horizontale après une soustraction
    if (row.classList.contains('subtraction-row')) {
      const nextRow = row.nextElementSibling;
      if (nextRow) {
        const line = document.createElement('div');
        line.className = 'excel-horizontal-line';
        nextRow.style.position = 'relative';
        nextRow.appendChild(line);
      }
    }
  }
  
  /**
   * Améliore une cellule du tableau
   * @param {HTMLElement} cell - La cellule à améliorer
   */
  enhanceCell(cell) {
    // Vérifier si la cellule a déjà été améliorée
    if (cell.dataset.enhanced === 'true') return;
    
    // Améliorer l'apparence de la cellule
    this.enhanceCellAppearance(cell);
    
    // Ajouter des opérateurs mathématiques si nécessaire
    this.addMathOperators(cell);
    
    // Ajouter des étiquettes d'opération
    this.addOperationLabels(cell);
    
    // Marquer la cellule comme améliorée
    cell.dataset.enhanced = 'true';
  }
  
  /**
   * Améliore l'apparence d'une cellule
   * @param {HTMLElement} cell - La cellule à améliorer
   */
  enhanceCellAppearance(cell) {
    // Vérifier si la cellule contient un chiffre
    const content = cell.textContent.trim();
    if (/^[0-9]+$/.test(content) && content.length > 1) {
      // Marquer les cellules avec plusieurs chiffres
      cell.classList.add('multi-digit');
    }
    
    // Améliorer les spans à l'intérieur de la cellule
    const spans = cell.querySelectorAll('span');
    spans.forEach(span => {
      // Supprimer le mot "existe" s'il est présent
      if (span.textContent.includes('existe')) {
        span.textContent = span.textContent.replace('existe', '').trim();
      }
      
      // Ajouter des classes en fonction du contenu
      if (span.classList.contains('quotient-cell')) {
        this.enhanceQuotientCell(span);
      } else if (span.classList.contains('multiplication-cell')) {
        this.enhanceMultiplicationCell(span);
      } else if (span.classList.contains('subtraction-cell')) {
        this.enhanceSubtractionCell(span);
      } else if (span.classList.contains('lowering-cell')) {
        this.enhanceLoweringCell(span);
      } else if (span.classList.contains('result-cell')) {
        this.enhanceResultCell(span);
      }
    });
  }
  
  /**
   * Améliore une cellule de quotient
   * @param {HTMLElement} span - Le span à améliorer
   */
  enhanceQuotientCell(span) {
    // Ajouter un indice "q" pour indiquer le quotient
    const content = span.textContent.trim();
    if (/^[0-9]+$/.test(content)) {
      span.innerHTML = `${content}<sub>q</sub>`;
    }
  }
  
  /**
   * Améliore une cellule de multiplication
   * @param {HTMLElement} span - Le span à améliorer
   */
  enhanceMultiplicationCell(span) {
    // Rien de spécial pour l'instant
  }
  
  /**
   * Améliore une cellule de soustraction
   * @param {HTMLElement} span - Le span à améliorer
   */
  enhanceSubtractionCell(span) {
    // Rien de spécial pour l'instant
  }
  
  /**
   * Améliore une cellule d'abaissement
   * @param {HTMLElement} span - Le span à améliorer
   */
  enhanceLoweringCell(span) {
    // Ajouter une flèche d'abaissement si elle n'existe pas déjà
    if (!span.querySelector('.arrow-indicator')) {
      const arrow = document.createElement('div');
      arrow.className = 'arrow-indicator';
      arrow.innerHTML = '↓';
      arrow.style.position = 'absolute';
      arrow.style.top = '-15px';
      arrow.style.left = '50%';
      arrow.style.transform = 'translateX(-50%)';
      arrow.style.color = 'var(--warning-color)';
      arrow.style.fontSize = '1.2rem';
      arrow.style.opacity = '0';
      arrow.style.transition = 'opacity 0.3s ease';
      
      span.style.position = 'relative';
      span.appendChild(arrow);
      
      // Afficher la flèche au survol
      span.addEventListener('mouseover', () => {
        arrow.style.opacity = '1';
      });
      
      span.addEventListener('mouseout', () => {
        arrow.style.opacity = '0';
      });
    }
  }
  
  /**
   * Améliore une cellule de résultat
   * @param {HTMLElement} span - Le span à améliorer
   */
  enhanceResultCell(span) {
    // Ajouter un effet de pulsation pour mettre en évidence le résultat
    span.style.animation = 'pulse 2s infinite';
    
    // Ajouter une animation de pulsation si elle n'existe pas déjà
    if (!document.querySelector('#pulse-animation')) {
      const style = document.createElement('style');
      style.id = 'pulse-animation';
      style.textContent = `
        @keyframes pulse {
          0% { transform: scale(1); }
          50% { transform: scale(1.05); }
          100% { transform: scale(1); }
        }
      `;
      document.head.appendChild(style);
    }
  }
  
  /**
   * Ajoute des opérateurs mathématiques si nécessaire
   * @param {HTMLElement} cell - La cellule à améliorer
   */
  addMathOperators(cell) {
    // Vérifier le type de cellule
    if (cell.querySelector('.multiplication-cell')) {
      // Ajouter un opérateur de multiplication
      this.addOperator(cell, '×', 'multiply');
    } else if (cell.querySelector('.subtraction-cell')) {
      // Ajouter un opérateur de soustraction
      this.addOperator(cell, '−', 'minus');
    } else if (cell.querySelector('.result-cell')) {
      // Ajouter un opérateur d'égalité
      this.addOperator(cell, '=', 'equals');
    }
  }
  
  /**
   * Ajoute un opérateur mathématique à une cellule
   * @param {HTMLElement} cell - La cellule
   * @param {string} symbol - Le symbole de l'opérateur
   * @param {string} type - Le type d'opérateur
   */
  addOperator(cell, symbol, type) {
    // Vérifier si l'opérateur existe déjà
    if (cell.querySelector('.operator')) return;
    
    // Créer l'opérateur
    const operator = document.createElement('span');
    operator.className = `operator ${type}`;
    operator.textContent = symbol;
    
    // Positionner l'opérateur
    operator.style.left = '-12px';
    operator.style.top = '50%';
    operator.style.transform = 'translateY(-50%)';
    
    // Ajouter l'opérateur à la cellule
    cell.style.position = 'relative';
    cell.appendChild(operator);
  }
  
  /**
   * Ajoute des étiquettes d'opération
   * @param {HTMLElement} cell - La cellule à améliorer
   */
  addOperationLabels(cell) {
    // Vérifier le type de cellule
    if (cell.querySelector('.quotient-cell')) {
      this.addLabel(cell, 'Quotient');
    } else if (cell.querySelector('.multiplication-cell')) {
      this.addLabel(cell, 'Multiplication');
    } else if (cell.querySelector('.subtraction-cell')) {
      this.addLabel(cell, 'Soustraction');
    } else if (cell.querySelector('.lowering-cell')) {
      this.addLabel(cell, 'Abaissement');
    } else if (cell.querySelector('.result-cell')) {
      this.addLabel(cell, 'Résultat');
    }
  }
  
  /**
   * Ajoute une étiquette à une cellule
   * @param {HTMLElement} cell - La cellule
   * @param {string} text - Le texte de l'étiquette
   */
  addLabel(cell, text) {
    // Vérifier si l'étiquette existe déjà
    if (cell.querySelector('.cell-label')) return;
    
    // Créer l'étiquette
    const label = document.createElement('span');
    label.className = 'cell-label';
    label.textContent = text;
    label.style.position = 'absolute';
    label.style.top = '-20px';
    label.style.left = '50%';
    label.style.transform = 'translateX(-50%)';
    label.style.fontSize = '0.7rem';
    label.style.backgroundColor = 'var(--card-background)';
    label.style.padding = '2px 5px';
    label.style.borderRadius = '3px';
    label.style.color = 'var(--text-muted)';
    label.style.whiteSpace = 'nowrap';
    label.style.opacity = '0';
    label.style.transition = 'opacity 0.3s ease';
    label.style.zIndex = '10';
    
    // Ajouter l'étiquette à la cellule
    cell.style.position = 'relative';
    cell.appendChild(label);
    
    // Afficher l'étiquette au survol
    cell.addEventListener('mouseover', () => {
      label.style.opacity = '1';
    });
    
    cell.addEventListener('mouseout', () => {
      label.style.opacity = '0';
    });
  }
  
  /**
   * Met en évidence les cellules liées
   * @param {HTMLElement} cell - La cellule survolée
   */
  highlightRelatedCells(cell) {
    // Vérifier le type de cellule
    if (cell.querySelector('.quotient-cell')) {
      // Mettre en évidence les cellules liées au quotient
      this.highlightCellsByClass('quotient-cell');
    } else if (cell.querySelector('.multiplication-cell')) {
      // Mettre en évidence les cellules liées à la multiplication
      this.highlightCellsByClass('multiplication-cell');
    } else if (cell.querySelector('.subtraction-cell')) {
      // Mettre en évidence les cellules liées à la soustraction
      this.highlightCellsByClass('subtraction-cell');
    } else if (cell.querySelector('.lowering-cell')) {
      // Mettre en évidence les cellules liées à l'abaissement
      this.highlightCellsByClass('lowering-cell');
    } else if (cell.querySelector('.result-cell')) {
      // Mettre en évidence les cellules liées au résultat
      this.highlightCellsByClass('result-cell');
    }
  }
  
  /**
   * Met en évidence les cellules par classe
   * @param {string} className - La classe des cellules à mettre en évidence
   */
  highlightCellsByClass(className) {
    if (this.excelTable) {
      const cells = this.excelTable.querySelectorAll(`td span.${className}`);
      cells.forEach(span => {
        const cell = span.closest('td');
        cell.style.boxShadow = '0 0 0 2px var(--primary-color)';
        cell.style.zIndex = '5';
      });
    }
  }
  
  /**
   * Supprime la mise en évidence des cellules liées
   * @param {HTMLElement} cell - La cellule quittée
   */
  removeHighlightFromRelatedCells(cell) {
    if (this.excelTable) {
      const cells = this.excelTable.querySelectorAll('td');
      cells.forEach(cell => {
        cell.style.boxShadow = '';
        cell.style.zIndex = '';
      });
    }
  }
  
  /**
   * Affiche les détails d'une cellule
   * @param {HTMLElement} cell - La cellule cliquée
   */
  showCellDetails(cell) {
    // Vérifier si la cellule contient un span
    const span = cell.querySelector('span');
    if (!span) return;
    
    // Déterminer le type de cellule
    let type = '';
    let value = span.textContent.trim();
    
    if (span.classList.contains('quotient-cell')) {
      type = 'Quotient';
      // Supprimer l'indice "q" s'il existe
      value = value.replace(/q/g, '');
    } else if (span.classList.contains('multiplication-cell')) {
      type = 'Multiplication';
    } else if (span.classList.contains('subtraction-cell')) {
      type = 'Soustraction';
    } else if (span.classList.contains('lowering-cell')) {
      type = 'Abaissement';
    } else if (span.classList.contains('result-cell')) {
      type = 'Résultat';
    } else if (span.classList.contains('divisor-value')) {
      type = 'Diviseur';
    } else if (span.classList.contains('dividend-value')) {
      type = 'Dividende';
    } else {
      type = 'Valeur';
    }
    
    // Afficher les détails dans une notification toast
    if (window.excelEnhancer) {
      window.excelEnhancer.showToast(`${type}: ${value}`, 'info');
    } else {
      // Fallback si excelEnhancer n'est pas disponible
      alert(`${type}: ${value}`);
    }
  }
}

// Initialiser l'améliorateur de cellules Excel lorsque le DOM est chargé
document.addEventListener('DOMContentLoaded', () => {
  // Attendre un peu pour s'assurer que tous les éléments sont chargés
  setTimeout(() => {
    const excelCellEnhancer = new ExcelCellEnhancer();
    
    // Exposer l'instance pour une utilisation ultérieure
    window.excelCellEnhancer = excelCellEnhancer;
  }, 1000);
});
