{"name": "smartdiv", "version": "1.3.6", "description": "Application web éducative interactive pour apprendre la division euclidienne étape par étape", "main": "index.html", "scripts": {"start": "npx serve . -p 8000", "dev": "npx serve . -p 3000", "build": "echo 'No build process needed for vanilla JS app'", "test": "echo 'No tests specified'", "lint": "npx eslint js/**/*.js", "format": "npx prettier --write .", "serve": "npx serve .", "preview": "npx serve . -p 4173"}, "keywords": ["education", "mathematics", "division", "euclidean", "interactive", "learning", "pwa", "accessibility", "responsive", "vanilla-js"], "author": {"name": "Ali <PERSON>", "email": "<EMAIL>"}, "license": "UNLICENSED", "private": true, "repository": {"type": "git", "url": "https://github.com/ali-messaoudi/smartdiv.git"}, "bugs": {"url": "https://github.com/ali-messaoudi/smartdiv/issues", "email": "<EMAIL>"}, "homepage": "https://smartdiv.edu", "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "devDependencies": {"eslint": "^8.0.0", "prettier": "^2.0.0", "serve": "^14.0.0"}, "pwa": {"name": "smartDiv", "short_name": "smartDiv", "description": "Division Euclidienne Interactive", "theme_color": "#3498db", "background_color": "#f4f6f7", "display": "standalone", "orientation": "any", "start_url": "index.html", "scope": "/"}, "categories": ["education", "mathematics", "learning"], "screenshots": [{"src": "screenshots/screenshot1.svg", "sizes": "1280x720", "type": "image/svg+xml", "platform": "wide"}], "features": ["Interactive step-by-step division learning", "Multiple division types support", "Excel-like calculation table", "Student and teacher dashboards", "PWA with offline support", "WCAG 2.1 AAA accessibility", "Responsive design", "Dark/light theme", "Multi-language support"]}