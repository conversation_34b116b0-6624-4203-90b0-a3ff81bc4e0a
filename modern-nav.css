/**
 * modern-nav.css
 *
 * Copyright (c) 2023 Ali MESSAOUDI
 * Tous droits réservés.
 * Ce code est protégé par les lois sur le droit d'auteur et ne peut être reproduit,
 * distribué ou utilisé sans autorisation explicite de l'auteur.
 */

/* Styles modernes pour la navigation principale */

/* Variables spécifiques à la navigation */
:root {
  --nav-bg: rgba(0, 0, 0, 0.15);
  --nav-hover-bg: rgba(255, 255, 255, 0.15);
  --nav-active-bg: rgba(255, 255, 255, 0.2);
  --nav-active-border: #ffffff;
  --nav-text: #ffffff;
  --nav-text-hover: #ffffff;
  --nav-text-active: #ffffff;
  --nav-transition: 0.3s ease;
  --nav-indicator-size: 3px;
  --nav-dropdown-bg: #ffffff;
  --nav-dropdown-text: var(--text-color);
  --nav-dropdown-hover: var(--primary-color-light);
  --nav-dropdown-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  --nav-mobile-breakpoint: 768px;
  --nav-mobile-menu-bg: var(--primary-color);
  --nav-mobile-menu-text: #ffffff;
  --nav-mobile-icon-size: 24px;
  --nav-item-spacing: 0.5rem;
  --nav-height: 50px;
}

/* Mode sombre */
body.dark-theme {
  --nav-bg: rgba(0, 0, 0, 0.3);
  --nav-hover-bg: rgba(255, 255, 255, 0.1);
  --nav-active-bg: rgba(255, 255, 255, 0.15);
  --nav-dropdown-bg: #2a2a2a;
  --nav-dropdown-text: #e1e1e1;
  --nav-dropdown-hover: #3a3a3a;
  --nav-dropdown-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Navigation principale */
.main-nav {
  background-color: var(--nav-bg);
  position: relative;
  width: 100%;
  height: var(--nav-height);
  z-index: 100;
  transition: all var(--nav-transition);
  overflow: visible;
}

.main-nav-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Liste de navigation */
.main-nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  height: 100%;
  align-items: center;
  flex-wrap: nowrap;
}

/* Éléments de navigation */
.main-nav li {
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
  margin: 0 var(--nav-item-spacing);
}

/* Liens de navigation */
.main-nav a {
  display: flex;
  align-items: center;
  padding: 0 1rem;
  color: var(--nav-text);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  height: 100%;
  position: relative;
  transition: all var(--nav-transition);
  white-space: nowrap;
}

/* Icônes dans les liens */
.main-nav a i {
  margin-right: 0.5rem;
  font-size: 1rem;
  transition: transform var(--nav-transition);
}

/* Effet de survol */
.main-nav a:hover {
  color: var(--nav-text-hover);
  background-color: var(--nav-hover-bg);
}

.main-nav a:hover i {
  transform: translateY(-2px);
}

/* Lien actif */
.main-nav a.active {
  color: var(--nav-text-active);
  background-color: var(--nav-active-bg);
  font-weight: 600;
}

/* Indicateur de lien actif */
.main-nav a.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: var(--nav-indicator-size);
  background-color: var(--nav-active-border);
  border-radius: var(--nav-indicator-size) var(--nav-indicator-size) 0 0;
  transition: transform 0.3s ease;
}

/* Animation d'entrée pour l'indicateur */
.main-nav a.active::after {
  animation: slideIn 0.3s ease forwards;
}

@keyframes slideIn {
  from {
    transform: scaleX(0);
  }
  to {
    transform: scaleX(1);
  }
}

/* Groupe d'authentification */
.auth-nav-group {
  display: flex;
  align-items: center;
  margin-left: auto;
  height: 100%;
}

/* Éléments d'authentification */
.auth-nav {
  height: 100%;
  display: flex;
  align-items: center;
}

/* Liens de connexion et d'inscription */
.login-nav a,
.register-nav a {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: all var(--nav-transition);
  font-weight: 500;
  font-size: 0.9rem;
  height: auto;
  margin: 0 0.25rem;
}

.login-nav a {
  color: var(--nav-text);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.register-nav a {
  background-color: var(--primary-color-light);
  color: white;
  border: 1px solid var(--primary-color-light);
}

.login-nav a:hover {
  background-color: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.register-nav a:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

/* Navigation utilisateur */
.user-nav {
  position: relative;
  height: 100%;
}

.user-nav > a {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 1rem;
  height: 100%;
  transition: all var(--nav-transition);
}

.user-nav > a:hover {
  background-color: var(--nav-hover-bg);
}

/* Menu déroulant utilisateur */
.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: var(--nav-dropdown-bg);
  border-radius: 8px;
  box-shadow: var(--nav-dropdown-shadow);
  min-width: 200px;
  overflow: hidden;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all var(--nav-transition);
  z-index: 200;
}

.user-nav:hover .user-dropdown,
.user-dropdown:hover {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.user-dropdown-header {
  padding: 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-color-light);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 600;
  color: var(--nav-dropdown-text);
}

.user-role {
  font-size: 0.8rem;
  color: var(--text-muted);
}

.user-dropdown-menu {
  list-style: none;
  padding: 0.5rem 0;
  margin: 0;
}

.user-dropdown-menu li a {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: var(--nav-dropdown-text);
  text-decoration: none;
  transition: all var(--nav-transition);
  font-weight: normal;
}

.user-dropdown-menu li a:hover {
  background-color: var(--nav-dropdown-hover);
}

.user-dropdown-menu li a i {
  margin-right: 0.75rem;
  width: 20px;
  text-align: center;
}

.user-dropdown-divider {
  height: 1px;
  background-color: rgba(0, 0, 0, 0.1);
  margin: 0.5rem 0;
}

/* Bouton de menu mobile */
.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  color: var(--nav-text);
  font-size: var(--nav-mobile-icon-size);
  cursor: pointer;
  padding: 0.5rem;
  z-index: 300;
}

/* Responsive */
@media (max-width: var(--nav-mobile-breakpoint)) {
  .main-nav {
    height: auto;
    padding: 0;
  }

  .main-nav-container {
    padding: 0.5rem 1rem;
    flex-wrap: wrap;
  }

  .mobile-menu-toggle {
    display: block;
  }

  .main-nav ul {
    flex-direction: column;
    width: 100%;
    height: auto;
    display: none;
    padding: 1rem 0;
  }

  .main-nav.mobile-open ul {
    display: flex;
  }

  .main-nav li {
    width: 100%;
    height: auto;
    margin: 0;
  }

  .main-nav a {
    width: 100%;
    padding: 0.75rem 1rem;
    height: auto;
  }

  .main-nav a.active::after {
    display: none;
  }

  .main-nav a.active {
    background-color: var(--nav-active-bg);
    border-radius: 4px;
  }

  .auth-nav-group {
    width: 100%;
    margin-left: 0;
    flex-direction: column;
    align-items: flex-start;
  }

  .auth-nav {
    width: 100%;
    margin: 0.5rem 0;
  }

  .login-nav a,
  .register-nav a {
    width: 100%;
    justify-content: center;
    margin: 0.25rem 0;
  }

  .user-nav {
    width: 100%;
  }

  .user-nav > a {
    width: 100%;
    justify-content: flex-start;
  }

  .user-dropdown {
    position: static;
    width: 100%;
    box-shadow: none;
    border-radius: 0;
    margin-top: 0.5rem;
  }

  .user-dropdown-header {
    padding: 0.75rem 1rem;
  }
}

/* Animation du bouton de menu mobile */
.hamburger {
  width: 24px;
  height: 18px;
  position: relative;
  transform: rotate(0deg);
  transition: 0.5s ease-in-out;
  cursor: pointer;
}

.hamburger span {
  display: block;
  position: absolute;
  height: 2px;
  width: 100%;
  background: var(--nav-text);
  border-radius: 2px;
  opacity: 1;
  left: 0;
  transform: rotate(0deg);
  transition: 0.25s ease-in-out;
}

.hamburger span:nth-child(1) {
  top: 0px;
}

.hamburger span:nth-child(2),
.hamburger span:nth-child(3) {
  top: 8px;
}

.hamburger span:nth-child(4) {
  top: 16px;
}

.mobile-open .hamburger span:nth-child(1) {
  top: 8px;
  width: 0%;
  left: 50%;
}

.mobile-open .hamburger span:nth-child(2) {
  transform: rotate(45deg);
}

.mobile-open .hamburger span:nth-child(3) {
  transform: rotate(-45deg);
}

.mobile-open .hamburger span:nth-child(4) {
  top: 8px;
  width: 0%;
  left: 50%;
}

/* Classe pour masquer des éléments */
.hidden {
  display: none !important;
}
