/**
 * Script pour gérer les boutons d'en-tête du tableau Excel
 */
document.addEventListener('DOMContentLoaded', function() {
  // Récupérer les boutons d'en-tête
  const fullscreenExcelHeaderBtn = document.getElementById('fullscreen-excel-header');
  const printExcelHeaderBtn = document.getElementById('print-excel-header');
  const exportExcelHeaderBtn = document.getElementById('export-excel-header');
  
  // Récupérer les boutons existants pour réutiliser leurs fonctionnalités
  const fullscreenExcelBtn = document.getElementById('fullscreen-excel');
  const printExcelBtn = document.getElementById('print-excel');
  
  // Ajouter les écouteurs d'événements pour les nouveaux boutons
  if (fullscreenExcelHeaderBtn && fullscreenExcelBtn) {
    fullscreenExcelHeaderBtn.addEventListener('click', function() {
      // Simuler un clic sur le bouton existant
      fullscreenExcelBtn.click();
    });
  }
  
  if (printExcelHeaderBtn && printExcelBtn) {
    printExcelHeaderBtn.addEventListener('click', function() {
      // Simuler un clic sur le bouton existant
      printExcelBtn.click();
    });
  }
  
  if (exportExcelHeaderBtn) {
    exportExcelHeaderBtn.addEventListener('click', function() {
      // Fonction pour exporter le tableau en image
      exportTableAsImage();
    });
  }
  
  /**
   * Fonction pour exporter le tableau en image
   */
  function exportTableAsImage() {
    const excelTable = document.getElementById('excel-table');
    
    if (!excelTable) {
      console.error("Tableau non trouvé");
      return;
    }
    
    // Utiliser html2canvas pour capturer le tableau
    if (typeof html2canvas === 'undefined') {
      // Si html2canvas n'est pas chargé, charger la bibliothèque
      const script = document.createElement('script');
      script.src = 'https://html2canvas.hertzen.com/dist/html2canvas.min.js';
      script.onload = function() {
        captureAndDownload();
      };
      document.head.appendChild(script);
    } else {
      captureAndDownload();
    }
    
    function captureAndDownload() {
      // Ajouter une classe temporaire pour le style d'exportation
      excelTable.classList.add('exporting');
      
      html2canvas(excelTable).then(function(canvas) {
        // Retirer la classe temporaire
        excelTable.classList.remove('exporting');
        
        // Convertir le canvas en URL de données
        const imageData = canvas.toDataURL('image/png');
        
        // Créer un lien de téléchargement
        const link = document.createElement('a');
        link.href = imageData;
        link.download = 'tableau-division-' + new Date().toISOString().slice(0, 10) + '.png';
        link.click();
      });
    }
  }
});
