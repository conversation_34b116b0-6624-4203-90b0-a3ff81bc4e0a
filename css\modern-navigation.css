/* 
 * smartDiv - Navigation moderne
 * Ce fichier contient les styles pour la navigation modernisée
 */

/* ===== HEADER ===== */
header {
  background-color: var(--color-background);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: all var(--transition-normal);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-sm);
}

.logo-image {
  height: 60px;
  width: auto;
  transition: transform var(--transition-fast);
}

.logo-image:hover {
  transform: scale(1.05);
}

.header-controls {
  display: flex;
  gap: var(--space-sm);
}

/* ===== NAVIGATION PRINCIPALE ===== */
.main-nav {
  background-color: var(--color-primary);
  color: white;
}

.main-nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-sm) 0;
}

.main-nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: var(--space-md);
}

.main-nav a {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.main-nav a:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
  text-decoration: none;
}

.main-nav a.active {
  color: white;
  background-color: rgba(255, 255, 255, 0.2);
  font-weight: var(--font-weight-semibold);
}

.main-nav a i {
  font-size: var(--font-size-md);
}

/* ===== NAVIGATION D'AUTHENTIFICATION ===== */
.auth-nav-group {
  display: flex;
  gap: var(--space-sm);
}

.nav-button {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.nav-button:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

#nav-login {
  background-color: transparent;
  border: 1px solid rgba(255, 255, 255, 0.5);
}

#nav-register {
  background-color: var(--color-accent);
}

#nav-register:hover {
  background-color: var(--color-accent-dark);
}

/* ===== MENU UTILISATEUR ===== */
.user-nav {
  position: relative;
}

.user-nav > a {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  background-color: rgba(255, 255, 255, 0.1);
  transition: all var(--transition-fast);
}

.user-nav > a:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 240px;
  background-color: var(--color-background);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  margin-top: var(--space-xs);
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all var(--transition-fast);
  z-index: 1000;
  overflow: hidden;
}

.user-nav:hover .user-dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.user-dropdown-header {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-md);
  background-color: var(--color-primary-light);
  color: white;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--color-primary-dark);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
}

.user-name {
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-xs);
}

.user-role {
  font-size: var(--font-size-sm);
  opacity: 0.8;
}

.user-dropdown-menu {
  list-style: none;
  margin: 0;
  padding: var(--space-xs) 0;
}

.user-dropdown-menu a {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  color: var(--color-text-primary);
  transition: all var(--transition-fast);
}

.user-dropdown-menu a:hover {
  background-color: var(--color-background-light);
  color: var(--color-primary);
  text-decoration: none;
}

.user-dropdown-divider {
  height: 1px;
  background-color: var(--color-border);
  margin: var(--space-xs) 0;
}

/* ===== MENU MOBILE ===== */
.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-xs);
}

.hamburger {
  width: 24px;
  height: 18px;
  position: relative;
  transform: rotate(0deg);
  transition: .5s ease-in-out;
}

.hamburger span {
  display: block;
  position: absolute;
  height: 3px;
  width: 100%;
  background: white;
  border-radius: 3px;
  opacity: 1;
  left: 0;
  transform: rotate(0deg);
  transition: .25s ease-in-out;
}

.hamburger span:nth-child(1) {
  top: 0px;
}

.hamburger span:nth-child(2),
.hamburger span:nth-child(3) {
  top: 8px;
}

.hamburger span:nth-child(4) {
  top: 16px;
}

.mobile-menu-toggle.active .hamburger span:nth-child(1) {
  top: 8px;
  width: 0%;
  left: 50%;
}

.mobile-menu-toggle.active .hamburger span:nth-child(2) {
  transform: rotate(45deg);
}

.mobile-menu-toggle.active .hamburger span:nth-child(3) {
  transform: rotate(-45deg);
}

.mobile-menu-toggle.active .hamburger span:nth-child(4) {
  top: 8px;
  width: 0%;
  left: 50%;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
  .mobile-menu-toggle {
    display: block;
  }
  
  .main-nav-container {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .main-nav ul {
    flex-direction: column;
    width: 100%;
    display: none;
  }
  
  .main-nav ul.active {
    display: flex;
  }
  
  .main-nav a {
    padding: var(--space-md);
    width: 100%;
  }
  
  .auth-nav-group {
    width: 100%;
    flex-direction: column;
    display: none;
  }
  
  .auth-nav-group.active {
    display: flex;
  }
  
  .nav-button {
    width: 100%;
    justify-content: center;
  }
  
  .user-dropdown {
    position: static;
    width: 100%;
    box-shadow: none;
    margin-top: 0;
  }
}
