<!DOCTYPE html>
<!--
  Copyright (c) 2023 Ali MESSAOUDI
  Tous droits réservés.
  Ce code est protégé par les lois sur le droit d'auteur et ne peut être reproduit,
  distribué ou utilisé sans autorisation explicite de l'auteur.
-->
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      Tableau de bord enseignant - Simulation de Division Euclidienne
    </title>
    <link rel="stylesheet" href="styles.css" />
    <link rel="stylesheet" href="auth-styles.css" />
    <link rel="stylesheet" href="dashboard-styles.css" />
    <link rel="stylesheet" href="modern-nav.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <header>
      <div class="header-content">
        <h1>
          <a href="index.html" class="logo-link">
            <img
              src="images/logo.jpg"
              alt="Logo smartDiv"
              class="logo-image"
              width="80"
              height="80"
            />
            smartDiv
          </a>
        </h1>
        <div class="header-controls">
          <div id="user-section" class="user-section">
            <button
              id="user-button"
              class="btn-auth"
              aria-label="Menu utilisateur"
              title="Menu utilisateur"
            >
              <i class="fas fa-user-circle" aria-hidden="true"></i>
              <span id="user-name-display">Utilisateur</span>
            </button>
            <div id="user-menu" class="user-menu hidden">
              <div class="user-info">
                <div class="user-avatar">
                  <i class="fas fa-user-circle"></i>
                </div>
                <div class="user-details">
                  <span id="user-name">Nom d'utilisateur</span>
                  <span id="user-role" class="user-role">Enseignant</span>
                </div>
              </div>
              <ul class="user-actions">
                <li>
                  <a href="teacher-dashboard.html" id="dashboard-link"
                    ><i class="fas fa-tachometer-alt"></i> Tableau de bord</a
                  >
                </li>
                <li>
                  <a href="#" id="profile-link"
                    ><i class="fas fa-id-card"></i> Mon profil</a
                  >
                </li>
                <li>
                  <a href="#" id="logout-link"
                    ><i class="fas fa-sign-out-alt"></i> Déconnexion</a
                  >
                </li>
              </ul>
            </div>
          </div>
          <button
            id="theme-toggle"
            class="btn-icon"
            aria-label="Changer de thème"
            title="Changer de thème"
          >
            <i class="fas fa-moon" aria-hidden="true"></i>
            <span class="visually-hidden">Mode sombre</span>
          </button>
        </div>
      </div>
      <nav class="main-nav" aria-label="Navigation principale">
        <div class="main-nav-container">
          <button
            class="mobile-menu-toggle"
            aria-label="Menu"
            aria-expanded="false"
            id="mobile-menu-toggle"
          >
            <div class="hamburger">
              <span></span>
              <span></span>
              <span></span>
              <span></span>
            </div>
          </button>

          <ul>
            <li>
              <a href="index.html"><i class="fas fa-calculator"></i> Accueil</a>
            </li>
            <li>
              <a
                href="teacher-dashboard.html"
                class="active"
                aria-current="page"
              >
                <i class="fas fa-tachometer-alt"></i> Tableau de bord
              </a>
            </li>
            <li>
              <a href="#" id="nav-students"
                ><i class="fas fa-user-graduate"></i> Mes élèves</a
              >
            </li>
            <li>
              <a href="#" id="nav-exercises"
                ><i class="fas fa-tasks"></i> Exercices</a
              >
            </li>
            <li>
              <a href="#" id="nav-stats"
                ><i class="fas fa-chart-bar"></i> Statistiques</a
              >
            </li>
          </ul>
        </div>
      </nav>
    </header>

    <main class="dashboard-main">
      <div class="dashboard-container">
        <div class="dashboard-header">
          <h2>Tableau de bord enseignant</h2>
          <p class="welcome-message">
            Bienvenue, <span id="welcome-name">Enseignant</span> !
          </p>
        </div>

        <div class="dashboard-stats">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-users"></i>
            </div>
            <div class="stat-content">
              <h3>Élèves actifs</h3>
              <p class="stat-value">24</p>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-tasks"></i>
            </div>
            <div class="stat-content">
              <h3>Exercices créés</h3>
              <p class="stat-value">15</p>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            <div class="stat-content">
              <h3>Taux de réussite moyen</h3>
              <p class="stat-value">78%</p>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-clock"></i>
            </div>
            <div class="stat-content">
              <h3>Temps d'activité</h3>
              <p class="stat-value">12h 30min</p>
            </div>
          </div>
        </div>

        <div class="dashboard-actions">
          <button class="btn-primary">
            <i class="fas fa-plus"></i> Créer un exercice
          </button>
          <button class="btn-primary">
            <i class="fas fa-user-plus"></i> Ajouter un élève
          </button>
          <button class="btn-primary">
            <i class="fas fa-file-export"></i> Exporter les données
          </button>
        </div>

        <div class="dashboard-sections">
          <div class="dashboard-section">
            <div class="section-header">
              <h3>Élèves récemment actifs</h3>
              <a href="#" class="section-link">Voir tous les élèves</a>
            </div>
            <div class="section-content">
              <div class="students-list">
                <div class="student-item">
                  <div class="student-avatar">
                    <i class="fas fa-user-graduate"></i>
                  </div>
                  <div class="student-details">
                    <h4>Emma Martin</h4>
                    <p>CM2</p>
                    <div class="student-meta">
                      <span class="student-activity"
                        >Dernière activité: Aujourd'hui</span
                      >
                      <span class="student-progress">Progression: 85%</span>
                    </div>
                  </div>
                  <div class="student-actions">
                    <button class="btn-icon" title="Voir le profil">
                      <i class="fas fa-eye"></i>
                    </button>
                  </div>
                </div>

                <div class="student-item">
                  <div class="student-avatar">
                    <i class="fas fa-user-graduate"></i>
                  </div>
                  <div class="student-details">
                    <h4>Lucas Dubois</h4>
                    <p>CM2</p>
                    <div class="student-meta">
                      <span class="student-activity"
                        >Dernière activité: Hier</span
                      >
                      <span class="student-progress">Progression: 72%</span>
                    </div>
                  </div>
                  <div class="student-actions">
                    <button class="btn-icon" title="Voir le profil">
                      <i class="fas fa-eye"></i>
                    </button>
                  </div>
                </div>

                <div class="student-item">
                  <div class="student-avatar">
                    <i class="fas fa-user-graduate"></i>
                  </div>
                  <div class="student-details">
                    <h4>Léa Bernard</h4>
                    <p>CM2</p>
                    <div class="student-meta">
                      <span class="student-activity"
                        >Dernière activité: Il y a 2 jours</span
                      >
                      <span class="student-progress">Progression: 90%</span>
                    </div>
                  </div>
                  <div class="student-actions">
                    <button class="btn-icon" title="Voir le profil">
                      <i class="fas fa-eye"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="dashboard-section">
            <div class="section-header">
              <h3>Progression de la classe</h3>
              <a href="#" class="section-link">Voir les détails</a>
            </div>
            <div class="section-content">
              <div class="progress-chart">
                <div class="chart-placeholder">
                  <div class="chart-bars">
                    <div class="chart-bar" style="height: 75%">
                      <span class="chart-value">75%</span>
                    </div>
                    <div class="chart-bar" style="height: 60%">
                      <span class="chart-value">60%</span>
                    </div>
                    <div class="chart-bar" style="height: 85%">
                      <span class="chart-value">85%</span>
                    </div>
                    <div class="chart-bar" style="height: 45%">
                      <span class="chart-value">45%</span>
                    </div>
                    <div class="chart-bar" style="height: 70%">
                      <span class="chart-value">70%</span>
                    </div>
                  </div>
                  <div class="chart-labels">
                    <span>Division simple</span>
                    <span>Division avec reste</span>
                    <span>Division décimale</span>
                    <span>Division euclidienne</span>
                    <span>Problèmes</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="dashboard-sections">
          <div class="dashboard-section">
            <div class="section-header">
              <h3>Exercices récents</h3>
              <a href="#" class="section-link">Voir tous les exercices</a>
            </div>
            <div class="section-content">
              <div class="exercise-list">
                <div class="exercise-item">
                  <div class="exercise-icon">
                    <i class="fas fa-divide"></i>
                  </div>
                  <div class="exercise-details">
                    <h4>Division avec reste - Niveau CM2</h4>
                    <p>Créé il y a 2 jours</p>
                    <div class="exercise-meta">
                      <span class="exercise-completion"
                        >Complété par: 18/24 élèves</span
                      >
                      <span class="exercise-score">Score moyen: 7.5/10</span>
                    </div>
                  </div>
                  <div class="exercise-actions">
                    <button class="btn-icon" title="Modifier">
                      <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon" title="Voir les résultats">
                      <i class="fas fa-chart-bar"></i>
                    </button>
                  </div>
                </div>

                <div class="exercise-item">
                  <div class="exercise-icon">
                    <i class="fas fa-divide"></i>
                  </div>
                  <div class="exercise-details">
                    <h4>Division décimale - Niveau CM2</h4>
                    <p>Créé il y a 5 jours</p>
                    <div class="exercise-meta">
                      <span class="exercise-completion"
                        >Complété par: 22/24 élèves</span
                      >
                      <span class="exercise-score">Score moyen: 6.8/10</span>
                    </div>
                  </div>
                  <div class="exercise-actions">
                    <button class="btn-icon" title="Modifier">
                      <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon" title="Voir les résultats">
                      <i class="fas fa-chart-bar"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="dashboard-section">
            <div class="section-header">
              <h3>Élèves nécessitant de l'attention</h3>
            </div>
            <div class="section-content">
              <div class="attention-list">
                <div class="attention-item">
                  <div class="attention-icon warning">
                    <i class="fas fa-exclamation-triangle"></i>
                  </div>
                  <div class="attention-content">
                    <h4>Thomas Petit</h4>
                    <p>Difficulté avec la division euclidienne (score: 3/10)</p>
                    <button class="btn-secondary">Voir le profil</button>
                  </div>
                </div>
                <div class="attention-item">
                  <div class="attention-icon info">
                    <i class="fas fa-info-circle"></i>
                  </div>
                  <div class="attention-content">
                    <h4>Sophie Leroy</h4>
                    <p>Inactive depuis plus d'une semaine</p>
                    <button class="btn-secondary">Voir le profil</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <footer>
      <div class="footer-content">
        <div class="footer-section">
          <h3>smartDiv</h3>
          <p>
            Une application éducative pour apprendre la division euclidienne.
          </p>
        </div>
        <div class="footer-section">
          <h3>Liens utiles</h3>
          <ul>
            <li><a href="index.html">Accueil</a></li>
            <li><a href="teacher-dashboard.html">Tableau de bord</a></li>
            <li><a href="#">Mes élèves</a></li>
          </ul>
        </div>
        <div class="footer-section">
          <h3>Contact</h3>
          <p><i class="fas fa-envelope"></i> <EMAIL></p>
        </div>
      </div>
      <div class="footer-bottom">
        <p>
          &copy; <span id="current-year">2023</span> Ali MESSAOUDI - smartDiv -
          Tous droits réservés
        </p>
        <p class="footer-version">
          <a href="LICENSE.txt" id="footer-license" aria-label="Licence"
            >Licence</a
          >
        </p>
      </div>
    </footer>

    <div id="toast-container" class="toast-container"></div>

    <script src="modern-nav.js"></script>
    <script src="dashboard.js"></script>
    <script>
      // Mettre à jour l'année automatiquement
      document.addEventListener("DOMContentLoaded", function () {
        // Mettre à jour l'année
        const yearElement = document.getElementById("current-year");
        if (yearElement) {
          yearElement.textContent = new Date().getFullYear();
        }
      });
    </script>
  </body>
</html>
