/**
 * auth-nav.js
 *
 * Copyright (c) 2023 Ali MESSAOUDI
 * Tous droits réservés.
 * Ce code est protégé par les lois sur le droit d'auteur et ne peut être reproduit,
 * distribué ou utilisé sans autorisation explicite de l'auteur.
 *
 * Système de navigation avec authentification pour DivisionSim
 * Gère l'affichage des éléments de navigation en fonction de l'état de connexion
 */
class AuthNavigation {
  constructor() {
    // Éléments DOM - Navigation
    this.teacherNav = document.querySelector('.teacher-nav');
    this.studentNav = document.querySelector('.student-nav');
    this.loginNav = document.querySelector('.login-nav');
    this.registerNav = document.querySelector('.register-nav');
    this.userNav = document.querySelector('.user-nav');
    this.navUsername = document.getElementById('nav-username');

    // État de l'utilisateur
    this.currentUser = null;

    // Initialiser le système
    this.init();
  }

  /**
   * Initialise le système de navigation
   */
  init() {
    // Charger l'utilisateur depuis le stockage local
    this.loadUserFromStorage();

    // Mettre à jour l'interface utilisateur
    this.updateUI();
  }

  /**
   * Charge l'utilisateur depuis le stockage local
   */
  loadUserFromStorage() {
    const storedUser = localStorage.getItem('currentUser');
    if (storedUser) {
      try {
        this.currentUser = JSON.parse(storedUser);
      } catch (error) {
        console.error('Erreur lors du chargement de l\'utilisateur:', error);
        localStorage.removeItem('currentUser');
      }
    }
  }

  /**
   * Met à jour l'interface utilisateur en fonction de l'état de connexion
   */
  updateUI() {
    if (this.currentUser) {
      // Utilisateur connecté
      if (this.loginNav) this.loginNav.classList.add('hidden');
      if (this.registerNav) this.registerNav.classList.add('hidden');
      if (this.userNav) {
        this.userNav.classList.remove('hidden');
        if (this.navUsername) {
          this.navUsername.textContent = this.currentUser.firstname;
        }
      }

      // Afficher le lien vers le tableau de bord approprié
      if (this.currentUser.role === 'teacher') {
        if (this.teacherNav) this.teacherNav.classList.remove('hidden');
        if (this.studentNav) this.studentNav.classList.add('hidden');
      } else {
        if (this.teacherNav) this.teacherNav.classList.add('hidden');
        if (this.studentNav) this.studentNav.classList.remove('hidden');
      }
    } else {
      // Utilisateur non connecté
      if (this.loginNav) this.loginNav.classList.remove('hidden');
      if (this.registerNav) this.registerNav.classList.remove('hidden');
      if (this.userNav) this.userNav.classList.add('hidden');
      if (this.teacherNav) this.teacherNav.classList.add('hidden');
      if (this.studentNav) this.studentNav.classList.add('hidden');
    }
  }
}

// Initialiser le système de navigation lorsque le DOM est chargé
document.addEventListener('DOMContentLoaded', () => {
  new AuthNavigation();
});
