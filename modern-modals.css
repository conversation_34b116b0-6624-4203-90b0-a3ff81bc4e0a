/* 
 * smartDiv - Styles des modales et composants flottants modernes
 * Ce fichier contient les styles pour les modales et composants flottants modernisés
 */

/* ===== OVERLAY MODAL ===== */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: var(--color-background);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  padding: var(--space-xl);
  max-width: 800px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  transform: translateY(20px);
  transition: transform var(--transition-normal);
  position: relative;
}

.modal-overlay.active .modal-content {
  transform: translateY(0);
}

.modal-close {
  position: absolute;
  top: var(--space-md);
  right: var(--space-md);
  background: none;
  border: none;
  color: var(--color-text-secondary);
  font-size: var(--font-size-xl);
  cursor: pointer;
  transition: all var(--transition-fast);
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.modal-close:hover {
  color: var(--color-error);
  background-color: rgba(220, 53, 69, 0.1);
}

.modal-content h2 {
  color: var(--color-primary);
  margin-top: 0;
  margin-bottom: var(--space-lg);
  padding-right: var(--space-2xl);
  position: relative;
  padding-bottom: var(--space-sm);
}

.modal-content h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 4px;
  background-color: var(--color-primary);
  border-radius: 2px;
}

.modal-footer {
  margin-top: var(--space-xl);
  display: flex;
  justify-content: flex-end;
  gap: var(--space-sm);
  border-top: 1px solid var(--color-border);
  padding-top: var(--space-lg);
}

/* ===== TABS ===== */
.tabs {
  display: flex;
  border-bottom: 1px solid var(--color-border);
  margin-bottom: var(--space-lg);
  gap: var(--space-xs);
}

.tab-btn {
  padding: var(--space-sm) var(--space-lg);
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  color: var(--color-text-secondary);
  cursor: pointer;
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
}

.tab-btn:hover {
  color: var(--color-primary);
}

.tab-btn.active {
  color: var(--color-primary);
  border-bottom-color: var(--color-primary);
  font-weight: var(--font-weight-semibold);
}

.tab-content {
  margin-bottom: var(--space-lg);
}

.tab-pane {
  display: none;
  animation: fadeIn 0.5s ease;
}

.tab-pane.active {
  display: block;
}

/* ===== TOAST NOTIFICATIONS ===== */
.toast-container {
  position: fixed;
  bottom: var(--space-lg);
  right: var(--space-lg);
  z-index: 1500;
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  max-width: 350px;
}

.toast {
  background-color: var(--color-background);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  padding: var(--space-md) var(--space-lg);
  display: flex;
  align-items: center;
  gap: var(--space-md);
  animation: slideUp 0.3s ease;
  border-left: 4px solid var(--color-primary);
}

.toast-icon {
  font-size: var(--font-size-xl);
  color: var(--color-primary);
}

.toast-content {
  flex: 1;
}

.toast-title {
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-xs);
}

.toast-message {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.toast-close {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  font-size: var(--font-size-lg);
  transition: color var(--transition-fast);
}

.toast-close:hover {
  color: var(--color-error);
}

.toast-success {
  border-left-color: var(--color-success);
}

.toast-success .toast-icon {
  color: var(--color-success);
}

.toast-warning {
  border-left-color: var(--color-warning);
}

.toast-warning .toast-icon {
  color: var(--color-warning);
}

.toast-error {
  border-left-color: var(--color-error);
}

.toast-error .toast-icon {
  color: var(--color-error);
}

.toast-info {
  border-left-color: var(--color-info);
}

.toast-info .toast-icon {
  color: var(--color-info);
}

/* ===== PANNEAU D'ACCESSIBILITÉ ===== */
.panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 320px;
  height: 100%;
  background-color: var(--color-background);
  box-shadow: var(--shadow-xl);
  z-index: 1800;
  transform: translateX(100%);
  transition: transform var(--transition-normal);
  display: flex;
  flex-direction: column;
}

.panel.active {
  transform: translateX(0);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-md) var(--space-lg);
  border-bottom: 1px solid var(--color-border);
}

.panel-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  color: var(--color-primary);
}

.panel-close {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  font-size: var(--font-size-xl);
  cursor: pointer;
  transition: color var(--transition-fast);
}

.panel-close:hover {
  color: var(--color-error);
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-lg);
}

.option-group {
  margin-bottom: var(--space-lg);
}

.option-group label {
  display: block;
  margin-bottom: var(--space-sm);
  font-weight: var(--font-weight-medium);
}

.range-control {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.btn-small {
  background-color: var(--color-background-light);
  border: 1px solid var(--color-border);
  color: var(--color-text-primary);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.btn-small:hover {
  background-color: var(--color-primary-light);
  color: white;
  border-color: var(--color-primary-light);
}

input[type="range"] {
  flex: 1;
  height: 6px;
  -webkit-appearance: none;
  background-color: var(--color-border);
  border-radius: 3px;
  outline: none;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: var(--color-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

input[type="range"]::-webkit-slider-thumb:hover {
  background-color: var(--color-primary-dark);
  transform: scale(1.2);
}

.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-border);
  transition: all var(--transition-fast);
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: all var(--transition-fast);
}

input:checked + .slider {
  background-color: var(--color-primary);
}

input:focus + .slider {
  box-shadow: 0 0 1px var(--color-primary);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}
