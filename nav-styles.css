/**
 * nav-styles.css
 *
 * Copyright (c) 2023 Ali MESSAOUDI
 * Tous droits réservés.
 * Ce code est protégé par les lois sur le droit d'auteur et ne peut être reproduit,
 * distribué ou utilisé sans autorisation explicite de l'auteur.
 */

/* Styles pour la navigation avec authentification */
.auth-nav {
  margin-left: auto;
}

.login-nav a,
.register-nav a {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.login-nav a {
  color: var(--primary-color);
}

.register-nav a {
  background-color: var(--primary-color);
  color: white;
}

.login-nav a:hover {
  background-color: rgba(52, 152, 219, 0.1);
}

.register-nav a:hover {
  background-color: var(--primary-color-dark);
}

.user-nav {
  position: relative;
}

.user-nav > a {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.user-nav > a:hover {
  background-color: rgba(52, 152, 219, 0.1);
}

/* Responsive pour la navigation */
@media (max-width: 768px) {
  .main-nav ul {
    flex-wrap: wrap;
  }

  .auth-nav {
    margin-left: 0;
    margin-top: 8px;
  }
}
