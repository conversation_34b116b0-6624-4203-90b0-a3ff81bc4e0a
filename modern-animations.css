/* 
 * smartDiv - Animations modernes
 * Ce fichier contient des animations avancées pour améliorer l'expérience utilisateur
 */

/* Animation d'entrée pour les éléments */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animation de rebond subtile */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Animation de pulsation pour attirer l'attention */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.4);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(67, 97, 238, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(67, 97, 238, 0);
  }
}

/* Animation de rotation */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Animation de secousse pour les erreurs */
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

/* Animation de fondu pour les transitions de page */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Animation d'expansion */
@keyframes expand {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Animation de glissement latéral */
@keyframes slideInRight {
  from {
    transform: translateX(50px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Animation de glissement latéral inverse */
@keyframes slideInLeft {
  from {
    transform: translateX(-50px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Animation de fondu pour les tooltips */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Classes d'animation */
.animate-fadeInUp {
  animation: fadeInUp 0.5s ease forwards;
}

.animate-bounce {
  animation: bounce 1s ease infinite;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-rotate {
  animation: rotate 1s linear infinite;
}

.animate-shake {
  animation: shake 0.5s ease;
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease forwards;
}

.animate-expand {
  animation: expand 0.4s ease forwards;
}

.animate-slideInRight {
  animation: slideInRight 0.5s ease forwards;
}

.animate-slideInLeft {
  animation: slideInLeft 0.5s ease forwards;
}

.animate-fadeInDown {
  animation: fadeInDown 0.5s ease forwards;
}

/* Délais d'animation */
.delay-100 {
  animation-delay: 100ms;
}

.delay-200 {
  animation-delay: 200ms;
}

.delay-300 {
  animation-delay: 300ms;
}

.delay-400 {
  animation-delay: 400ms;
}

.delay-500 {
  animation-delay: 500ms;
}

/* Désactivation des animations pour les utilisateurs qui préfèrent réduire les animations */
@media (prefers-reduced-motion: reduce) {
  .animate-fadeInUp,
  .animate-bounce,
  .animate-pulse,
  .animate-rotate,
  .animate-shake,
  .animate-fadeIn,
  .animate-expand,
  .animate-slideInRight,
  .animate-slideInLeft,
  .animate-fadeInDown {
    animation: none !important;
    transition: none !important;
  }
}
