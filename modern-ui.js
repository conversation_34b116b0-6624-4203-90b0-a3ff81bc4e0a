/**
 * smartDiv - Script d'interface utilisateur moderne
 * Ce script gère les fonctionnalités d'interface utilisateur modernisées
 */

document.addEventListener('DOMContentLoaded', function() {
  // Initialisation des composants d'interface
  initializeToasts();
  initializeTabs();
  initializeModals();
  initializeDropdowns();
  initializeMobileMenu();
  initializeAnimations();
  initializeThemeToggle();
  initializeAccessibilityPanel();
  
  // Amélioration des interactions du tableau Excel
  enhanceExcelTable();
});

/**
 * Initialise le système de notifications toast
 */
function initializeToasts() {
  // Fonction pour créer une notification toast
  window.createToast = function(message, type = 'info', duration = 3000) {
    const toastContainer = document.getElementById('toast-container');
    if (!toastContainer) return;
    
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    
    let iconClass = 'fa-info-circle';
    if (type === 'success') iconClass = 'fa-check-circle';
    if (type === 'warning') iconClass = 'fa-exclamation-triangle';
    if (type === 'error') iconClass = 'fa-times-circle';
    
    toast.innerHTML = `
      <div class="toast-icon">
        <i class="fas ${iconClass}"></i>
      </div>
      <div class="toast-content">
        <div class="toast-message">${message}</div>
      </div>
      <button class="toast-close">
        <i class="fas fa-times"></i>
      </button>
    `;
    
    toastContainer.appendChild(toast);
    
    // Gestion de la fermeture
    const closeBtn = toast.querySelector('.toast-close');
    closeBtn.addEventListener('click', function() {
      toast.remove();
    });
    
    // Suppression automatique après la durée spécifiée
    setTimeout(function() {
      if (toast.parentNode) {
        toast.style.opacity = '0';
        setTimeout(() => toast.remove(), 300);
      }
    }, duration);
  };
}

/**
 * Initialise les onglets dans les modales
 */
function initializeTabs() {
  const tabBtns = document.querySelectorAll('.tab-btn');
  
  tabBtns.forEach(btn => {
    btn.addEventListener('click', function() {
      const tabId = this.dataset.tab;
      const tabContainer = this.closest('.tabs').nextElementSibling;
      
      // Désactiver tous les onglets
      tabContainer.querySelectorAll('.tab-pane').forEach(tab => {
        tab.classList.remove('active');
      });
      
      // Désactiver tous les boutons
      this.parentElement.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
      });
      
      // Activer l'onglet sélectionné
      document.getElementById(tabId).classList.add('active');
      this.classList.add('active');
    });
  });
}

/**
 * Initialise les modales
 */
function initializeModals() {
  // Ouvrir les modales
  document.querySelectorAll('[data-modal]').forEach(trigger => {
    trigger.addEventListener('click', function(e) {
      e.preventDefault();
      const modalId = this.dataset.modal;
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
      }
    });
  });
  
  // Fermer les modales
  document.querySelectorAll('.modal-close, [data-close-modal]').forEach(closeBtn => {
    closeBtn.addEventListener('click', function() {
      const modal = this.closest('.modal-overlay');
      if (modal) {
        modal.classList.remove('active');
        document.body.style.overflow = '';
      }
    });
  });
  
  // Fermer les modales en cliquant sur l'overlay
  document.querySelectorAll('.modal-overlay').forEach(modal => {
    modal.addEventListener('click', function(e) {
      if (e.target === this) {
        this.classList.remove('active');
        document.body.style.overflow = '';
      }
    });
  });
  
  // Gestion des touches clavier
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      const activeModal = document.querySelector('.modal-overlay.active');
      if (activeModal) {
        activeModal.classList.remove('active');
        document.body.style.overflow = '';
      }
    }
  });
}

/**
 * Initialise les menus déroulants
 */
function initializeDropdowns() {
  document.querySelectorAll('.dropdown-toggle').forEach(toggle => {
    toggle.addEventListener('click', function(e) {
      e.preventDefault();
      e.stopPropagation();
      
      const dropdown = this.nextElementSibling;
      dropdown.classList.toggle('active');
      
      // Fermer les autres dropdowns
      document.querySelectorAll('.dropdown-menu.active').forEach(menu => {
        if (menu !== dropdown) {
          menu.classList.remove('active');
        }
      });
    });
  });
  
  // Fermer les dropdowns en cliquant ailleurs
  document.addEventListener('click', function() {
    document.querySelectorAll('.dropdown-menu.active').forEach(menu => {
      menu.classList.remove('active');
    });
  });
}

/**
 * Initialise le menu mobile
 */
function initializeMobileMenu() {
  const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
  if (!mobileMenuToggle) return;
  
  mobileMenuToggle.addEventListener('click', function() {
    this.classList.toggle('active');
    
    const navList = document.querySelector('.main-nav ul');
    const authGroup = document.querySelector('.auth-nav-group');
    
    if (navList) navList.classList.toggle('active');
    if (authGroup) authGroup.classList.toggle('active');
  });
}

/**
 * Initialise les animations
 */
function initializeAnimations() {
  // Animation des éléments au scroll
  const animateOnScroll = function() {
    const elements = document.querySelectorAll('.animate-on-scroll');
    
    elements.forEach(element => {
      const elementPosition = element.getBoundingClientRect().top;
      const windowHeight = window.innerHeight;
      
      if (elementPosition < windowHeight * 0.9) {
        element.classList.add('animated');
      }
    });
  };
  
  // Exécuter une fois au chargement
  animateOnScroll();
  
  // Puis à chaque scroll
  window.addEventListener('scroll', animateOnScroll);
}

/**
 * Initialise le basculement de thème (clair/sombre)
 */
function initializeThemeToggle() {
  const themeToggle = document.getElementById('theme-toggle');
  const footerThemeToggle = document.getElementById('footer-theme-toggle');
  
  const toggles = [themeToggle, footerThemeToggle].filter(Boolean);
  
  // Vérifier si un thème est déjà enregistré
  const currentTheme = localStorage.getItem('theme');
  if (currentTheme === 'dark') {
    document.body.classList.add('dark-mode');
    toggles.forEach(toggle => {
      const icon = toggle.querySelector('i');
      if (icon) {
        icon.classList.remove('fa-moon');
        icon.classList.add('fa-sun');
      }
    });
  }
  
  // Fonction de basculement
  const toggleTheme = function() {
    document.body.classList.toggle('dark-mode');
    
    toggles.forEach(toggle => {
      const icon = toggle.querySelector('i');
      if (icon) {
        icon.classList.toggle('fa-moon');
        icon.classList.toggle('fa-sun');
      }
    });
    
    // Enregistrer la préférence
    if (document.body.classList.contains('dark-mode')) {
      localStorage.setItem('theme', 'dark');
    } else {
      localStorage.setItem('theme', 'light');
    }
  };
  
  // Ajouter les écouteurs d'événements
  toggles.forEach(toggle => {
    if (toggle) {
      toggle.addEventListener('click', toggleTheme);
    }
  });
}

/**
 * Initialise le panneau d'accessibilité
 */
function initializeAccessibilityPanel() {
  const accessibilityToggle = document.getElementById('accessibility-toggle');
  const footerAccessibility = document.getElementById('footer-accessibility');
  const accessibilityPanel = document.getElementById('accessibility-panel');
  const panelClose = document.querySelector('.panel-close');
  
  const toggles = [accessibilityToggle, footerAccessibility].filter(Boolean);
  
  if (!accessibilityPanel) return;
  
  // Ouvrir le panneau
  toggles.forEach(toggle => {
    if (toggle) {
      toggle.addEventListener('click', function() {
        accessibilityPanel.classList.add('active');
      });
    }
  });
  
  // Fermer le panneau
  if (panelClose) {
    panelClose.addEventListener('click', function() {
      accessibilityPanel.classList.remove('active');
    });
  }
  
  // Gestion de la taille de police
  const fontSizeSlider = document.getElementById('font-size');
  const decreaseFont = document.getElementById('decrease-font');
  const increaseFont = document.getElementById('increase-font');
  
  if (fontSizeSlider) {
    // Charger la taille enregistrée
    const savedFontSize = localStorage.getItem('fontSize');
    if (savedFontSize) {
      document.documentElement.style.fontSize = `${savedFontSize}%`;
      fontSizeSlider.value = savedFontSize;
    }
    
    // Mettre à jour la taille
    fontSizeSlider.addEventListener('input', function() {
      document.documentElement.style.fontSize = `${this.value}%`;
      localStorage.setItem('fontSize', this.value);
    });
    
    // Boutons d'augmentation/diminution
    if (decreaseFont) {
      decreaseFont.addEventListener('click', function() {
        fontSizeSlider.value = Math.max(80, parseInt(fontSizeSlider.value) - 10);
        fontSizeSlider.dispatchEvent(new Event('input'));
      });
    }
    
    if (increaseFont) {
      increaseFont.addEventListener('click', function() {
        fontSizeSlider.value = Math.min(150, parseInt(fontSizeSlider.value) + 10);
        fontSizeSlider.dispatchEvent(new Event('input'));
      });
    }
  }
  
  // Autres options d'accessibilité
  const contrastMode = document.getElementById('contrast-mode');
  const dyslexicFont = document.getElementById('dyslexic-font');
  const reduceMotion = document.getElementById('reduce-motion');
  
  // Contraste élevé
  if (contrastMode) {
    contrastMode.checked = localStorage.getItem('highContrast') === 'true';
    
    if (contrastMode.checked) {
      document.body.classList.add('high-contrast');
    }
    
    contrastMode.addEventListener('change', function() {
      document.body.classList.toggle('high-contrast');
      localStorage.setItem('highContrast', this.checked);
    });
  }
  
  // Police pour dyslexiques
  if (dyslexicFont) {
    dyslexicFont.checked = localStorage.getItem('dyslexicFont') === 'true';
    
    if (dyslexicFont.checked) {
      document.body.classList.add('dyslexic-font');
    }
    
    dyslexicFont.addEventListener('change', function() {
      document.body.classList.toggle('dyslexic-font');
      localStorage.setItem('dyslexicFont', this.checked);
    });
  }
  
  // Réduire les animations
  if (reduceMotion) {
    reduceMotion.checked = localStorage.getItem('reduceMotion') === 'true';
    
    if (reduceMotion.checked) {
      document.body.classList.add('reduce-motion');
    }
    
    reduceMotion.addEventListener('change', function() {
      document.body.classList.toggle('reduce-motion');
      localStorage.setItem('reduceMotion', this.checked);
    });
  }
  
  // Réinitialiser les paramètres
  const resetAccessibility = document.getElementById('reset-accessibility');
  
  if (resetAccessibility) {
    resetAccessibility.addEventListener('click', function() {
      // Réinitialiser la taille de police
      document.documentElement.style.fontSize = '100%';
      if (fontSizeSlider) fontSizeSlider.value = 100;
      localStorage.removeItem('fontSize');
      
      // Réinitialiser les autres options
      document.body.classList.remove('high-contrast', 'dyslexic-font', 'reduce-motion');
      
      if (contrastMode) contrastMode.checked = false;
      if (dyslexicFont) dyslexicFont.checked = false;
      if (reduceMotion) reduceMotion.checked = false;
      
      localStorage.removeItem('highContrast');
      localStorage.removeItem('dyslexicFont');
      localStorage.removeItem('reduceMotion');
      
      // Notification
      if (window.createToast) {
        window.createToast('Paramètres d\'accessibilité réinitialisés', 'success');
      }
    });
  }
}

/**
 * Améliore les interactions du tableau Excel
 */
function enhanceExcelTable() {
  const excelTable = document.getElementById('excel-table');
  if (!excelTable) return;
  
  // Surlignage des cellules au survol
  excelTable.addEventListener('mouseover', function(e) {
    if (e.target.tagName === 'TD') {
      const rowIndex = e.target.parentElement.rowIndex;
      const colIndex = e.target.cellIndex;
      
      // Surligner la ligne et la colonne
      for (let i = 0; i < this.rows.length; i++) {
        if (this.rows[i].cells[colIndex]) {
          this.rows[i].cells[colIndex].classList.add('highlight');
        }
      }
      
      const row = this.rows[rowIndex];
      if (row) {
        for (let i = 0; i < row.cells.length; i++) {
          row.cells[i].classList.add('highlight');
        }
      }
    }
  });
  
  excelTable.addEventListener('mouseout', function(e) {
    if (e.target.tagName === 'TD') {
      // Retirer tous les surlignages
      const cells = this.querySelectorAll('td.highlight');
      cells.forEach(cell => {
        cell.classList.remove('highlight');
      });
    }
  });
  
  // Gestion du plein écran
  const fullscreenBtn = document.getElementById('fullscreen-excel');
  const fullscreenHeaderBtn = document.getElementById('fullscreen-excel-header');
  const excelWrapper = document.querySelector('.excel-sheet-wrapper');
  
  const toggleFullscreen = function() {
    if (excelWrapper) {
      excelWrapper.classList.toggle('fullscreen');
      
      // Notification
      if (window.createToast) {
        if (excelWrapper.classList.contains('fullscreen')) {
          window.createToast('Mode plein écran activé', 'info');
        } else {
          window.createToast('Mode plein écran désactivé', 'info');
        }
      }
    }
  };
  
  if (fullscreenBtn) {
    fullscreenBtn.addEventListener('click', toggleFullscreen);
  }
  
  if (fullscreenHeaderBtn) {
    fullscreenHeaderBtn.addEventListener('click', toggleFullscreen);
  }
  
  // Échap pour quitter le plein écran
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && excelWrapper && excelWrapper.classList.contains('fullscreen')) {
      excelWrapper.classList.remove('fullscreen');
    }
  });
}
