/**
 * auth-page.js
 *
 * Copyright (c) 2023 Ali MESSAOUDI
 * Tous droits réservés.
 * Ce code est protégé par les lois sur le droit d'auteur et ne peut être reproduit,
 * distribué ou utilisé sans autorisation explicite de l'auteur.
 *
 * Système d'authentification pour DivisionSim
 * Gère la connexion, l'inscription et la gestion des utilisateurs
 */
class AuthSystem {
  constructor() {
    // Éléments DOM - Onglets et navigation
    this.authTabs = document.querySelectorAll('.auth-tab');
    this.authTabContents = document.querySelectorAll('.auth-tab-content');
    this.switchTabLinks = document.querySelectorAll('.switch-tab');

    // Éléments DOM - Formulaires
    this.loginForm = document.getElementById('login-form');
    this.registerForm = document.getElementById('register-form');
    this.togglePasswordButtons = document.querySelectorAll('.toggle-password');

    // Éléments DOM - Champs spécifiques
    this.userTypeRadios = document.querySelectorAll('input[name="user-type"]');
    this.teacherFields = document.querySelector('.teacher-fields');
    this.studentFields = document.querySelector('.student-fields');

    // Éléments DOM - Feedback
    this.loginFeedback = document.querySelector('#login-tab .auth-feedback');
    this.registerFeedback = document.querySelector('#register-tab .auth-feedback');
    this.formFeedbacks = document.querySelectorAll('.form-feedback');
    this.passwordInput = document.getElementById('register-password');
    this.strengthMeter = document.querySelectorAll('.strength-segment');
    this.strengthText = document.querySelector('.strength-text');

    // Spinners pour les boutons
    this.loginSpinner = document.querySelector('#login-form .spinner');
    this.registerSpinner = document.querySelector('#register-form .spinner');

    // Thème
    this.themeToggle = document.getElementById('theme-toggle');

    // État de l'authentification
    this.currentUser = null;

    // Initialiser le système
    this.init();
  }

  /**
   * Initialise le système d'authentification
   */
  init() {
    // Vérifier si un onglet est spécifié dans l'URL
    this.checkUrlHash();

    // Charger l'utilisateur depuis le stockage local
    this.loadUserFromStorage();

    // Ajouter les écouteurs d'événements
    this.addEventListeners();

    // Initialiser le thème
    this.initTheme();
  }

  /**
   * Vérifie si un onglet est spécifié dans l'URL
   */
  checkUrlHash() {
    if (window.location.hash === '#register') {
      this.switchTab('register-tab');
    }
  }

  /**
   * Initialise le thème
   */
  initTheme() {
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
      document.body.classList.add('dark-theme');
      this.themeToggle.querySelector('i').classList.remove('fa-moon');
      this.themeToggle.querySelector('i').classList.add('fa-sun');
    }
  }

  /**
   * Ajoute tous les écouteurs d'événements nécessaires
   */
  addEventListeners() {
    // Gestion des onglets
    this.authTabs.forEach(tab => {
      tab.addEventListener('click', () => {
        this.switchTab(tab.dataset.tab);
      });
    });

    // Liens pour changer d'onglet
    this.switchTabLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        this.switchTab(link.dataset.tab);
      });
    });

    // Formulaire de connexion
    if (this.loginForm) {
      this.loginForm.addEventListener('submit', (e) => {
        e.preventDefault();
        this.handleLogin();
      });
    }

    // Formulaire d'inscription
    if (this.registerForm) {
      this.registerForm.addEventListener('submit', (e) => {
        e.preventDefault();
        this.handleRegister();
      });
    }

    // Boutons pour afficher/masquer le mot de passe
    this.togglePasswordButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        const input = e.target.closest('.input-with-icon').querySelector('input');
        this.togglePasswordVisibility(input, button);
      });
    });

    // Changement de type d'utilisateur
    this.userTypeRadios.forEach(radio => {
      radio.addEventListener('change', () => {
        this.toggleUserTypeFields();
      });
    });

    // Validation du mot de passe en temps réel
    if (this.passwordInput) {
      this.passwordInput.addEventListener('input', () => {
        this.updatePasswordStrength(this.passwordInput.value);
      });
    }

    // Thème
    if (this.themeToggle) {
      this.themeToggle.addEventListener('click', () => {
        this.toggleTheme();
      });
    }

    // Gestion de la touche Escape pour fermer les modals
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        // Implémentation future pour les modals
      }
    });
  }

  /**
   * Change d'onglet dans le modal d'authentification
   * @param {string} tabId - L'ID de l'onglet à afficher
   */
  switchTab(tabId) {
    // Désactiver tous les onglets
    this.authTabs.forEach(tab => {
      tab.classList.remove('active');
    });

    // Désactiver tous les contenus d'onglets
    this.authTabContents.forEach(content => {
      content.classList.remove('active');
    });

    // Activer l'onglet sélectionné
    document.querySelector(`.auth-tab[data-tab="${tabId}"]`).classList.add('active');
    document.getElementById(tabId).classList.add('active');

    // Mettre à jour l'URL
    if (tabId === 'register-tab') {
      window.history.replaceState(null, null, '#register');
    } else {
      window.history.replaceState(null, null, ' ');
    }
  }

  /**
   * Affiche ou masque le mot de passe
   * @param {HTMLElement} input - L'élément input du mot de passe
   * @param {HTMLElement} button - Le bouton de basculement
   */
  togglePasswordVisibility(input, button) {
    const icon = button.querySelector('i');

    if (input.type === 'password') {
      input.type = 'text';
      icon.classList.remove('fa-eye');
      icon.classList.add('fa-eye-slash');
    } else {
      input.type = 'password';
      icon.classList.remove('fa-eye-slash');
      icon.classList.add('fa-eye');
    }
  }

  /**
   * Affiche ou masque les champs spécifiques au type d'utilisateur
   */
  toggleUserTypeFields() {
    const userType = document.querySelector('input[name="user-type"]:checked').value;

    if (userType === 'teacher') {
      this.teacherFields.classList.remove('hidden');
      this.studentFields.classList.add('hidden');
    } else {
      this.teacherFields.classList.add('hidden');
      this.studentFields.classList.remove('hidden');
    }
  }

  /**
   * Bascule entre le thème clair et sombre
   */
  toggleTheme() {
    const isDark = document.body.classList.toggle('dark-theme');
    const icon = this.themeToggle.querySelector('i');

    if (isDark) {
      icon.classList.remove('fa-moon');
      icon.classList.add('fa-sun');
      localStorage.setItem('theme', 'dark');
    } else {
      icon.classList.remove('fa-sun');
      icon.classList.add('fa-moon');
      localStorage.setItem('theme', 'light');
    }
  }

  /**
   * Charge l'utilisateur depuis le stockage local
   */
  loadUserFromStorage() {
    const storedUser = localStorage.getItem('currentUser');
    if (storedUser) {
      try {
        this.currentUser = JSON.parse(storedUser);

        // Si l'utilisateur est déjà connecté, rediriger vers la page d'accueil
        if (this.currentUser) {
          window.location.href = 'index.html';
        }
      } catch (error) {
        console.error('Erreur lors du chargement de l\'utilisateur:', error);
        localStorage.removeItem('currentUser');
      }
    }
  }

  /**
   * Évalue la force du mot de passe et met à jour l'indicateur visuel
   * @param {string} password - Le mot de passe à évaluer
   */
  updatePasswordStrength(password) {
    // Critères d'évaluation
    const hasLowerCase = /[a-z]/.test(password);
    const hasUpperCase = /[A-Z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChars = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    const isLongEnough = password.length >= 8;

    // Calculer le score (0-4)
    let score = 0;
    if (hasLowerCase) score++;
    if (hasUpperCase) score++;
    if (hasNumbers) score++;
    if (hasSpecialChars) score++;
    if (isLongEnough) score++;

    // Ajuster le score en fonction de la longueur
    if (password.length < 4) {
      score = Math.min(score, 1);
    } else if (password.length < 6) {
      score = Math.min(score, 2);
    }

    // Déterminer la force
    let strength = '';
    if (score === 0 || password.length === 0) {
      strength = '';
    } else if (score < 2) {
      strength = 'faible';
    } else if (score < 4) {
      strength = 'moyenne';
    } else {
      strength = 'forte';
    }

    // Mettre à jour l'interface
    this.strengthMeter.forEach((segment, index) => {
      segment.className = 'strength-segment';

      if (password.length === 0) {
        return;
      }

      if (index < score) {
        if (score < 2) {
          segment.classList.add('weak');
        } else if (score < 4) {
          segment.classList.add('medium');
        } else {
          segment.classList.add('strong');
        }
      }
    });

    // Mettre à jour le texte
    if (this.strengthText) {
      if (password.length === 0) {
        this.strengthText.textContent = 'Force du mot de passe';
        this.strengthText.style.color = '';
      } else if (strength === 'faible') {
        this.strengthText.textContent = 'Mot de passe faible';
        this.strengthText.style.color = 'var(--danger-color)';
      } else if (strength === 'moyenne') {
        this.strengthText.textContent = 'Mot de passe moyen';
        this.strengthText.style.color = 'var(--warning-color)';
      } else {
        this.strengthText.textContent = 'Mot de passe fort';
        this.strengthText.style.color = 'var(--success-color)';
      }
    }
  }

  /**
   * Affiche un message de feedback dans un formulaire
   * @param {HTMLElement} input - L'élément input concerné
   * @param {string} message - Le message à afficher
   * @param {string} type - Le type de message (success, error)
   */
  showFormFeedback(input, message, type = 'error') {
    const feedbackElement = input.closest('.form-group').querySelector('.form-feedback');
    if (feedbackElement) {
      feedbackElement.textContent = message;
      feedbackElement.className = `form-feedback ${type}`;
    }
  }

  /**
   * Affiche un message de feedback global pour un formulaire
   * @param {HTMLElement} formFeedback - L'élément de feedback du formulaire
   * @param {string} message - Le message à afficher
   * @param {string} type - Le type de message (success, error)
   */
  showAuthFeedback(formFeedback, message, type = 'error') {
    if (formFeedback) {
      formFeedback.textContent = message;
      formFeedback.className = `auth-feedback ${type}`;
      formFeedback.classList.remove('hidden');
    }
  }

  /**
   * Affiche un message toast
   * @param {string} message - Le message à afficher
   * @param {string} type - Le type de message (success, error, info, warning)
   */
  showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;

    // Ajouter le toast au conteneur
    const container = document.getElementById('toast-container');
    container.appendChild(toast);

    // Afficher le toast
    setTimeout(() => {
      toast.classList.add('show');
    }, 10);

    // Supprimer le toast après 3 secondes
    setTimeout(() => {
      toast.classList.remove('show');
      setTimeout(() => {
        toast.remove();
      }, 300);
    }, 3000);
  }

  /**
   * Gère la soumission du formulaire de connexion
   */
  handleLogin() {
    // Réinitialiser les messages de feedback
    this.formFeedbacks.forEach(feedback => {
      feedback.textContent = '';
      feedback.className = 'form-feedback';
    });

    if (this.loginFeedback) {
      this.loginFeedback.classList.add('hidden');
    }

    // Afficher le spinner
    if (this.loginSpinner) {
      this.loginSpinner.classList.remove('hidden');
    }

    // Récupérer les valeurs du formulaire
    const emailInput = document.getElementById('login-email');
    const passwordInput = document.getElementById('login-password');
    const email = emailInput.value.trim();
    const password = passwordInput.value;
    const remember = document.getElementById('remember').checked;

    // Validation basique
    let isValid = true;

    if (!email) {
      this.showFormFeedback(emailInput, 'Veuillez entrer votre adresse email.');
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      this.showFormFeedback(emailInput, 'Veuillez entrer une adresse email valide.');
      isValid = false;
    }

    if (!password) {
      this.showFormFeedback(passwordInput, 'Veuillez entrer votre mot de passe.');
      isValid = false;
    }

    if (!isValid) {
      // Masquer le spinner
      if (this.loginSpinner) {
        this.loginSpinner.classList.add('hidden');
      }
      return;
    }

    // Simuler un délai réseau pour une meilleure expérience utilisateur
    setTimeout(() => {
      // Vérifier si l'utilisateur existe dans le stockage local
      const users = this.getUsers();
      const user = users.find(u => u.email === email && u.password === password);

      // Masquer le spinner
      if (this.loginSpinner) {
        this.loginSpinner.classList.add('hidden');
      }

      if (user) {
        // Connexion réussie
        this.currentUser = user;

        // Enregistrer l'utilisateur dans le stockage local
        localStorage.setItem('currentUser', JSON.stringify(user));

        // Afficher un message de succès
        this.showToast(`Bienvenue, ${user.firstname} !`, 'success');

        // Rediriger vers la page d'accueil après un court délai
        setTimeout(() => {
          window.location.href = 'index.html';
        }, 1000);
      } else {
        // Échec de la connexion
        this.showAuthFeedback(this.loginFeedback, 'Email ou mot de passe incorrect.', 'error');
      }
    }, 800); // Délai simulé de 800ms
  }

  /**
   * Gère la soumission du formulaire d'inscription
   */
  handleRegister() {
    // Réinitialiser les messages de feedback
    this.formFeedbacks.forEach(feedback => {
      feedback.textContent = '';
      feedback.className = 'form-feedback';
    });

    if (this.registerFeedback) {
      this.registerFeedback.classList.add('hidden');
    }

    // Afficher le spinner
    if (this.registerSpinner) {
      this.registerSpinner.classList.remove('hidden');
    }

    // Récupérer les valeurs du formulaire
    const userType = document.querySelector('input[name="user-type"]:checked').value;
    const firstnameInput = document.getElementById('firstname');
    const lastnameInput = document.getElementById('lastname');
    const emailInput = document.getElementById('register-email');
    const passwordInput = document.getElementById('register-password');
    const confirmPasswordInput = document.getElementById('confirm-password');
    const termsInput = document.getElementById('terms');

    const firstname = firstnameInput.value.trim();
    const lastname = lastnameInput.value.trim();
    const email = emailInput.value.trim();
    const password = passwordInput.value;
    const confirmPassword = confirmPasswordInput.value;
    const termsAccepted = termsInput.checked;

    // Validation des champs
    let isValid = true;

    if (!firstname) {
      this.showFormFeedback(firstnameInput, 'Veuillez entrer votre prénom.');
      isValid = false;
    }

    if (!lastname) {
      this.showFormFeedback(lastnameInput, 'Veuillez entrer votre nom.');
      isValid = false;
    }

    if (!email) {
      this.showFormFeedback(emailInput, 'Veuillez entrer votre adresse email.');
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      this.showFormFeedback(emailInput, 'Veuillez entrer une adresse email valide.');
      isValid = false;
    }

    if (!password) {
      this.showFormFeedback(passwordInput, 'Veuillez créer un mot de passe.');
      isValid = false;
    } else if (password.length < 8) {
      this.showFormFeedback(passwordInput, 'Le mot de passe doit contenir au moins 8 caractères.');
      isValid = false;
    }

    if (!confirmPassword) {
      this.showFormFeedback(confirmPasswordInput, 'Veuillez confirmer votre mot de passe.');
      isValid = false;
    } else if (password !== confirmPassword) {
      this.showFormFeedback(confirmPasswordInput, 'Les mots de passe ne correspondent pas.');
      isValid = false;
    }

    if (!termsAccepted) {
      this.showFormFeedback(termsInput, 'Vous devez accepter les conditions d\'utilisation.');
      isValid = false;
    }

    // Validation des champs spécifiques selon le type d'utilisateur
    if (userType === 'teacher') {
      const schoolInput = document.getElementById('school');
      const school = schoolInput.value.trim();

      if (!school) {
        this.showFormFeedback(schoolInput, 'Veuillez indiquer votre établissement.');
        isValid = false;
      }
    } else {
      const gradeInput = document.getElementById('grade');
      const grade = gradeInput.value;

      if (!grade) {
        this.showFormFeedback(gradeInput, 'Veuillez sélectionner votre niveau scolaire.');
        isValid = false;
      }
    }

    if (!isValid) {
      // Masquer le spinner
      if (this.registerSpinner) {
        this.registerSpinner.classList.add('hidden');
      }
      return;
    }

    // Simuler un délai réseau pour une meilleure expérience utilisateur
    setTimeout(() => {
      // Vérifier si l'email est déjà utilisé
      const users = this.getUsers();

      // Masquer le spinner
      if (this.registerSpinner) {
        this.registerSpinner.classList.add('hidden');
      }

      if (users.some(u => u.email === email)) {
        this.showFormFeedback(emailInput, 'Cet email est déjà utilisé.');
        return;
      }

      // Créer le nouvel utilisateur
      const newUser = {
        id: Date.now().toString(),
        role: userType,
        firstname,
        lastname,
        email,
        password,
        createdAt: new Date().toISOString()
      };

      // Ajouter des champs spécifiques selon le type d'utilisateur
      if (userType === 'teacher') {
        newUser.school = document.getElementById('school').value.trim();
      } else {
        newUser.grade = document.getElementById('grade').value;
      }

      // Ajouter l'utilisateur à la liste
      users.push(newUser);
      localStorage.setItem('users', JSON.stringify(users));

      // Connecter automatiquement l'utilisateur
      this.currentUser = newUser;
      localStorage.setItem('currentUser', JSON.stringify(newUser));

      // Afficher un message de succès
      this.showAuthFeedback(this.registerFeedback, 'Votre compte a été créé avec succès !', 'success');

      // Rediriger vers la page d'accueil après un court délai
      setTimeout(() => {
        window.location.href = 'index.html';
      }, 1500);
    }, 800); // Délai simulé de 800ms
  }

  /**
   * Récupère la liste des utilisateurs depuis le stockage local
   * @returns {Array} - La liste des utilisateurs
   */
  getUsers() {
    const users = localStorage.getItem('users');
    return users ? JSON.parse(users) : [];
  }
}

// Initialiser le système d'authentification lorsque le DOM est chargé
document.addEventListener('DOMContentLoaded', () => {
  new AuthSystem();
});
