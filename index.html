<!DOCTYPE html>
<!--
  Copyright (c) 2023 Ali MESSAOUDI
  Tous droits réservés.
  Ce code est protégé par les lois sur le droit d'auteur et ne peut être reproduit,
  distribué ou utilisé sans autorisation explicite de l'auteur.
-->
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      name="description"
      content="Simulation interactive de division euclidienne pour l'apprentissage des mathématiques. Outil pédagogique gratuit pour comprendre et pratiquer la division euclidienne étape par étape."
    />
    <meta name="theme-color" content="#3498db" />
    <meta name="author" content="Simulation Mathématique" />
    <meta
      name="keywords"
      content="division euclidienne, mathématiques, simulation, apprentissage, éducation, algorithme de division, reste, quotient, diviseur, dividende"
    />
    <meta name="robots" content="index, follow" />
    <meta
      property="og:title"
      content="smartDiv - Division Euclidienne Interactive"
    />
    <meta
      property="og:description"
      content="Simulation interactive de division euclidienne pour l'apprentissage des mathématiques. Outil pédagogique gratuit."
    />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="og-image.jpg" />
    <meta name="twitter:card" content="summary_large_image" />
    <title>smartDiv - Division Euclidienne Interactive</title>

    <!-- Préchargement des ressources critiques -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preload" href="styles.css" as="style" />
    <link rel="preload" href="script.js" as="script" />
    <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com" />

    <!-- Styles -->
    <link rel="stylesheet" href="styles.css" />
    <link rel="stylesheet" href="excel-improvements.css" />
    <link rel="stylesheet" href="modern-nav.css" />
    <!-- Nouveaux styles modernisés -->
    <link rel="stylesheet" href="modern-styles.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />

    <!-- Favicon -->
    <link
      rel="icon"
      href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>➗</text></svg>"
    />

    <!-- PWA support -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta
      name="apple-mobile-web-app-status-bar-style"
      content="black-translucent"
    />
    <meta name="apple-mobile-web-app-title" content="Division Euclidienne" />
    <link
      rel="apple-touch-icon"
      href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>➗</text></svg>"
    />

    <!-- Manifest for PWA -->
    <link rel="manifest" href="manifest.json" />
  </head>
  <body>
    <header>
      <div class="header-content">
        <img
          src="images/logo.jpg"
          alt="Logo smartDiv"
          class="logo-image"
          width="60"
          height="60"
        />

        <div class="header-controls">
          <button
            id="help-button"
            class="btn-icon"
            aria-label="Aide générale"
            title="Aide générale"
          >
            <i class="fas fa-question-circle" aria-hidden="true"></i>
            <span class="visually-hidden">Aide</span>
          </button>
          <button
            id="theme-toggle"
            class="btn-icon"
            aria-label="Changer de thème"
            title="Changer de thème"
          >
            <i class="fas fa-moon" aria-hidden="true"></i>
            <span class="visually-hidden">Mode sombre</span>
          </button>
          <button
            id="accessibility-toggle"
            class="btn-icon"
            title="Options d'accessibilité"
            aria-label="Options d'accessibilité"
          >
            <i class="fas fa-universal-access" aria-hidden="true"></i>
            <span class="visually-hidden">Accessibilité</span>
          </button>
        </div>
      </div>
      <nav class="main-nav" aria-label="Navigation principale">
        <div class="main-nav-container">
          <button
            class="mobile-menu-toggle"
            aria-label="Menu"
            aria-expanded="false"
            id="mobile-menu-toggle"
          >
            <div class="hamburger">
              <span></span>
              <span></span>
              <span></span>
              <span></span>
            </div>
          </button>

          <ul>
            <li>
              <a
                href="index.html"
                class="active"
                id="nav-simulation"
                aria-current="page"
                data-i18n="nav.simulation"
                ><i class="fas fa-calculator"></i> Simulation</a
              >
            </li>
            <li>
              <a href="#" id="nav-aide" data-i18n="nav.help"
                ><i class="fas fa-question-circle"></i> Aide</a
              >
            </li>
            <li>
              <a href="#" id="nav-a-propos" data-i18n="nav.about"
                ><i class="fas fa-info-circle"></i> À propos</a
              >
            </li>
            <li class="teacher-nav hidden">
              <a
                href="teacher-dashboard.html"
                id="nav-dashboard"
                data-i18n="nav.dashboard"
                ><i class="fas fa-chalkboard-teacher"></i> Tableau de bord</a
              >
            </li>
            <li class="student-nav hidden">
              <a
                href="student-dashboard.html"
                id="nav-dashboard"
                data-i18n="nav.dashboard"
                ><i class="fas fa-user-graduate"></i> Tableau de bord</a
              >
            </li>
          </ul>

          <div class="auth-nav-group">
            <div class="auth-nav login-nav">
              <button
                onclick="window.location.href='auth.html'"
                id="nav-login"
                class="nav-button"
              >
                <i class="fas fa-sign-in-alt"></i> Connexion
              </button>
            </div>
            <div class="auth-nav register-nav">
              <button
                onclick="window.location.href='auth.html#register'"
                id="nav-register"
                class="nav-button"
              >
                <i class="fas fa-user-plus"></i> Inscription
              </button>
            </div>
            <div class="auth-nav user-nav hidden">
              <a href="auth.html" id="nav-user">
                <i class="fas fa-user-circle"></i>
                <span id="nav-username">Utilisateur</span>
              </a>
              <div class="user-dropdown">
                <div class="user-dropdown-header">
                  <div class="user-avatar">
                    <i class="fas fa-user"></i>
                  </div>
                  <div class="user-info">
                    <div class="user-name" id="dropdown-user-name">
                      Utilisateur
                    </div>
                    <div class="user-role" id="dropdown-user-role">Élève</div>
                  </div>
                </div>
                <ul class="user-dropdown-menu">
                  <li class="student-dashboard-link hidden">
                    <a href="student-dashboard.html"
                      ><i class="fas fa-tachometer-alt"></i> Tableau de bord</a
                    >
                  </li>
                  <li class="teacher-dashboard-link hidden">
                    <a href="teacher-dashboard.html"
                      ><i class="fas fa-tachometer-alt"></i> Tableau de bord</a
                    >
                  </li>
                  <li>
                    <a href="#"><i class="fas fa-user-cog"></i> Mon profil</a>
                  </li>
                  <li>
                    <a href="#"><i class="fas fa-history"></i> Historique</a>
                  </li>
                  <li class="user-dropdown-divider"></li>
                  <li>
                    <a href="#" id="logout-link"
                      ><i class="fas fa-sign-out-alt"></i> Déconnexion</a
                    >
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </nav>
    </header>
    <main id="app">
      <!-- Écran d'introduction -->
      <section id="introduction-screen" class="screen">
        <h2>Bienvenue dans la simulation</h2>
        <p class="intro-description">
          Suivez les étapes pour apprendre la division euclidienne. Les
          opérations seront automatiquement ajoutées dans le tableau Excel.
        </p>

        <form id="division-form" class="division-form">
          <div class="form-section">
            <h3 class="section-title">1. Choisissez le type de division</h3>
            <div class="radio-group division-types">
              <div class="radio-option">
                <label for="division-type-remainder">Division avec reste</label>
              </div>
              <div class="radio-option">
                <label for="division-type-exact">Division exacte</label>
              </div>
              <div class="radio-option">
                <label for="division-type-decimal">Division décimale</label>
              </div>
              <div class="radio-option">
                <label for="division-type-fraction"
                  >Division fractionnaire</label
                >
              </div>
              <div class="radio-option">
                <label for="division-type-grouping"
                  >Division par groupement</label
                >
              </div>
            </div>
          </div>

          <div class="form-section">
            <h3 class="section-title">
              2. Sélectionnez le niveau de difficulté
            </h3>
            <div class="radio-group difficulty-options">
              <div class="radio-option">
                <label for="difficulty-very-easy">Très facile</label>
              </div>
              <div class="radio-option">
                <label for="difficulty-easy">Facile</label>
              </div>
              <div class="radio-option">
                <label for="difficulty-medium">Moyen</label>
              </div>
              <div class="radio-option">
                <label for="difficulty-hard">Difficile</label>
              </div>
              <div class="radio-option">
                <label for="difficulty-expert">Expert</label>
              </div>
            </div>

            <div class="generate-numbers-container">
              <button type="button" id="generate-numbers" class="btn-secondary">
                <i class="fas fa-dice"></i> Générer des nombres
              </button>
            </div>
          </div>

          <div class="form-section">
            <h3 class="section-title">
              3. Saisir vos mêmes termes de la division
            </h3>
            <div class="input-group">
              <div class="input-field">
                <label for="dividend-input">Dividende :</label>
                <input
                  type="number"
                  id="dividend-input"
                  aria-label="Entrée du dividende"
                  min="1"
                  required
                />
              </div>
              <div class="input-field">
                <label for="divisor-input">Diviseur :</label>
                <input
                  type="number"
                  id="divisor-input"
                  aria-label="Entrée du diviseur"
                  min="1"
                  required
                />
              </div>
            </div>

            <p class="preview-row">
              Prévisualisation : <span id="dividend-preview"></span
              ><span class="preview-separator">÷</span
              ><span id="divisor-preview"></span>
            </p>
          </div>

          <div class="form-actions">
            <button type="button" id="start-simulation" class="btn-primary">
              <i class="fas fa-play"></i> Commencer
            </button>
          </div>
        </form>
      </section>

      <!-- Écran de simulation -->
      <section id="simulation-screen" class="screen hidden">
        <div class="main-container">
          <div class="simulation-steps">
            <div
              class="simulation-header"
              style="width: 100%; margin-left: 0; margin-right: auto"
            >
              <h2>Simulation en cours</h2>
              <div class="simulation-info">
                <div class="info-item">
                  <strong>Dividende :</strong>
                  <span id="dividend-display"></span>
                </div>
                <div class="info-item">
                  <strong>Diviseur :</strong> <span id="divisor-display"></span>
                </div>
                <div class="info-item">
                  <strong>Reste actuel :</strong>
                  <span id="remainder-display">0</span>
                </div>
              </div>
              <button
                id="btn-restart"
                aria-label="Recommencer la division"
                class="btn-restart"
              >
                <i class="fas fa-sync-alt"></i>
              </button>
            </div>
            <!-- Étapes de la simulation -->
            <div
              id="selection-stage"
              class="card-step"
              aria-labelledby="step1-title"
            >
              <h3 id="step1-title">Étape 1 : Sélection du Dividende</h3>
              <p>Sélectionnez le nombre à diviser.</p>
              <div
                id="dividend-selection"
                class="digit-selection"
                aria-live="polite"
              ></div>
              <button id="add-digit-btn" class="btn-primary">
                <i class="fas fa-plus"></i> Ajouter un chiffre
              </button>
            </div>

            <div
              id="quotient-stage"
              class="card-step hidden"
              aria-labelledby="step2-title"
            >
              <h3 id="step2-title">Étape 2 : Calcul du Quotient</h3>
              <div class="instruction-text">
                Combien de fois puis-je partager
                <span id="consigne-dividend" class="highlight-dividend"></span>
                en
                <span id="consigne-divisor" class="highlight-divisor"></span> ?
              </div>
              <div class="input-action-group">
                <input
                  type="number"
                  id="estimate-input"
                  aria-label="Estimation du quotient"
                  min="0"
                />
                <button id="submit-estimate" class="btn-primary">
                  <i class="fas fa-check"></i> Valider
                </button>
              </div>
              <p id="feedback" aria-live="polite"></p>
            </div>

            <div
              id="multiplication-stage"
              class="card-step hidden"
              aria-labelledby="step3-title"
            >
              <h3 id="step3-title">Étape 3 : Multiplication</h3>
              <p id="mult-operation-label"></p>
              <div class="input-action-group">
                <input
                  type="number"
                  id="mult-input"
                  aria-label="Résultat de la multiplication"
                  min="0"
                />
                <button id="check-mult" class="btn-primary">Vérifier</button>
              </div>
              <p id="feedback-mult" aria-live="polite"></p>
              <button id="next-to-sub" class="btn-secondary hidden">
                Continuer
              </button>
            </div>

            <div
              id="subtraction-stage"
              class="card-step hidden"
              aria-labelledby="step3b-title"
            >
              <h3 id="step3b-title">Étape 3b : Soustraction</h3>
              <p id="sub-operation-label"></p>
              <div class="input-action-group">
                <input
                  type="number"
                  id="sub-input"
                  aria-label="Résultat de la soustraction"
                  min="0"
                />
                <button id="check-sub" class="btn-primary">Vérifier</button>
              </div>
              <p id="feedback-sub" aria-live="polite"></p>
            </div>

            <div
              id="lowering-stage"
              class="card-step hidden"
              aria-labelledby="step4-title"
            >
              <h3 id="step4-title">Étape 4 : Abaissement</h3>
              <p>
                Chiffre abaissé :
                <span id="current-digit" aria-live="polite"></span>
                <span id="arrow-lowering" class="arrow-lowering">↓</span>
              </p>
              <button id="continue-lowering" class="btn-primary">
                Continuer
              </button>
            </div>

            <!-- Résumé final -->
            <div
              id="final-summary"
              class="card-step hidden"
              aria-labelledby="result-title"
            >
              <h3 id="result-title">✅ Résultat Final</h3>
              <p id="final-formula" class="final-formula"></p>
              <p
                id="final-congrats"
                class="final-congrats"
                aria-live="polite"
              ></p>
              <button id="restart" class="btn-success">
                <i class="fas fa-redo-alt"></i> Nouvelle division
              </button>
            </div>
          </div>

          <div class="excel-sheet-wrapper">
            <div class="excel-sheet-header">
              <h3>Tableau de calcul</h3>
              <div class="excel-actions">
                <button
                  id="toggle-grid"
                  class="btn-icon"
                  aria-label="Afficher/masquer la grille"
                  title="Afficher/masquer la grille"
                >
                  <i class="fas fa-border-all"></i>
                </button>
                <button
                  id="toggle-highlight"
                  class="btn-icon"
                  aria-label="Activer/désactiver le surlignage"
                  title="Activer/désactiver le surlignage"
                >
                  <i class="fas fa-highlighter"></i>
                </button>
                <button
                  id="clear-excel"
                  class="btn-icon"
                  aria-label="Effacer le tableau"
                  title="Effacer le tableau"
                >
                  <i class="fas fa-eraser"></i>
                </button>
                <button
                  id="fullscreen-excel-header"
                  class="btn-icon"
                  aria-label="Plein écran"
                  title="Plein écran"
                >
                  <i class="fas fa-expand"></i>
                </button>
                <button
                  id="print-excel-header"
                  class="btn-icon"
                  aria-label="Imprimer le tableau"
                  title="Imprimer le tableau"
                >
                  <i class="fas fa-print"></i>
                </button>
                <button
                  id="export-excel-header"
                  class="btn-icon"
                  aria-label="Télécharger en image"
                  title="Télécharger en image"
                >
                  <i class="fas fa-download"></i>
                </button>
                <button
                  id="help-excel"
                  class="btn-icon"
                  aria-label="Aide sur le tableau"
                  title="Aide sur le tableau"
                >
                  <i class="fas fa-question-circle"></i>
                </button>
              </div>
            </div>

            <div class="excel-toolbar">
              <div class="excel-tools">
                <button
                  id="print-excel"
                  class="btn-tool"
                  title="Imprimer le tableau"
                >
                  <i class="fas fa-print"></i> Imprimer
                </button>
                <button
                  id="fullscreen-excel"
                  class="btn-tool"
                  title="Plein écran"
                >
                  <i class="fas fa-expand"></i> Plein écran
                </button>
              </div>
              <div class="excel-view-options">
                <select
                  id="excel-view-mode"
                  class="excel-select"
                  title="Mode d'affichage"
                >
                  <option value="standard">Vue standard</option>
                  <option value="compact">Vue compacte</option>
                  <option value="detailed">Vue détaillée</option>
                </select>
              </div>
            </div>

            <div class="excel-container">
              <table id="excel-table">
                <thead>
                  <tr>
                    <!-- En-têtes générées dynamiquement -->
                  </tr>
                </thead>
                <tbody id="excel-body">
                  <!-- Lignes générées dynamiquement -->
                </tbody>
              </table>
            </div>

            <div class="excel-footer">
              <div class="excel-status">
                <span id="excel-operation-count">0 opérations</span>
                <span class="excel-status-separator">|</span>
                <span id="excel-last-operation">Aucune opération</span>
              </div>
              <div class="zoom-controls">
                <button
                  id="zoom-out"
                  class="btn-icon"
                  aria-label="Zoom arrière"
                  title="Zoom arrière"
                >
                  <i class="fas fa-search-minus"></i>
                </button>
                <span id="zoom-level">100%</span>
                <button
                  id="zoom-in"
                  class="btn-icon"
                  aria-label="Zoom avant"
                  title="Zoom avant"
                >
                  <i class="fas fa-search-plus"></i>
                </button>
                <button
                  id="reset-zoom"
                  class="btn-icon"
                  aria-label="Réinitialiser le zoom"
                  title="Réinitialiser le zoom"
                >
                  <i class="fas fa-sync-alt"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- Toast notifications -->
    <div id="toast-container" class="toast-container" aria-live="polite"></div>

    <!-- Aide Excel -->
    <div id="excel-help-modal" class="excel-help-modal hidden">
      <div class="excel-help-content">
        <h3>Aide sur le tableau de calcul</h3>
        <p>
          Ce tableau vous permet de suivre les opérations effectuées pendant la
          division euclidienne :
        </p>
        <ul>
          <li>
            Les opérations sont ajoutées automatiquement au fur et à mesure de
            la simulation
          </li>
          <li>
            Chaque type d'opération est affiché avec une couleur différente
          </li>
          <li>
            Vous pouvez zoomer ou dézoomer avec les boutons en bas à droite
          </li>
          <li>Le bouton d'effacement permet de vider toutes les cellules</li>
        </ul>
        <button id="close-help" class="btn-primary">Fermer</button>
      </div>
    </div>

    <!-- Aide générale -->
    <div id="general-help-modal" class="modal-overlay hidden">
      <div class="modal-content">
        <button
          class="modal-close"
          id="close-general-help"
          aria-label="Fermer l'aide"
        >
          <i class="fas fa-times"></i>
        </button>
        <h2>Guide d'utilisation</h2>

        <div class="tabs">
          <button class="tab-btn active" data-tab="tab-intro">
            Introduction
          </button>
          <button class="tab-btn" data-tab="tab-etapes">Étapes</button>
          <button class="tab-btn" data-tab="tab-astuces">Astuces</button>
        </div>

        <div class="tab-content">
          <div id="tab-intro" class="tab-pane active">
            <h3>Qu'est-ce que la division euclidienne ?</h3>
            <p>
              La division euclidienne est une opération mathématique qui
              consiste à diviser un nombre entier (le dividende) par un autre
              nombre entier non nul (le diviseur) pour obtenir un quotient et un
              reste.
            </p>
            <p>
              La relation fondamentale est :
              <strong>Dividende = Diviseur × Quotient + Reste</strong>, où le
              reste est inférieur au diviseur.
            </p>

            <h3>Comment utiliser cette simulation</h3>
            <p>
              Cette application vous guide pas à pas dans la réalisation d'une
              division euclidienne. Vous pourrez :
            </p>
            <ul>
              <li>
                Choisir vos propres nombres pour le dividende et le diviseur
              </li>
              <li>Suivre chaque étape du calcul de manière interactive</li>
              <li>
                Visualiser les opérations dans un tableau similaire à Excel
              </li>
              <li>Vérifier vos calculs à chaque étape</li>
            </ul>
          </div>

          <div id="tab-etapes" class="tab-pane">
            <h3>Les étapes de la division euclidienne</h3>
            <ol>
              <li>
                <strong>Sélection du dividende</strong> : Choisir les premiers
                chiffres du dividende pour commencer la division.
              </li>
              <li>
                <strong>Calcul du quotient</strong> : Déterminer combien de fois
                le diviseur est contenu dans la partie sélectionnée.
              </li>
              <li>
                <strong>Multiplication</strong> : Multiplier le diviseur par le
                chiffre du quotient trouvé.
              </li>
              <li>
                <strong>Soustraction</strong> : Soustraire le produit obtenu de
                la partie sélectionnée du dividende.
              </li>
              <li>
                <strong>Abaissement</strong> : Abaisser le chiffre suivant du
                dividende et recommencer le processus.
              </li>
            </ol>
            <p>
              Ces étapes sont répétées jusqu'à ce que tous les chiffres du
              dividende aient été traités.
            </p>
          </div>

          <div id="tab-astuces" class="tab-pane">
            <h3>Astuces pour réussir</h3>
            <ul>
              <li>
                <strong>Estimation</strong> : Pour trouver le chiffre du
                quotient, cherchez le plus grand nombre qui, multiplié par le
                diviseur, donne un résultat inférieur ou égal à la partie
                sélectionnée du dividende.
              </li>
              <li>
                <strong>Vérification</strong> : À chaque étape, vérifiez que le
                reste est bien inférieur au diviseur.
              </li>
              <li>
                <strong>Utilisation du tableau</strong> : Le tableau Excel vous
                aide à visualiser les opérations. Utilisez le zoom si
                nécessaire.
              </li>
              <li>
                <strong>Pratique</strong> : Plus vous ferez de divisions, plus
                vous deviendrez rapide et précis.
              </li>
            </ul>
            <p>
              N'hésitez pas à utiliser le bouton "Recommencer" si vous souhaitez
              essayer avec d'autres nombres.
            </p>
          </div>
        </div>

        <div class="modal-footer">
          <button id="general-help-close" class="btn-primary">Compris !</button>
        </div>
      </div>
    </div>

    <!-- Section À propos -->
    <div id="about-modal" class="modal-overlay hidden">
      <div class="modal-content">
        <button class="modal-close" id="close-about" aria-label="Fermer">
          <i class="fas fa-times"></i>
        </button>
        <h2>À propos de cette application</h2>

        <div class="about-content">
          <p>
            Cette application de simulation de division euclidienne a été conçue
            pour aider les élèves et enseignants dans l'apprentissage des
            mathématiques.
          </p>

          <h3>Fonctionnalités</h3>
          <ul>
            <li>Interface interactive et intuitive</li>
            <li>Guidage pas à pas dans le processus de division</li>
            <li>Visualisation des opérations dans un tableau Excel</li>
            <li>Mode sombre/clair pour un confort visuel optimal</li>
            <li>Design responsive adapté à tous les appareils</li>
          </ul>

          <h3>Technologies utilisées</h3>
          <p>
            Cette application a été développée avec HTML5, CSS3 et JavaScript
            vanilla, sans dépendances externes autres que Font Awesome pour les
            icônes.
          </p>

          <h3>Version</h3>
          <p>
            Version 1.2.0 - Dernière mise à jour :
            <span id="last-update-date">Mai 2023</span>
          </p>
        </div>

        <div class="modal-footer">
          <button id="about-close" class="btn-primary">Fermer</button>
        </div>
      </div>
    </div>

    <footer>
      <div class="footer-content">
        <div class="footer-section">
          <h3>Simulation de Division Euclidienne</h3>
          <p>
            Un outil pédagogique interactif pour apprendre la division
            euclidienne étape par étape. Idéal pour les élèves et enseignants.
          </p>
          <div class="social-links">
            <a
              href="#"
              aria-label="Partager sur Facebook"
              title="Partager sur Facebook"
              rel="noopener"
            >
              <i class="fab fa-facebook-f" aria-hidden="true"></i>
            </a>
            <a
              href="#"
              aria-label="Partager sur Twitter"
              title="Partager sur Twitter"
              rel="noopener"
            >
              <i class="fab fa-twitter" aria-hidden="true"></i>
            </a>
            <a
              href="#"
              aria-label="Partager sur LinkedIn"
              title="Partager sur LinkedIn"
              rel="noopener"
            >
              <i class="fab fa-linkedin-in" aria-hidden="true"></i>
            </a>
          </div>
        </div>

        <div class="footer-section">
          <h3>Navigation</h3>
          <ul>
            <li>
              <a
                href="#"
                id="footer-simulation"
                aria-label="Aller à la simulation"
                >Simulation</a
              >
            </li>
            <li>
              <a href="#" id="footer-aide" aria-label="Consulter l'aide"
                >Aide</a
              >
            </li>
            <li>
              <a
                href="#"
                id="footer-a-propos"
                aria-label="À propos de cette application"
                >À propos</a
              >
            </li>
          </ul>
        </div>

        <div class="footer-section">
          <h3>Ressources</h3>
          <ul>
            <li>
              <a
                href="#"
                id="export-pdf"
                aria-label="Exporter les résultats en PDF"
              >
                <i class="fas fa-file-pdf" aria-hidden="true"></i> Exporter en
                PDF
              </a>
            </li>
            <li>
              <a
                href="#"
                id="print-simulation"
                aria-label="Imprimer les résultats"
              >
                <i class="fas fa-print" aria-hidden="true"></i> Imprimer
              </a>
            </li>
            <li>
              <a
                href="#"
                id="share-simulation"
                aria-label="Partager cette application"
              >
                <i class="fas fa-share-alt" aria-hidden="true"></i> Partager
              </a>
            </li>
          </ul>
        </div>

        <div class="footer-section">
          <h3>Préférences</h3>
          <div class="footer-preferences">
            <button
              id="footer-theme-toggle"
              class="footer-btn"
              aria-label="Changer de thème"
            >
              <i class="fas fa-moon" aria-hidden="true"></i> Mode sombre
            </button>
            <button
              id="footer-accessibility"
              class="footer-btn"
              aria-label="Options d'accessibilité"
            >
              <i class="fas fa-universal-access" aria-hidden="true"></i>
              Accessibilité
            </button>
          </div>
        </div>
      </div>

      <div class="footer-bottom">
        <p>
          &copy; <span id="current-year">2023</span> Ali MESSAOUDI - smartDiv.
          Tous droits réservés.
        </p>
        <p class="footer-version">
          Version 1.0.0 |
          <a
            href="#"
            id="footer-privacy"
            aria-label="Politique de confidentialité"
            >Confidentialité</a
          >
          |
          <a href="LICENSE.txt" id="footer-license" aria-label="Licence"
            >Licence</a
          >
        </p>
      </div>
    </footer>

    <!-- Structured Data for SEO -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "EducationalApplication",
        "name": "Simulation de Division Euclidienne",
        "description": "Simulation interactive de division euclidienne pour l'apprentissage des mathématiques. Outil pédagogique gratuit pour comprendre et pratiquer la division euclidienne étape par étape.",
        "applicationCategory": "EducationalApplication",
        "operatingSystem": "Web",
        "offers": {
          "@type": "Offer",
          "price": "0",
          "priceCurrency": "EUR"
        },
        "audience": {
          "@type": "EducationalAudience",
          "educationalRole": "student"
        },
        "educationalUse": "Interactive Teaching",
        "learningResourceType": "Simulation"
      }
    </script>

    <!-- Panneau d'accessibilité -->
    <div id="accessibility-panel" class="panel hidden">
      <div class="panel-header">
        <h3>Options d'accessibilité</h3>
        <button class="panel-close" aria-label="Fermer le panneau">
          <i class="fas fa-times" aria-hidden="true"></i>
        </button>
      </div>
      <div class="panel-content">
        <div class="option-group">
          <label for="font-size">Taille du texte</label>
          <div class="range-control">
            <button
              class="btn-small"
              id="decrease-font"
              aria-label="Diminuer la taille du texte"
            >
              A-
            </button>
            <input
              type="range"
              id="font-size"
              min="80"
              max="150"
              value="100"
              aria-label="Taille du texte"
            />
            <button
              class="btn-small"
              id="increase-font"
              aria-label="Augmenter la taille du texte"
            >
              A+
            </button>
          </div>
        </div>
        <div class="option-group">
          <label for="contrast-mode">Contraste élevé</label>
          <label class="switch">
            <input type="checkbox" id="contrast-mode" />
            <span class="slider round"></span>
          </label>
        </div>
        <div class="option-group">
          <label for="dyslexic-font">Police pour dyslexiques</label>
          <label class="switch">
            <input type="checkbox" id="dyslexic-font" />
            <span class="slider round"></span>
          </label>
        </div>
        <div class="option-group">
          <label for="reduce-motion">Réduire les animations</label>
          <label class="switch">
            <input type="checkbox" id="reduce-motion" />
            <span class="slider round"></span>
          </label>
        </div>
        <button id="reset-accessibility" class="btn-secondary">
          Réinitialiser
        </button>
      </div>
    </div>

    <!-- Scripts -->
    <script src="script.js" defer></script>
    <script src="excel-enhancements.js" defer></script>
    <script src="excel-cell-enhancer.js" defer></script>
    <script src="excel-math-operations.js" defer></script>
    <script src="navigation-enhancements.js" defer></script>
    <script src="modern-nav.js" defer></script>
    <script src="auth-nav.js" defer></script>
    <!-- Nouveau script d'interface modernisée -->
    <script src="modern-ui.js" defer></script>
    <script>
      // Mettre à jour l'année automatiquement et initialiser les fonctionnalités
      document.addEventListener("DOMContentLoaded", function () {
        // Mettre à jour l'année
        document.getElementById("current-year").textContent =
          new Date().getFullYear();

        // Enregistrer le service worker pour le support hors ligne
        if ("serviceWorker" in navigator) {
          navigator.serviceWorker
            .register("service-worker.js")
            .then(function (registration) {
              console.log(
                "Service Worker enregistré avec succès:",
                registration.scope
              );
            })
            .catch(function (error) {
              console.log(
                "Échec de l'enregistrement du Service Worker:",
                error
              );
            });
        }

        // Précharger les ressources importantes
        const preloadResources = () => {
          // Précharger les images importantes
          const imagesToPreload = document.querySelectorAll("img[data-src]");
          if ("IntersectionObserver" in window) {
            const imageObserver = new IntersectionObserver((entries) => {
              entries.forEach((entry) => {
                if (entry.isIntersecting) {
                  const img = entry.target;
                  img.src = img.dataset.src;
                  img.removeAttribute("data-src");
                  imageObserver.unobserve(img);
                }
              });
            });

            imagesToPreload.forEach((image) => {
              imageObserver.observe(image);
            });
          } else {
            // Fallback pour les navigateurs qui ne supportent pas IntersectionObserver
            imagesToPreload.forEach((image) => {
              image.src = image.dataset.src;
              image.removeAttribute("data-src");
            });
          }
        };

        // Appeler la fonction de préchargement
        preloadResources();
      });
    </script>

    <!-- Script pour gérer la navigation vers les pages d'authentification -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Fonction pour naviguer vers la page de connexion
        document
          .getElementById("nav-login")
          .addEventListener("click", function () {
            window.location.href = "auth.html";
          });

        // Fonction pour naviguer vers la page d'inscription
        document
          .getElementById("nav-register")
          .addEventListener("click", function () {
            window.location.href = "auth.html#register";
          });
      });
    </script>

    <!-- Script pour gérer les boutons d'en-tête du tableau Excel -->
    <script src="excel-header-buttons.js"></script>
  </body>
</html>
