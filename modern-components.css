/* 
 * smartDiv - Composants d'interface moderne
 * Ce fichier contient les styles pour les composants d'interface modernisés
 */

/* ===== BOUTONS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.625rem 1.25rem;
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-base);
  transition: all var(--transition-fast);
  cursor: pointer;
  border: none;
  gap: 0.5rem;
  text-decoration: none;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--color-primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--color-secondary);
  color: white;
}

.btn-secondary:hover {
  background-color: var(--color-secondary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(60, 207, 207, 0.2);
}

.btn-accent {
  background-color: var(--color-accent);
  color: white;
}

.btn-accent:hover {
  background-color: var(--color-accent-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 122, 0, 0.2);
}

.btn-outline {
  background-color: transparent;
  border: 2px solid var(--color-primary);
  color: var(--color-primary);
}

.btn-outline:hover {
  background-color: var(--color-primary);
  color: white;
}

.btn-success {
  background-color: var(--color-success);
  color: white;
}

.btn-warning {
  background-color: var(--color-warning);
  color: var(--color-text-primary);
}

.btn-error {
  background-color: var(--color-error);
  color: white;
}

.btn-icon {
  width: 2.5rem;
  height: 2.5rem;
  padding: 0;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: var(--font-size-sm);
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: var(--font-size-lg);
}

.btn-block {
  display: flex;
  width: 100%;
}

/* ===== CARTES ===== */
.card {
  background-color: var(--color-background);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: 1.5rem;
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
  overflow: hidden;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.card-header {
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--color-border);
  padding-bottom: 1rem;
}

.card-title {
  font-size: var(--font-size-lg);
  margin: 0;
}

.card-body {
  margin-bottom: 1rem;
}

.card-footer {
  border-top: 1px solid var(--color-border);
  padding-top: 1rem;
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.card-step {
  background-color: var(--color-background);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: 1.5rem;
  margin-bottom: var(--space-lg);
  border-left: 4px solid var(--color-primary);
}

.card-step.active {
  border-left-color: var(--color-accent);
  box-shadow: var(--shadow-lg);
}

/* ===== FORMULAIRES ===== */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

.form-control {
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: var(--font-size-base);
  line-height: 1.5;
  color: var(--color-text-primary);
  background-color: var(--color-background);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-md);
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-control:focus {
  border-color: var(--color-primary);
  outline: 0;
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
}

.form-control::placeholder {
  color: var(--color-text-secondary);
  opacity: 0.7;
}

/* Style amélioré pour les boutons radio et cases à cocher */
.radio-group, .checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.radio-option, .checkbox-option {
  position: relative;
  display: flex;
  align-items: center;
}

.radio-option input[type="radio"],
.checkbox-option input[type="checkbox"] {
  position: absolute;
  opacity: 0;
}

.radio-option label,
.checkbox-option label {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border: 2px solid var(--color-border);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.radio-option input[type="radio"]:checked + label,
.checkbox-option input[type="checkbox"]:checked + label {
  border-color: var(--color-primary);
  background-color: rgba(67, 97, 238, 0.1);
}

.radio-option input[type="radio"]:focus + label,
.checkbox-option input[type="checkbox"]:focus + label {
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
}

/* ===== MISE EN PAGE ===== */
.container {
  width: 100%;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--space-lg);
  padding-right: var(--space-lg);
}

.container-sm {
  max-width: 640px;
}

.container-md {
  max-width: 768px;
}

.container-lg {
  max-width: 1024px;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin-left: calc(var(--space-md) * -1);
  margin-right: calc(var(--space-md) * -1);
}

.col {
  flex: 1 0 0%;
  padding-left: var(--space-md);
  padding-right: var(--space-md);
}

/* Colonnes de taille fixe */
.col-1 { flex: 0 0 8.333333%; }
.col-2 { flex: 0 0 16.666667%; }
.col-3 { flex: 0 0 25%; }
.col-4 { flex: 0 0 33.333333%; }
.col-6 { flex: 0 0 50%; }
.col-8 { flex: 0 0 66.666667%; }
.col-9 { flex: 0 0 75%; }
.col-12 { flex: 0 0 100%; }

/* ===== SECTIONS ===== */
.section {
  padding-top: var(--space-2xl);
  padding-bottom: var(--space-2xl);
}

.section-sm {
  padding-top: var(--space-xl);
  padding-bottom: var(--space-xl);
}

.section-lg {
  padding-top: var(--space-3xl);
  padding-bottom: var(--space-3xl);
}

.section-divider {
  height: 1px;
  background-color: var(--color-border);
  margin: var(--space-2xl) 0;
}

.section-title {
  position: relative;
  margin-bottom: var(--space-xl);
  padding-bottom: var(--space-sm);
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 4px;
  background-color: var(--color-primary);
  border-radius: 2px;
}
