<!DOCTYPE html>
<!--
  Copyright (c) 2023 Ali MESSAOUDI
  Tous droits réservés.
  Ce code est protégé par les lois sur le droit d'auteur et ne peut être reproduit,
  distribué ou utilisé sans autorisation explicite de l'auteur.
-->
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Tableau de bord élève - Simulation de Division Euclidienne</title>
    <link rel="stylesheet" href="styles.css" />
    <link rel="stylesheet" href="auth-styles.css" />
    <link rel="stylesheet" href="dashboard-styles.css" />
    <link rel="stylesheet" href="modern-nav.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <header>
      <div class="header-content">
        <h1>
          <a href="index.html" class="logo-link">
            <img
              src="images/logo.jpg"
              alt="Logo smartDiv"
              class="logo-image"
              width="80"
              height="80"
            />
            smartDiv
          </a>
        </h1>
        <div class="header-controls">
          <div id="user-section" class="user-section">
            <button
              id="user-button"
              class="btn-auth"
              aria-label="Menu utilisateur"
              title="Menu utilisateur"
            >
              <i class="fas fa-user-circle" aria-hidden="true"></i>
              <span id="user-name-display">Utilisateur</span>
            </button>
            <div id="user-menu" class="user-menu hidden">
              <div class="user-info">
                <div class="user-avatar">
                  <i class="fas fa-user-circle"></i>
                </div>
                <div class="user-details">
                  <span id="user-name">Nom d'utilisateur</span>
                  <span id="user-role" class="user-role">Élève</span>
                </div>
              </div>
              <ul class="user-actions">
                <li>
                  <a href="student-dashboard.html" id="dashboard-link"
                    ><i class="fas fa-tachometer-alt"></i> Tableau de bord</a
                  >
                </li>
                <li>
                  <a href="#" id="profile-link"
                    ><i class="fas fa-id-card"></i> Mon profil</a
                  >
                </li>
                <li>
                  <a href="#" id="logout-link"
                    ><i class="fas fa-sign-out-alt"></i> Déconnexion</a
                  >
                </li>
              </ul>
            </div>
          </div>
          <button
            id="theme-toggle"
            class="btn-icon"
            aria-label="Changer de thème"
            title="Changer de thème"
          >
            <i class="fas fa-moon" aria-hidden="true"></i>
            <span class="visually-hidden">Mode sombre</span>
          </button>
        </div>
      </div>
      <nav class="main-nav" aria-label="Navigation principale">
        <div class="main-nav-container">
          <button
            class="mobile-menu-toggle"
            aria-label="Menu"
            aria-expanded="false"
            id="mobile-menu-toggle"
          >
            <div class="hamburger">
              <span></span>
              <span></span>
              <span></span>
              <span></span>
            </div>
          </button>

          <ul>
            <li>
              <a href="index.html"><i class="fas fa-calculator"></i> Accueil</a>
            </li>
            <li>
              <a
                href="student-dashboard.html"
                class="active"
                aria-current="page"
              >
                <i class="fas fa-tachometer-alt"></i> Tableau de bord
              </a>
            </li>
            <li>
              <a href="#" id="nav-exercises"
                ><i class="fas fa-tasks"></i> Exercices</a
              >
            </li>
            <li>
              <a href="#" id="nav-progress"
                ><i class="fas fa-chart-line"></i> Ma progression</a
              >
            </li>
          </ul>
        </div>
      </nav>
    </header>

    <main class="dashboard-main">
      <div class="dashboard-container">
        <div class="dashboard-header">
          <h2>Tableau de bord</h2>
          <p class="welcome-message">
            Bienvenue, <span id="welcome-name">Élève</span> !
          </p>
        </div>

        <div class="dashboard-stats">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-tasks"></i>
            </div>
            <div class="stat-content">
              <h3>Exercices complétés</h3>
              <p class="stat-value">12</p>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            <div class="stat-content">
              <h3>Taux de réussite</h3>
              <p class="stat-value">85%</p>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-medal"></i>
            </div>
            <div class="stat-content">
              <h3>Badges obtenus</h3>
              <p class="stat-value">5</p>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-clock"></i>
            </div>
            <div class="stat-content">
              <h3>Temps d'apprentissage</h3>
              <p class="stat-value">3h 45min</p>
            </div>
          </div>
        </div>

        <div class="dashboard-sections">
          <div class="dashboard-section">
            <div class="section-header">
              <h3>Exercices récents</h3>
              <a href="#" class="section-link">Voir tous les exercices</a>
            </div>
            <div class="section-content">
              <div class="exercise-list">
                <div class="exercise-item">
                  <div class="exercise-icon">
                    <i class="fas fa-divide"></i>
                  </div>
                  <div class="exercise-details">
                    <h4>Division avec reste</h4>
                    <p>Difficulté: Facile</p>
                    <div class="exercise-meta">
                      <span class="exercise-date">Aujourd'hui</span>
                      <span class="exercise-score">Score: 9/10</span>
                    </div>
                  </div>
                  <div class="exercise-actions">
                    <button class="btn-secondary">Refaire</button>
                  </div>
                </div>

                <div class="exercise-item">
                  <div class="exercise-icon">
                    <i class="fas fa-divide"></i>
                  </div>
                  <div class="exercise-details">
                    <h4>Division décimale</h4>
                    <p>Difficulté: Moyenne</p>
                    <div class="exercise-meta">
                      <span class="exercise-date">Hier</span>
                      <span class="exercise-score">Score: 7/10</span>
                    </div>
                  </div>
                  <div class="exercise-actions">
                    <button class="btn-secondary">Refaire</button>
                  </div>
                </div>

                <div class="exercise-item">
                  <div class="exercise-icon">
                    <i class="fas fa-divide"></i>
                  </div>
                  <div class="exercise-details">
                    <h4>Division euclidienne</h4>
                    <p>Difficulté: Difficile</p>
                    <div class="exercise-meta">
                      <span class="exercise-date">Il y a 2 jours</span>
                      <span class="exercise-score">Score: 8/10</span>
                    </div>
                  </div>
                  <div class="exercise-actions">
                    <button class="btn-secondary">Refaire</button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="dashboard-section">
            <div class="section-header">
              <h3>Progression</h3>
              <a href="#" class="section-link">Voir les détails</a>
            </div>
            <div class="section-content">
              <div class="progress-chart">
                <div class="chart-placeholder">
                  <div class="chart-bars">
                    <div class="chart-bar" style="height: 40%">
                      <span class="chart-value">40%</span>
                    </div>
                    <div class="chart-bar" style="height: 65%">
                      <span class="chart-value">65%</span>
                    </div>
                    <div class="chart-bar" style="height: 85%">
                      <span class="chart-value">85%</span>
                    </div>
                    <div class="chart-bar" style="height: 70%">
                      <span class="chart-value">70%</span>
                    </div>
                    <div class="chart-bar" style="height: 90%">
                      <span class="chart-value">90%</span>
                    </div>
                  </div>
                  <div class="chart-labels">
                    <span>Division simple</span>
                    <span>Division avec reste</span>
                    <span>Division décimale</span>
                    <span>Division euclidienne</span>
                    <span>Problèmes</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="dashboard-sections">
          <div class="dashboard-section">
            <div class="section-header">
              <h3>Badges et récompenses</h3>
              <a href="#" class="section-link">Voir tous les badges</a>
            </div>
            <div class="section-content">
              <div class="badges-grid">
                <div class="badge-item">
                  <div class="badge-icon">
                    <i class="fas fa-star"></i>
                  </div>
                  <div class="badge-name">Premier pas</div>
                </div>
                <div class="badge-item">
                  <div class="badge-icon">
                    <i class="fas fa-trophy"></i>
                  </div>
                  <div class="badge-name">Expert en division</div>
                </div>
                <div class="badge-item">
                  <div class="badge-icon">
                    <i class="fas fa-bolt"></i>
                  </div>
                  <div class="badge-name">Rapidité</div>
                </div>
                <div class="badge-item">
                  <div class="badge-icon">
                    <i class="fas fa-calendar-check"></i>
                  </div>
                  <div class="badge-name">Assiduité</div>
                </div>
                <div class="badge-item badge-locked">
                  <div class="badge-icon">
                    <i class="fas fa-lock"></i>
                  </div>
                  <div class="badge-name">Perfection</div>
                </div>
                <div class="badge-item badge-locked">
                  <div class="badge-icon">
                    <i class="fas fa-lock"></i>
                  </div>
                  <div class="badge-name">Maître diviseur</div>
                </div>
              </div>
            </div>
          </div>

          <div class="dashboard-section">
            <div class="section-header">
              <h3>Recommandations</h3>
            </div>
            <div class="section-content">
              <div class="recommendations-list">
                <div class="recommendation-item">
                  <div class="recommendation-icon">
                    <i class="fas fa-lightbulb"></i>
                  </div>
                  <div class="recommendation-content">
                    <h4>Division avec reste</h4>
                    <p>
                      Continuez à pratiquer pour améliorer votre compréhension
                      des restes.
                    </p>
                    <button class="btn-primary">Commencer</button>
                  </div>
                </div>
                <div class="recommendation-item">
                  <div class="recommendation-icon">
                    <i class="fas fa-lightbulb"></i>
                  </div>
                  <div class="recommendation-content">
                    <h4>Problèmes de division</h4>
                    <p>
                      Essayez de résoudre des problèmes concrets utilisant la
                      division.
                    </p>
                    <button class="btn-primary">Commencer</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <footer>
      <div class="footer-content">
        <div class="footer-section">
          <h3>smartDiv</h3>
          <p>
            Une application éducative pour apprendre la division euclidienne.
          </p>
        </div>
        <div class="footer-section">
          <h3>Liens utiles</h3>
          <ul>
            <li><a href="index.html">Accueil</a></li>
            <li><a href="student-dashboard.html">Tableau de bord</a></li>
            <li><a href="#">Exercices</a></li>
          </ul>
        </div>
        <div class="footer-section">
          <h3>Contact</h3>
          <p><i class="fas fa-envelope"></i> <EMAIL></p>
        </div>
      </div>
      <div class="footer-bottom">
        <p>
          &copy; <span id="current-year">2023</span> Ali MESSAOUDI - smartDiv -
          Tous droits réservés
        </p>
        <p class="footer-version">
          <a href="LICENSE.txt" id="footer-license" aria-label="Licence"
            >Licence</a
          >
        </p>
      </div>
    </footer>

    <div id="toast-container" class="toast-container"></div>

    <script src="modern-nav.js"></script>
    <script src="dashboard.js"></script>
    <script>
      // Mettre à jour l'année automatiquement
      document.addEventListener("DOMContentLoaded", function () {
        // Mettre à jour l'année
        const yearElement = document.getElementById("current-year");
        if (yearElement) {
          yearElement.textContent = new Date().getFullYear();
        }
      });
    </script>
  </body>
</html>
