/**
 * Système d'internationalisation (i18n) pour smartDiv
 * 
 * Ce module gère la traduction de l'interface utilisateur en plusieurs langues.
 * Il supporte le chargement dynamique des traductions et la détection automatique
 * de la langue préférée de l'utilisateur.
 * 
 * Langues supportées :
 * - Français (fr) - par défaut
 * - <PERSON><PERSON><PERSON> (en)
 * - Espagnol (es)
 * - Allemand (de)
 * 
 * <AUTHOR> MESSAOUDI
 * @version 1.0.0
 */

class I18n {
  constructor() {
    this.currentLanguage = 'fr';
    this.translations = {};
    this.fallbackLanguage = 'fr';
    this.supportedLanguages = ['fr', 'en', 'es', 'de'];
    
    // Détecter la langue préférée de l'utilisateur
    this.detectLanguage();
    
    // Charger les traductions
    this.loadTranslations();
  }

  /**
   * Détecte la langue préférée de l'utilisateur
   */
  detectLanguage() {
    // 1. Vérifier le localStorage
    const savedLanguage = localStorage.getItem('smartdiv-language');
    if (savedLanguage && this.supportedLanguages.includes(savedLanguage)) {
      this.currentLanguage = savedLanguage;
      return;
    }

    // 2. Vérifier les paramètres URL
    const urlParams = new URLSearchParams(window.location.search);
    const urlLanguage = urlParams.get('lang');
    if (urlLanguage && this.supportedLanguages.includes(urlLanguage)) {
      this.currentLanguage = urlLanguage;
      this.saveLanguage(urlLanguage);
      return;
    }

    // 3. Détecter la langue du navigateur
    const browserLanguage = navigator.language || navigator.userLanguage;
    const languageCode = browserLanguage.split('-')[0];
    if (this.supportedLanguages.includes(languageCode)) {
      this.currentLanguage = languageCode;
      this.saveLanguage(languageCode);
      return;
    }

    // 4. Utiliser la langue par défaut
    this.currentLanguage = this.fallbackLanguage;
  }

  /**
   * Sauvegarde la langue choisie
   */
  saveLanguage(language) {
    localStorage.setItem('smartdiv-language', language);
  }

  /**
   * Charge les traductions pour la langue courante
   */
  async loadTranslations() {
    try {
      const response = await fetch(`locales/${this.currentLanguage}.json`);
      if (!response.ok) {
        throw new Error(`Failed to load translations for ${this.currentLanguage}`);
      }
      this.translations = await response.json();
      
      // Appliquer les traductions à la page
      this.applyTranslations();
      
      // Émettre un événement pour notifier que les traductions sont chargées
      document.dispatchEvent(new CustomEvent('i18n:loaded', {
        detail: { language: this.currentLanguage }
      }));
      
    } catch (error) {
      console.warn(`Failed to load translations for ${this.currentLanguage}:`, error);
      
      // Charger la langue de fallback si ce n'est pas déjà fait
      if (this.currentLanguage !== this.fallbackLanguage) {
        this.currentLanguage = this.fallbackLanguage;
        await this.loadTranslations();
      }
    }
  }

  /**
   * Obtient une traduction par sa clé
   * @param {string} key - Clé de traduction (ex: "nav.simulation")
   * @param {object} params - Paramètres pour l'interpolation
   * @returns {string} - Texte traduit
   */
  t(key, params = {}) {
    const keys = key.split('.');
    let value = this.translations;

    // Naviguer dans l'objet de traductions
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        console.warn(`Translation key not found: ${key}`);
        return key; // Retourner la clé si la traduction n'est pas trouvée
      }
    }

    // Interpolation des paramètres
    if (typeof value === 'string' && Object.keys(params).length > 0) {
      return value.replace(/\{\{(\w+)\}\}/g, (match, param) => {
        return params[param] || match;
      });
    }

    return value;
  }

  /**
   * Change la langue de l'application
   * @param {string} language - Code de langue
   */
  async changeLanguage(language) {
    if (!this.supportedLanguages.includes(language)) {
      console.warn(`Unsupported language: ${language}`);
      return;
    }

    this.currentLanguage = language;
    this.saveLanguage(language);
    await this.loadTranslations();
  }

  /**
   * Applique les traductions aux éléments avec l'attribut data-i18n
   */
  applyTranslations() {
    const elements = document.querySelectorAll('[data-i18n]');
    
    elements.forEach(element => {
      const key = element.getAttribute('data-i18n');
      const translation = this.t(key);
      
      if (translation !== key) {
        // Vérifier si l'élément a un attribut data-i18n-attr pour traduire un attribut
        const attr = element.getAttribute('data-i18n-attr');
        if (attr) {
          element.setAttribute(attr, translation);
        } else {
          element.textContent = translation;
        }
      }
    });

    // Mettre à jour l'attribut lang du document
    document.documentElement.lang = this.currentLanguage;
  }

  /**
   * Obtient la langue courante
   * @returns {string} - Code de langue
   */
  getCurrentLanguage() {
    return this.currentLanguage;
  }

  /**
   * Obtient la liste des langues supportées
   * @returns {Array} - Liste des codes de langue
   */
  getSupportedLanguages() {
    return this.supportedLanguages;
  }

  /**
   * Obtient les informations sur une langue
   * @param {string} language - Code de langue
   * @returns {object} - Informations sur la langue
   */
  getLanguageInfo(language) {
    const languageNames = {
      'fr': { name: 'Français', nativeName: 'Français', flag: '🇫🇷' },
      'en': { name: 'English', nativeName: 'English', flag: '🇬🇧' },
      'es': { name: 'Español', nativeName: 'Español', flag: '🇪🇸' },
      'de': { name: 'Deutsch', nativeName: 'Deutsch', flag: '🇩🇪' }
    };

    return languageNames[language] || { name: language, nativeName: language, flag: '🌐' };
  }
}

// Instance globale
window.i18n = new I18n();

// Fonction utilitaire globale pour les traductions
window.t = (key, params) => window.i18n.t(key, params);

// Exporter pour les modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = I18n;
}
