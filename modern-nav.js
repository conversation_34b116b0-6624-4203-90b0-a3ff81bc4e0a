/**
 * modern-nav.js
 *
 * Copyright (c) 2023-2024 Ali MESSAOUDI
 * Tous droits réservés.
 * Ce code est protégé par les lois sur le droit d'auteur et ne peut être reproduit,
 * distribué ou utilisé sans autorisation explicite de l'auteur.
 *
 * Script pour la navigation moderne
 * Gère le menu mobile, les animations, les interactions et l'accessibilité
 *
 * Fonctionnalités :
 * - Menu mobile responsive avec animation
 * - Gestion des sous-menus et dropdowns
 * - Support complet de l'accessibilité (ARIA, navigation au clavier)
 * - Indicateurs de défilement pour la navigation horizontale
 * - Gestion des utilisateurs et des rôles
 * - Animations optimisées pour les performances
 * - Support des préférences utilisateur (réduction des animations)
 */
class ModernNavigation {
  constructor() {
    // Éléments DOM
    this.nav = document.querySelector('.main-nav');
    this.navContainer = document.querySelector('.main-nav-container');
    this.mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    this.userNav = document.querySelector('.user-nav');
    this.userDropdown = document.querySelector('.user-dropdown');
    this.dropdownUserName = document.getElementById('dropdown-user-name');
    this.dropdownUserRole = document.getElementById('dropdown-user-role');
    this.navUsername = document.getElementById('nav-username');
    this.header = document.querySelector('header');

    // Éléments de navigation spécifiques au rôle
    this.teacherNav = document.querySelector('.teacher-nav');
    this.studentNav = document.querySelector('.student-nav');
    this.teacherDashboardLink = document.querySelector('.teacher-dashboard-link');
    this.studentDashboardLink = document.querySelector('.student-dashboard-link');

    // Éléments pour l'accessibilité
    this.srAnnouncer = this.createScreenReaderAnnouncer();

    // État
    this.isMobileMenuOpen = false;
    this.currentUser = null;
    this.lastScrollTop = 0;
    this.scrollThreshold = 50;
    this.prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

    // Initialisation
    this.init();
  }

  /**
   * Crée un élément pour annoncer les changements aux lecteurs d'écran
   */
  createScreenReaderAnnouncer() {
    let announcer = document.getElementById('sr-announcer');

    if (!announcer) {
      announcer = document.createElement('div');
      announcer.id = 'sr-announcer';
      announcer.setAttribute('aria-live', 'polite');
      announcer.setAttribute('class', 'sr-only');
      announcer.style.position = 'absolute';
      announcer.style.width = '1px';
      announcer.style.height = '1px';
      announcer.style.padding = '0';
      announcer.style.margin = '-1px';
      announcer.style.overflow = 'hidden';
      announcer.style.clip = 'rect(0, 0, 0, 0)';
      announcer.style.whiteSpace = 'nowrap';
      announcer.style.border = '0';
      document.body.appendChild(announcer);
    }

    return announcer;
  }

  /**
   * Initialise la navigation
   */
  init() {
    // Charger l'utilisateur depuis le stockage local
    this.loadUserFromStorage();

    // Ajouter les écouteurs d'événements
    this.addEventListeners();

    // Mettre à jour l'interface utilisateur
    this.updateUserInfo();

    // Vérifier si on est sur mobile
    this.checkMobileView();

    // Ajouter des indicateurs de défilement si nécessaire
    this.checkScrollIndicators();

    // Configurer l'accessibilité des sous-menus
    this.setupSubmenuAccessibility();

    // Configurer l'effet de défilement pour l'en-tête
    this.setupHeaderScrollEffect();

    // Mettre en évidence le lien actif
    this.highlightActiveNavLink();

    // Ajouter une classe pour indiquer que JavaScript est activé
    document.documentElement.classList.add('js-enabled');

    // Annoncer que la navigation est prête (pour les tests)
    console.log('Navigation moderne initialisée');
  }

  /**
   * Charge l'utilisateur depuis le stockage local
   */
  loadUserFromStorage() {
    const storedUser = localStorage.getItem('currentUser');
    if (storedUser) {
      try {
        this.currentUser = JSON.parse(storedUser);
      } catch (error) {
        console.error('Erreur lors du chargement de l\'utilisateur:', error);
        localStorage.removeItem('currentUser');
      }
    }
  }

  /**
   * Ajoute les écouteurs d'événements
   */
  addEventListeners() {
    // Menu mobile
    if (this.mobileMenuToggle) {
      this.mobileMenuToggle.addEventListener('click', () => {
        this.toggleMobileMenu();
      });

      // Accessibilité du clavier pour le menu mobile
      this.mobileMenuToggle.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          this.toggleMobileMenu();
        }
      });
    }

    // Fermeture du menu mobile en cliquant en dehors
    document.addEventListener('click', (e) => {
      if (this.isMobileMenuOpen &&
          !this.navContainer.contains(e.target) &&
          !this.mobileMenuToggle.contains(e.target)) {
        this.closeMobileMenu();
      }
    });

    // Gestion du redimensionnement de la fenêtre
    window.addEventListener('resize', () => {
      this.checkMobileView();
      this.checkScrollIndicators();
    });

    // Gestion du défilement horizontal dans la navigation
    if (this.nav) {
      this.nav.addEventListener('scroll', () => {
        this.checkScrollIndicators();
      });
    }

    // Gestion du défilement de la page pour l'effet d'en-tête
    window.addEventListener('scroll', () => {
      this.handleHeaderScroll();
    });

    // Gestion des liens de navigation
    const navLinks = document.querySelectorAll('.main-nav a');
    navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        // Si c'est un lien de menu déroulant, ne pas fermer le menu mobile
        if (!link.closest('.user-dropdown')) {
          this.closeMobileMenu();
        }
      });

      // Accessibilité du clavier pour les liens
      link.addEventListener('keydown', (e) => {
        // Gérer la navigation avec les flèches
        if (e.key === 'ArrowDown' || e.key === 'ArrowUp' || e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
          this.handleKeyboardNavigation(e, link);
        }
      });
    });

    // Écouter les changements de préférence pour les animations réduites
    const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    reducedMotionQuery.addEventListener('change', () => {
      this.prefersReducedMotion = reducedMotionQuery.matches;
      this.updateAnimationPreferences();
    });

    // Gestion du bouton de déconnexion
    const logoutLink = document.getElementById('logout-link');
    if (logoutLink) {
      logoutLink.addEventListener('click', (e) => {
        e.preventDefault();
        this.handleLogout();
      });
    }
  }

  /**
   * Gère la navigation au clavier entre les éléments de menu
   */
  handleKeyboardNavigation(event, currentLink) {
    const navLinks = Array.from(document.querySelectorAll('.main-nav a:not(.hidden)'));
    const currentIndex = navLinks.indexOf(currentLink);
    let nextIndex;

    switch (event.key) {
      case 'ArrowRight':
      case 'ArrowDown':
        event.preventDefault();
        nextIndex = (currentIndex + 1) % navLinks.length;
        navLinks[nextIndex].focus();
        break;
      case 'ArrowLeft':
      case 'ArrowUp':
        event.preventDefault();
        nextIndex = (currentIndex - 1 + navLinks.length) % navLinks.length;
        navLinks[nextIndex].focus();
        break;
    }
  }

  /**
   * Met à jour les préférences d'animation en fonction des préférences utilisateur
   */
  updateAnimationPreferences() {
    if (this.prefersReducedMotion) {
      document.documentElement.classList.add('reduced-motion');
    } else {
      document.documentElement.classList.remove('reduced-motion');
    }
  }

  /**
   * Met à jour les informations de l'utilisateur dans le menu déroulant
   */
  updateUserInfo() {
    if (this.currentUser) {
      // Mettre à jour le nom d'utilisateur dans la barre de navigation
      if (this.navUsername) {
        this.navUsername.textContent = this.currentUser.firstname;
      }

      // Mettre à jour les informations dans le menu déroulant
      if (this.dropdownUserName) {
        this.dropdownUserName.textContent = `${this.currentUser.firstname} ${this.currentUser.lastname}`;
      }

      if (this.dropdownUserRole) {
        this.dropdownUserRole.textContent = this.currentUser.role === 'teacher' ? 'Enseignant' : 'Élève';
      }

      // Afficher les liens appropriés en fonction du rôle
      const isTeacher = this.currentUser.role === 'teacher';

      // Liens dans la barre de navigation principale
      if (this.teacherNav) {
        this.teacherNav.classList.toggle('hidden', !isTeacher);
      }

      if (this.studentNav) {
        this.studentNav.classList.toggle('hidden', isTeacher);
      }

      // Liens dans le menu déroulant
      if (this.teacherDashboardLink) {
        this.teacherDashboardLink.classList.toggle('hidden', !isTeacher);
      }

      if (this.studentDashboardLink) {
        this.studentDashboardLink.classList.toggle('hidden', isTeacher);
      }
    }
  }

  /**
   * Vérifie si on est en vue mobile et ajuste l'interface en conséquence
   */
  checkMobileView() {
    const isMobile = window.innerWidth <= 768;

    if (isMobile) {
      // Fermer le menu mobile par défaut sur mobile
      this.closeMobileMenu();
    } else {
      // Sur desktop, s'assurer que le menu est visible
      if (this.nav) {
        this.nav.classList.remove('mobile-open');
        this.isMobileMenuOpen = false;

        if (this.mobileMenuToggle) {
          this.mobileMenuToggle.setAttribute('aria-expanded', 'false');
        }
      }
    }
  }

  /**
   * Vérifie s'il faut afficher les indicateurs de défilement
   */
  checkScrollIndicators() {
    if (!this.nav) return;

    const navList = this.nav.querySelector('ul');
    if (!navList) return;

    const hasHorizontalScroll = navList.scrollWidth > navList.clientWidth;
    const isScrolledToStart = navList.scrollLeft <= 10;
    const isScrolledToEnd = navList.scrollLeft + navList.clientWidth >= navList.scrollWidth - 10;

    // Ajouter ou supprimer les classes pour les indicateurs
    if (hasHorizontalScroll) {
      if (!isScrolledToStart) {
        this.nav.classList.add('scroll-left');
      } else {
        this.nav.classList.remove('scroll-left');
      }

      if (!isScrolledToEnd) {
        this.nav.classList.add('scroll-right');
      } else {
        this.nav.classList.remove('scroll-right');
      }
    } else {
      this.nav.classList.remove('scroll-left', 'scroll-right');
    }
  }

  /**
   * Ouvre ou ferme le menu mobile
   */
  toggleMobileMenu() {
    if (this.isMobileMenuOpen) {
      this.closeMobileMenu();
    } else {
      this.openMobileMenu();
    }
  }

  /**
   * Configure l'accessibilité des sous-menus pour la navigation au clavier
   */
  setupSubmenuAccessibility() {
    const submenuTriggers = document.querySelectorAll('.main-nav li.has-submenu > a');

    submenuTriggers.forEach(trigger => {
      // Ajouter des attributs ARIA
      trigger.setAttribute('aria-haspopup', 'true');
      trigger.setAttribute('aria-expanded', 'false');

      // Gérer l'ouverture/fermeture au clavier
      trigger.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          const expanded = trigger.getAttribute('aria-expanded') === 'true';
          trigger.setAttribute('aria-expanded', !expanded);

          // Trouver et afficher/masquer le sous-menu
          const submenu = trigger.nextElementSibling;
          if (submenu && submenu.classList.contains('submenu')) {
            submenu.classList.toggle('open');

            // Si le sous-menu est ouvert, focus sur le premier élément
            if (!expanded) {
              setTimeout(() => {
                const firstLink = submenu.querySelector('a');
                if (firstLink) firstLink.focus();
              }, 100);
            }

            // Annoncer l'état du sous-menu aux technologies d'assistance
            this.announceToScreenReader(`Sous-menu ${!expanded ? 'ouvert' : 'fermé'}`);
          }
        }
      });
    });
  }

  /**
   * Configure l'effet de réduction de l'en-tête lors du défilement
   */
  setupHeaderScrollEffect() {
    if (!this.header || this.prefersReducedMotion) return;

    // Initialiser l'état
    this.lastScrollTop = window.pageYOffset || document.documentElement.scrollTop;
  }

  /**
   * Gère l'effet de défilement de l'en-tête
   */
  handleHeaderScroll() {
    if (!this.header || this.prefersReducedMotion) return;

    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

    // Ajouter/supprimer la classe en fonction de la direction du défilement
    if (scrollTop > this.lastScrollTop && scrollTop > this.scrollThreshold) {
      this.header.classList.add('scrolled-down');
    } else {
      this.header.classList.remove('scrolled-down');
    }

    this.lastScrollTop = scrollTop;
  }

  /**
   * Met en évidence le lien actif dans la navigation
   */
  highlightActiveNavLink() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.main-nav a');

    navLinks.forEach(link => {
      // Supprimer la classe active de tous les liens
      link.classList.remove('active');
      link.removeAttribute('aria-current');

      // Vérifier si le lien correspond à la page actuelle
      const linkPath = new URL(link.href, window.location.origin).pathname;
      if (currentPath === linkPath ||
          (currentPath === '/' && linkPath.includes('index.html'))) {
        link.classList.add('active');
        link.setAttribute('aria-current', 'page');
      }
    });
  }

  /**
   * Annonce un message aux lecteurs d'écran
   */
  announceToScreenReader(message) {
    if (!this.srAnnouncer) return;

    this.srAnnouncer.textContent = message;

    // Effacer le message après un délai
    setTimeout(() => {
      this.srAnnouncer.textContent = '';
    }, 3000);
  }

  /**
   * Gère la déconnexion de l'utilisateur
   */
  handleLogout() {
    // Supprimer l'utilisateur du stockage local
    localStorage.removeItem('currentUser');
    this.currentUser = null;

    // Mettre à jour l'interface
    if (this.userNav) {
      this.userNav.classList.add('hidden');
    }

    const loginNav = document.querySelector('.login-nav');
    const registerNav = document.querySelector('.register-nav');

    if (loginNav) loginNav.classList.remove('hidden');
    if (registerNav) registerNav.classList.remove('hidden');

    // Masquer les éléments spécifiques au rôle
    if (this.teacherNav) this.teacherNav.classList.add('hidden');
    if (this.studentNav) this.studentNav.classList.add('hidden');

    // Annoncer la déconnexion
    this.announceToScreenReader('Vous avez été déconnecté');

    // Rediriger vers la page d'accueil
    window.location.href = 'index.html';
  }

  /**
   * Ouvre le menu mobile
   */
  openMobileMenu() {
    if (this.navContainer) {
      this.navContainer.classList.add('mobile-open');
      this.isMobileMenuOpen = true;

      if (this.mobileMenuToggle) {
        this.mobileMenuToggle.setAttribute('aria-expanded', 'true');
      }

      // Empêcher le défilement du body
      document.body.classList.add('menu-open');

      // Annoncer l'ouverture du menu aux technologies d'assistance
      this.announceToScreenReader('Menu ouvert');
    }
  }

  /**
   * Ferme le menu mobile
   */
  closeMobileMenu() {
    if (this.navContainer) {
      this.navContainer.classList.remove('mobile-open');
      this.isMobileMenuOpen = false;

      if (this.mobileMenuToggle) {
        this.mobileMenuToggle.setAttribute('aria-expanded', 'false');
      }

      // Réactiver le défilement du body
      document.body.classList.remove('menu-open');

      // Annoncer la fermeture du menu aux technologies d'assistance
      this.announceToScreenReader('Menu fermé');
    }
  }
}

// Initialiser la navigation lorsque le DOM est chargé
document.addEventListener('DOMContentLoaded', () => {
  new ModernNavigation();
});
