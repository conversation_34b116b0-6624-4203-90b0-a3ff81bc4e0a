/* Améliorations pour la feuille Excel */

/* Amélioration de la structure de base */
.excel-sheet-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 600px; /* Augmentation de la hauteur */
  background: var(--excel-bg);
  border: 1px solid var(--excel-border);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--medium-shadow);
  transition: all var(--transition-speed) ease;
  position: relative;
  min-width: 0;
}

.excel-sheet-header {
  background: var(--gradient-primary);
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.excel-sheet-header h3 {
  margin: 0;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Barre d'outils améliorée */
.excel-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background-color: var(--card-background);
  border-bottom: 1px solid var(--excel-border);
  flex-wrap: wrap;
  gap: 10px;
}

.excel-toolbar-group {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 10px;
  border-right: 1px solid var(--border-color);
}

.excel-toolbar-group:last-child {
  border-right: none;
}

.btn-tool {
  background-color: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 0.9rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all var(--transition-speed) ease;
}

.btn-tool:hover {
  background-color: rgba(52, 152, 219, 0.1);
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

.btn-tool i {
  font-size: 1rem;
  color: var(--primary-color);
}

.btn-tool.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.btn-tool.active i {
  color: white;
}

/* Styles de légende supprimés */

/* Conteneur Excel amélioré */
.excel-container {
  flex: 1;
  overflow: auto;
  position: relative;
  scrollbar-width: thin;
  scrollbar-color: var(--primary-color) var(--excel-bg);
  background-color: var(--excel-bg);
  /* Amélioration de la performance */
  will-change: transform;
  /* Amélioration de l'accessibilité - défilement fluide */
  -webkit-overflow-scrolling: touch;
  /* Amélioration visuelle */
  border-radius: var(--border-radius-sm);
  /* Amélioration de l'expérience utilisateur */
  padding: var(--spacing-sm);
  /* Amélioration de l'accessibilité - focus visible */
  outline: none;
}

/* Tableau Excel amélioré */
#excel-table {
  border-collapse: collapse;
  width: 100%;
  font-size: var(--font-size-base);
  table-layout: fixed;
  transition: all var(--transition-speed) ease;
  margin: 0 auto;
  /* Amélioration de la performance */
  contain: content;
  /* Amélioration visuelle */
  border-radius: var(--border-radius-sm);
  overflow: hidden;
}

#excel-table th {
  background-color: var(--excel-header-bg);
  color: var(--text-color);
  font-weight: var(--font-weight-semibold);
  text-align: center;
  padding: var(--spacing-md) var(--spacing-sm);
  position: sticky;
  top: 0;
  z-index: 10;
  border: none; /* Pas de bordure par défaut */
  border-bottom: 2px solid var(--excel-border); /* Garder uniquement la bordure du bas */
  transition: all var(--transition-speed) ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  /* Amélioration de l'accessibilité - lisibilité */
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.1);
  /* Amélioration de la taille minimale pour les cibles tactiles */
  min-height: 44px;
}

#excel-table td {
  border: none; /* Pas de bordure par défaut */
  padding: 0;
  text-align: center;
  height: 44px; /* Taille minimale pour les cibles tactiles */
  width: 44px;
  background: var(--excel-cell-bg);
  transition: all var(--transition-speed) ease;
  position: relative;
  font-family: var(--font-family);
  font-size: var(--font-size-lg);
  overflow: visible;
  /* Amélioration de l'accessibilité - contraste */
  color: var(--text-color);
  /* Amélioration visuelle */
  vertical-align: middle;
}

#excel-table td:hover {
  background-color: rgba(52, 152, 219, 0.15);
  z-index: 5;
  /* Amélioration visuelle */
  box-shadow: inset 0 0 0 1px rgba(52, 152, 219, 0.3);
}

#excel-table td:focus-within {
  background-color: rgba(52, 152, 219, 0.2);
  box-shadow: inset 0 0 0 2px var(--primary-color);
  outline: none;
  z-index: 5;
  /* Amélioration de l'accessibilité - indication visuelle */
  position: relative;
}

/* Amélioration de l'accessibilité - indication de focus */
#excel-table td:focus-within::after {
  content: "";
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border: 1px solid var(--primary-color);
  border-radius: 2px;
  pointer-events: none;
  animation: pulseFocus 1.5s infinite;
}

@keyframes pulseFocus {
  0% {
    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.4);
  }
  70% {
    box-shadow: 0 0 0 4px rgba(52, 152, 219, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0);
  }
}

/* Désactiver l'animation pour les utilisateurs qui préfèrent réduire les animations */
@media (prefers-reduced-motion: reduce) {
  #excel-table td:focus-within::after {
    animation: none;
    box-shadow: 0 0 0 2px var(--primary-color);
  }
}

/* Styles améliorés pour les cellules */
#excel-table td span {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  transition: all var(--transition-speed) ease;
  position: relative;
  box-sizing: border-box;
}

/* Styles communs pour tous les types de cellules */
#excel-table td span.quotient-cell,
#excel-table td span.multiplication-cell,
#excel-table td span.subtraction-cell,
#excel-table td span.lowering-cell,
#excel-table td span.result-cell,
#excel-table td span.divisor-value,
#excel-table td span.dividend-value {
  border-radius: 4px;
  padding: 0;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  width: 36px;
  height: 36px;
  margin: auto;
  position: relative;
  overflow: visible;
}

/* Styles spécifiques pour chaque type de cellule */
#excel-table td span.quotient-cell {
  background-color: rgba(46, 204, 113, 0.15);
  color: var(--success-color);
  border: 2px solid var(--success-color);
}

#excel-table td span.multiplication-cell {
  background-color: rgba(231, 76, 60, 0.15);
  color: var(--danger-color);
  border: 2px solid var(--danger-color);
}

#excel-table td span.subtraction-cell {
  background-color: rgba(52, 152, 219, 0.15);
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

#excel-table td span.lowering-cell {
  background-color: rgba(243, 156, 18, 0.15);
  color: var(--warning-color);
  border: 2px solid var(--warning-color);
}

#excel-table td span.result-cell {
  background-color: rgba(52, 152, 219, 0.2);
  color: var(--info-color);
  border: 2px solid var(--info-color);
  font-weight: bold;
}

#excel-table td span.divisor-value {
  background-color: rgba(155, 89, 182, 0.15);
  color: #8e44ad;
  border: 2px solid #8e44ad;
}

#excel-table td span.dividend-value {
  background-color: rgba(52, 73, 94, 0.15);
  color: #2c3e50;
  border: 2px solid #2c3e50;
}

/* Effet 3D subtil pour les cellules */
#excel-table td span.quotient-cell,
#excel-table td span.multiplication-cell,
#excel-table td span.subtraction-cell,
#excel-table td span.lowering-cell,
#excel-table td span.result-cell,
#excel-table td span.divisor-value,
#excel-table td span.dividend-value {
  transform: translateY(0);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

#excel-table td:hover span.quotient-cell,
#excel-table td:hover span.multiplication-cell,
#excel-table td:hover span.subtraction-cell,
#excel-table td:hover span.lowering-cell,
#excel-table td:hover span.result-cell,
#excel-table td:hover span.divisor-value,
#excel-table td:hover span.dividend-value {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Indicateurs de type d'opération */
#excel-table td span.quotient-cell::before,
#excel-table td span.multiplication-cell::before,
#excel-table td span.subtraction-cell::before,
#excel-table td span.lowering-cell::before,
#excel-table td span.result-cell::before {
  content: "";
  position: absolute;
  top: -8px;
  right: -8px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  opacity: 0;
  transition: opacity 0.3s ease;
}

#excel-table td:hover span.quotient-cell::before {
  content: "Q";
  background-color: var(--success-color);
  opacity: 1;
}

#excel-table td:hover span.multiplication-cell::before {
  content: "×";
  background-color: var(--danger-color);
  opacity: 1;
}

#excel-table td:hover span.subtraction-cell::before {
  content: "−";
  background-color: var(--primary-color);
  opacity: 1;
}

#excel-table td:hover span.lowering-cell::before {
  content: "↓";
  background-color: var(--warning-color);
  opacity: 1;
}

#excel-table td:hover span.result-cell::before {
  content: "=";
  background-color: var(--info-color);
  opacity: 1;
}

/* Animation améliorée pour les nouvelles opérations */
@keyframes highlightOperation {
  0% {
    transform: scale(1.08);
    box-shadow: 0 0 15px rgba(52, 152, 219, 0.5);
    background-color: rgba(52, 152, 219, 0.25);
  }
  50% {
    transform: scale(1.04);
    box-shadow: 0 0 8px rgba(52, 152, 219, 0.3);
    background-color: rgba(52, 152, 219, 0.15);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }
}

#excel-table td span.new-operation {
  animation: highlightOperation 1.8s ease-out;
}

/* Pied de page Excel amélioré */
.excel-footer {
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--excel-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--card-background);
  /* Amélioration visuelle */
  border-radius: 0 0 var(--border-radius) var(--border-radius);
  /* Amélioration de l'accessibilité - contraste */
  color: var(--text-color);
  /* Amélioration de la performance */
  will-change: contents;
  /* Amélioration de l'expérience utilisateur */
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.excel-status {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  color: var(--text-muted);
  gap: 10px;
}

.excel-status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 10px;
  border-radius: 20px;
  background-color: var(--excel-cell-bg);
  border: 1px solid var(--border-color);
}

.excel-status-item i {
  color: var(--primary-color);
}

/* Contrôles de zoom améliorés */
.zoom-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background-color: var(--excel-cell-bg);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: 20px;
  border: 1px solid var(--border-color);
  /* Amélioration visuelle */
  box-shadow: var(--soft-shadow);
  /* Amélioration de l'accessibilité */
  position: relative;
}

.zoom-btn {
  background: transparent;
  border: none;
  color: var(--text-color);
  font-size: 1.1rem;
  cursor: pointer;
  padding: var(--spacing-xs);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-speed) ease;
  /* Amélioration de l'accessibilité - taille des cibles */
  min-width: 36px;
  min-height: 36px;
  border-radius: 50%;
}

.zoom-btn:hover {
  color: var(--primary-color);
  transform: scale(1.1);
  background-color: rgba(52, 152, 219, 0.1);
}

.zoom-btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--primary-color);
}

.zoom-btn:active {
  transform: scale(0.95);
}

#zoom-level {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  min-width: 50px;
  text-align: center;
  /* Amélioration de l'accessibilité - contraste */
  color: var(--text-color);
  /* Amélioration visuelle */
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  background-color: rgba(255, 255, 255, 0.5);
}

/* Tooltip pour les cellules - Amélioré pour l'accessibilité et l'expérience utilisateur */
.excel-tooltip {
  position: absolute;
  background-color: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
  color: var(--text-color);
  box-shadow: var(--medium-shadow);
  z-index: 100;
  pointer-events: none;
  opacity: 0;
  transition: opacity var(--transition-speed) ease,
    transform var(--transition-speed) ease;
  max-width: 250px;
  /* Amélioration visuelle */
  transform: translateY(5px);
  /* Amélioration de l'accessibilité - lisibilité */
  line-height: var(--line-height-base);
  /* Amélioration de la performance */
  will-change: opacity, transform;
  /* Amélioration de l'expérience utilisateur */
  text-align: left;
  /* Amélioration visuelle - éviter les débordements de texte */
  overflow-wrap: break-word;
  word-wrap: break-word;
  hyphens: auto;
}

.excel-tooltip.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Flèche du tooltip */
.excel-tooltip::before,
.excel-tooltip::after {
  content: "";
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
}

.excel-tooltip::before {
  border-width: 8px;
  border-style: solid;
  border-color: transparent transparent var(--border-color) transparent;
}

.excel-tooltip::after {
  border-width: 7px;
  border-style: solid;
  border-color: transparent transparent var(--card-background) transparent;
  margin-bottom: -1px;
}

/* Désactiver l'animation pour les utilisateurs qui préfèrent réduire les animations */
@media (prefers-reduced-motion: reduce) {
  .excel-tooltip {
    transition: opacity 0.01s linear;
    transform: none !important;
  }
}

/* Styles pour les modes d'affichage */
#excel-table.compact-view td {
  padding: 5px 6px;
  height: 28px;
  font-size: 0.85rem;
}

#excel-table.detailed-view td {
  padding: 10px 12px;
  height: 42px;
  font-size: 1rem;
}

/* Styles pour la flèche d'abaissement */
#excel-table td span.arrow-lowering {
  font-size: 1.5rem;
  color: var(--warning-color);
  display: inline-block;
  animation: arrowPulse 1.5s infinite;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: absolute;
  z-index: 10;
}

@keyframes arrowPulse {
  0% {
    transform: translateY(0) scale(1);
    opacity: 0.7;
  }
  50% {
    transform: translateY(4px) scale(1.1);
    opacity: 1;
  }
  100% {
    transform: translateY(0) scale(1);
    opacity: 0.7;
  }
}

/* Styles pour les opérateurs mathématiques */
#excel-table td span.operator {
  font-size: 1.3rem;
  font-weight: bold;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: var(--excel-cell-bg);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: absolute;
  z-index: 5;
}

#excel-table td span.operator.plus {
  color: var(--success-color);
  border: 1px solid var(--success-color);
}

#excel-table td span.operator.minus {
  color: var(--danger-color);
  border: 1px solid var(--danger-color);
}

#excel-table td span.operator.multiply {
  color: var(--warning-color);
  border: 1px solid var(--warning-color);
}

#excel-table td span.operator.divide {
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

#excel-table td span.operator.equals {
  color: var(--info-color);
  border: 1px solid var(--info-color);
}

/* Styles pour les lignes de séparation */
.excel-horizontal-line {
  position: absolute;
  height: 2px;
  background-color: var(--primary-color);
  z-index: 4;
  left: 0;
  right: 0;
}

.excel-vertical-line {
  position: absolute;
  width: 2px;
  background-color: var(--primary-color);
  z-index: 4;
  top: 0;
  bottom: 0;
}

/* Styles pour les lignes de division */
.excel-division-line {
  position: relative;
}

.excel-division-line-horizontal {
  height: 2px;
  background-color: var(--primary-color);
  position: absolute;
  left: 0;
  right: 0;
}

.excel-division-line-vertical {
  width: 2px;
  background-color: var(--primary-color);
  position: absolute;
  top: 0;
  bottom: 0;
}

/* Styles pour les étiquettes d'opération */
#excel-table td span.operation-label,
#excel-table td span.result-label {
  font-size: 0.85rem;
  color: var(--text-muted);
  white-space: nowrap;
  font-weight: 500;
  position: absolute;
  left: -80px;
  width: 75px;
  text-align: right;
  background-color: var(--excel-cell-bg);
  padding: 2px 5px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  opacity: 0;
  transition: opacity 0.3s ease;
}

#excel-table td:hover span.operation-label,
#excel-table td:hover span.result-label {
  opacity: 1;
}

/* Styles pour la cellule de vérification */
#excel-table td span.verification-cell {
  display: inline-block;
  padding: 8px 12px;
  border-radius: 6px;
  background-color: rgba(46, 204, 113, 0.15);
  color: var(--text-color);
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--success-color);
}

/* Styles pour les groupes de cellules */
.excel-cell-group {
  position: relative;
}

.excel-cell-group::after {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px dashed var(--primary-color);
  border-radius: 6px;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.excel-cell-group:hover::after {
  opacity: 1;
}

/* Styles pour les indices et exposants */
#excel-table td span sup,
#excel-table td span sub {
  font-size: 0.7em;
  position: relative;
  line-height: 0;
}

#excel-table td span sup {
  top: -0.5em;
}

#excel-table td span sub {
  bottom: -0.25em;
}

/* Styles pour les cellules avec plusieurs chiffres */
#excel-table td.multi-digit {
  position: relative;
}

#excel-table td.multi-digit::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(52, 152, 219, 0.05);
  border: 1px dashed var(--primary-color);
  z-index: 1;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

#excel-table td.multi-digit:hover::before {
  opacity: 1;
}
