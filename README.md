# smartDiv - Division Euclidienne Interactive

![smartDiv Logo](icons/icon-192x192.svg)

## 📖 Description

**smartDiv** est une application web éducative interactive conçue pour enseigner et apprendre la division euclidienne étape par étape. Cette application offre une expérience d'apprentissage immersive avec une interface moderne et accessible.

## ✨ Fonctionnalités

### 🎯 Simulation Interactive
- **Apprentissage étape par étape** : Visualisez chaque étape de la division euclidienne
- **Types de division multiples** : Division avec reste, exacte, décimale, fractionnaire, par groupement
- **Niveaux de difficulté** : De très facile à expert
- **Génération automatique** : Création automatique de nombres selon le niveau choisi

### 📊 Tableau Excel Intégré
- **Visualisation en temps réel** : Toutes les opérations sont affichées dans un tableau similaire à Excel
- **Fonctionnalités avancées** : Zoom, impression, export, plein écran
- **Interface intuitive** : Navigation facile avec support tactile

### 👥 Système d'Authentification
- **Comptes élèves et enseignants** : Interfaces adaptées selon le rôle
- **Tableaux de bord personnalisés** : Suivi de progression et statistiques
- **Connexion sociale** : Support Google, Facebook, Apple, Microsoft

### 🎨 Interface Moderne
- **Design responsive** : Adapté à tous les appareils (mobile, tablette, desktop)
- **Mode sombre/clair** : Basculement automatique selon les préférences système
- **Accessibilité WCAG 2.1** : Support complet des technologies d'assistance
- **Animations optimisées** : Respect des préférences de mouvement réduit

### 🌐 Progressive Web App (PWA)
- **Installation possible** : Fonctionne comme une application native
- **Mode hors ligne** : Utilisation sans connexion internet
- **Notifications** : Alertes et rappels personnalisés

## 🚀 Installation et Utilisation

### Prérequis
- Navigateur web moderne (Chrome, Firefox, Safari, Edge)
- Connexion internet (optionnelle après installation PWA)

### Installation Locale
1. Clonez le repository :
   ```bash
   git clone https://github.com/votre-username/smartdiv.git
   cd smartdiv
   ```

2. Ouvrez `index.html` dans votre navigateur ou utilisez un serveur local :
   ```bash
   # Avec Python
   python -m http.server 8000
   
   # Avec Node.js
   npx serve .
   
   # Avec PHP
   php -S localhost:8000
   ```

3. Accédez à `http://localhost:8000`

### Installation PWA
1. Visitez l'application dans votre navigateur
2. Cliquez sur l'icône d'installation dans la barre d'adresse
3. Suivez les instructions pour installer l'application

## 📱 Utilisation

### Pour les Élèves
1. **Connexion** : Créez un compte élève ou connectez-vous
2. **Simulation** : Choisissez le type et niveau de division
3. **Apprentissage** : Suivez les étapes guidées
4. **Progression** : Consultez vos statistiques dans le tableau de bord

### Pour les Enseignants
1. **Connexion** : Créez un compte enseignant
2. **Gestion** : Ajoutez et suivez vos élèves
3. **Exercices** : Créez des exercices personnalisés
4. **Analyse** : Consultez les statistiques de progression

## 🛠️ Technologies Utilisées

- **Frontend** : HTML5, CSS3, JavaScript (Vanilla)
- **PWA** : Service Worker, Web App Manifest
- **Accessibilité** : ARIA, WCAG 2.1 AAA
- **Design** : CSS Grid, Flexbox, Variables CSS
- **Icônes** : Font Awesome, SVG personnalisés

## 📁 Structure du Projet

```
smartdiv/
├── index.html              # Page principale
├── auth.html               # Page d'authentification
├── student-dashboard.html  # Tableau de bord élève
├── teacher-dashboard.html  # Tableau de bord enseignant
├── offline.html           # Page hors ligne
├── manifest.json          # Manifest PWA
├── service-worker.js      # Service Worker
├── css/                   # Feuilles de style
│   ├── styles.css
│   ├── modern-*.css
│   └── ...
├── js/                    # Scripts JavaScript
│   ├── script.js
│   ├── auth-*.js
│   └── ...
├── icons/                 # Icônes PWA
├── images/               # Images et logos
├── screenshots/          # Captures d'écran
├── locales/             # Fichiers de traduction
└── fonts/               # Polices personnalisées
```

## 🌍 Internationalisation

L'application supporte plusieurs langues :
- 🇫🇷 Français (par défaut)
- 🇬🇧 Anglais
- 🇪🇸 Espagnol
- 🇩🇪 Allemand

## ♿ Accessibilité

smartDiv respecte les standards d'accessibilité :
- **WCAG 2.1 AAA** : Contraste élevé, navigation clavier
- **Technologies d'assistance** : Support complet des lecteurs d'écran
- **Préférences utilisateur** : Respect des préférences système
- **Police dyslexique** : Option pour les utilisateurs dyslexiques

## 📄 Licence

Copyright (c) 2023 Ali MESSAOUDI. Tous droits réservés.

Ce code est protégé par les lois sur le droit d'auteur et ne peut être reproduit, distribué ou utilisé sans autorisation explicite de l'auteur.

## 👨‍💻 Auteur

**Ali MESSAOUDI**
- Email : <EMAIL>
- Version : 1.3.6

## 🤝 Contribution

Ce projet est actuellement en développement privé. Pour toute suggestion ou rapport de bug, veuillez contacter l'auteur.

## 📈 Roadmap

### Version 1.4.0 (À venir)
- [ ] Mode multijoueur en temps réel
- [ ] Exercices collaboratifs
- [ ] Intégration avec les LMS (Moodle, Canvas)
- [ ] API REST pour développeurs

### Version 1.5.0 (Futur)
- [ ] Intelligence artificielle pour recommandations personnalisées
- [ ] Réalité augmentée pour visualisation 3D
- [ ] Support vocal avec reconnaissance de parole
- [ ] Gamification avancée avec système de récompenses

## 🆘 Support

Pour obtenir de l'aide :
1. Consultez la section "Aide" dans l'application
2. Contactez le support : <EMAIL>
3. Consultez la documentation en ligne

---

*smartDiv - Apprendre la division euclidienne n'a jamais été aussi simple !* 🎓✨
