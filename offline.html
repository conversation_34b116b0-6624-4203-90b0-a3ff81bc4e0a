<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Hors ligne - smartDiv</title>
  <style>
    :root {
      --color-primary: #4361EE;
      --color-primary-light: #6C8AFF;
      --color-primary-dark: #2D41B3;
      --color-text-primary: #333333;
      --color-text-secondary: #757575;
      --color-background: #FFFFFF;
      --font-primary: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
      --space-md: 1rem;
      --space-lg: 1.5rem;
      --space-xl: 2rem;
      --radius-md: 0.5rem;
      --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
    }
    
    @media (prefers-color-scheme: dark) {
      :root {
        --color-text-primary: #F0F0F0;
        --color-text-secondary: #AAAAAA;
        --color-background: #121212;
      }
    }
    
    body {
      font-family: var(--font-primary);
      background-color: var(--color-background);
      color: var(--color-text-primary);
      margin: 0;
      padding: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      text-align: center;
    }
    
    .offline-container {
      max-width: 600px;
      padding: var(--space-xl);
      background-color: var(--color-background);
      border-radius: var(--radius-md);
      box-shadow: var(--shadow-md);
      margin: var(--space-md);
    }
    
    h1 {
      color: var(--color-primary);
      margin-bottom: var(--space-md);
    }
    
    p {
      color: var(--color-text-secondary);
      line-height: 1.6;
      margin-bottom: var(--space-lg);
    }
    
    .icon {
      font-size: 4rem;
      margin-bottom: var(--space-lg);
      color: var(--color-primary);
    }
    
    .btn {
      display: inline-block;
      background-color: var(--color-primary);
      color: white;
      padding: 0.75rem 1.5rem;
      border-radius: var(--radius-md);
      text-decoration: none;
      font-weight: 500;
      transition: background-color 0.3s ease;
    }
    
    .btn:hover {
      background-color: var(--color-primary-dark);
    }
    
    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.05); }
      100% { transform: scale(1); }
    }
    
    .pulse {
      animation: pulse 2s infinite;
    }
  </style>
</head>
<body>
  <div class="offline-container">
    <div class="icon pulse">📶</div>
    <h1>Vous êtes hors ligne</h1>
    <p>Il semble que vous n'ayez pas de connexion Internet. Certaines fonctionnalités de smartDiv peuvent ne pas être disponibles.</p>
    <p>Vérifiez votre connexion et réessayez.</p>
    <a href="/" class="btn">Réessayer</a>
  </div>
  
  <script>
    // Vérifier périodiquement si la connexion est rétablie
    window.addEventListener('online', () => {
      window.location.href = '/';
    });
    
    // Ajouter un gestionnaire pour le bouton de réessai
    document.querySelector('.btn').addEventListener('click', (e) => {
      e.preventDefault();
      window.location.reload();
    });
  </script>
</body>
</html>
