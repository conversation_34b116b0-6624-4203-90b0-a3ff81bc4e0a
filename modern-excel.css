/*
 * smartDiv - Styles du tableau Excel moderne
 * Ce fichier contient les styles pour l'interface du tableau Excel modernisée
 */

/* ===== WRAPPER DU TABLEAU ===== */
.excel-sheet-wrapper {
  background-color: var(--color-background);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 500px;
}

/* ===== EN-TÊTE DU TABLEAU ===== */
.excel-sheet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-md) var(--space-lg);
  background-color: var(--color-primary);
  color: white;
  width: 100%;
  margin-left: 0;
  margin-right: auto;
}

.excel-sheet-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  color: white;
}

.excel-sheet-header h3 i {
  font-size: var(--font-size-xl);
}

.excel-actions {
  display: flex;
  gap: var(--space-xs);
}

.excel-actions .btn-icon {
  width: 36px;
  height: 36px;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.excel-actions .btn-icon:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.excel-actions .btn-icon:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

/* ===== BARRE D'OUTILS EXCEL ===== */
.excel-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-sm) var(--space-lg);
  background-color: var(--color-background-light);
  border-bottom: 1px solid var(--color-border);
}

.excel-tools {
  display: flex;
  gap: var(--space-sm);
}

.btn-tool {
  background-color: var(--color-background);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.btn-tool:hover {
  background-color: var(--color-primary-light);
  color: white;
  border-color: var(--color-primary-light);
}

.btn-tool i {
  font-size: var(--font-size-base);
}

.excel-view-options {
  display: flex;
  align-items: center;
}

.excel-select {
  padding: var(--space-sm) var(--space-md);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  background-color: var(--color-background);
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.excel-select:focus {
  border-color: var(--color-primary);
  outline: none;
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
}

/* ===== CONTENEUR DU TABLEAU ===== */
.excel-container {
  flex: 1;
  overflow: auto;
  padding: var(--space-md);
  background-color: var(--color-background-light);
}

/* ===== TABLEAU EXCEL ===== */
#excel-table {
  width: 100%;
  border-collapse: collapse;
  font-family: var(--font-mono);
  background-color: white;
  border: 1px solid var(--color-border);
}

#excel-table th,
#excel-table td {
  border: 1px solid var(--color-border);
  padding: var(--space-sm);
  text-align: center;
  min-width: 40px;
  height: 40px;
  transition: all var(--transition-fast);
}

#excel-table th {
  background-color: var(--color-background-light);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-secondary);
  position: sticky;
  top: 0;
  z-index: 10;
}

#excel-table td {
  font-size: var(--font-size-base);
}

#excel-table td.highlight {
  background-color: rgba(67, 97, 238, 0.1);
}

#excel-table td.operation-multiply {
  background-color: rgba(60, 207, 207, 0.1);
  color: var(--color-secondary-dark);
}

#excel-table td.operation-subtract {
  background-color: rgba(255, 122, 0, 0.1);
  color: var(--color-accent-dark);
}

#excel-table td.operation-result {
  background-color: rgba(40, 167, 69, 0.1);
  color: var(--color-success);
  font-weight: var(--font-weight-semibold);
}

/* ===== PIED DE PAGE EXCEL ===== */
.excel-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-sm) var(--space-lg);
  background-color: var(--color-background-light);
  border-top: 1px solid var(--color-border);
}

.excel-status {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.excel-status-separator {
  margin: 0 var(--space-xs);
  color: var(--color-border);
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.zoom-controls .btn-icon {
  width: 32px;
  height: 32px;
  background-color: var(--color-background);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.zoom-controls .btn-icon:hover {
  background-color: var(--color-primary-light);
  color: white;
  border-color: var(--color-primary-light);
}

#zoom-level {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  width: 50px;
  text-align: center;
}

/* ===== MODAL D'AIDE EXCEL ===== */
.excel-help-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.excel-help-modal.active {
  opacity: 1;
  visibility: visible;
}

.excel-help-content {
  background-color: var(--color-background);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  padding: var(--space-xl);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  transform: translateY(20px);
  transition: transform var(--transition-normal);
}

.excel-help-modal.active .excel-help-content {
  transform: translateY(0);
}

.excel-help-content h3 {
  color: var(--color-primary);
  margin-top: 0;
  margin-bottom: var(--space-lg);
  position: relative;
  padding-bottom: var(--space-sm);
}

.excel-help-content h3::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 4px;
  background-color: var(--color-primary);
  border-radius: 2px;
}

.excel-help-content ul {
  margin-bottom: var(--space-lg);
  padding-left: var(--space-lg);
}

.excel-help-content li {
  margin-bottom: var(--space-sm);
  position: relative;
}

.excel-help-content li::before {
  content: "•";
  color: var(--color-primary);
  font-weight: bold;
  display: inline-block;
  width: 1em;
  margin-left: -1em;
}

/* ===== MODES D'AFFICHAGE ===== */
/* Vue standard (par défaut) */
#excel-table.view-standard td {
  font-size: var(--font-size-base);
}

/* Vue compacte */
#excel-table.view-compact th,
#excel-table.view-compact td {
  padding: var(--space-xs);
  font-size: var(--font-size-sm);
  min-width: 30px;
  height: 30px;
}

/* Vue détaillée */
#excel-table.view-detailed th,
#excel-table.view-detailed td {
  padding: var(--space-md);
  font-size: var(--font-size-lg);
  min-width: 50px;
  height: 50px;
}

/* ===== PLEIN ÉCRAN ===== */
.excel-sheet-wrapper.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1500;
  border-radius: 0;
}

/* ===== ANIMATIONS ===== */
@keyframes cellHighlight {
  0% {
    background-color: rgba(67, 97, 238, 0.3);
  }
  100% {
    background-color: rgba(67, 97, 238, 0.1);
  }
}

.cell-highlight-animation {
  animation: cellHighlight 1s ease;
}
