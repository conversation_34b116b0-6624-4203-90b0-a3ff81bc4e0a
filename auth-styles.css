/**
 * auth-styles.css
 *
 * Copyright (c) 2023 Ali MESSAOUDI
 * Tous droits réservés.
 * Ce code est protégé par les lois sur le droit d'auteur et ne peut être reproduit,
 * distribué ou utilisé sans autorisation explicite de l'auteur.
 */

/* Variables */
:root {
  --primary-color: #3498db;
  --primary-color-dark: #2980b9;
  --primary-color-light: #e1f0fa;
  --secondary-color: #2ecc71;
  --secondary-color-dark: #27ae60;
  --danger-color: #e74c3c;
  --warning-color: #f39c12;
  --success-color: #2ecc71;
  --text-color: #333;
  --text-color-light: #777;
  --border-color: #ddd;
  --card-background: #fff;
  --background-color: #f5f7fa;
  --shadow-color: rgba(0, 0, 0, 0.1);
}

/* Bouton de connexion dans l'en-tête */
.btn-auth {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-auth:hover {
  background-color: var(--primary-color-dark);
  transform: translateY(-1px);
}

.btn-auth i {
  font-size: 1rem;
}

/* Page d'authentification */
.auth-main {
  min-height: calc(100vh - 200px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background-color: var(--background-color);
  background-image: linear-gradient(
    135deg,
    rgba(52, 152, 219, 0.05) 0%,
    rgba(46, 204, 113, 0.05) 100%
  );
}

.auth-container {
  display: flex;
  width: 100%;
  max-width: 1200px;
  min-height: 600px;
  background-color: var(--card-background);
  border-radius: 16px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1), 0 5px 15px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Panneau d'informations */
.auth-info-panel {
  flex: 1;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-color-dark)
  );
  color: white;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.auth-info-content {
  max-width: 500px;
  margin: 0 auto;
}

.auth-info-panel h2 {
  font-size: 2.2rem;
  margin-bottom: 16px;
}

.auth-tagline {
  font-size: 1.2rem;
  margin-bottom: 40px;
  opacity: 0.9;
}

/* Fonctionnalités */
.auth-features {
  margin-bottom: 40px;
}

.auth-feature {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.auth-feature-icon {
  width: 50px;
  height: 50px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 1.5rem;
}

.auth-feature-text h3 {
  font-size: 1.1rem;
  margin-bottom: 4px;
}

.auth-feature-text p {
  font-size: 0.9rem;
  opacity: 0.8;
  margin: 0;
}

/* Témoignages */
.auth-testimonials h3 {
  font-size: 1.2rem;
  margin-bottom: 16px;
}

.auth-testimonial {
  background-color: rgba(255, 255, 255, 0.1);
  padding: 16px;
  border-radius: 8px;
  font-style: italic;
}

.auth-testimonial p {
  margin-bottom: 12px;
}

.auth-testimonial-author {
  display: flex;
  flex-direction: column;
  font-style: normal;
  font-size: 0.9rem;
}

.auth-testimonial-role {
  opacity: 0.7;
  font-size: 0.8rem;
}

/* Panneau de formulaire */
.auth-form-panel {
  flex: 1;
  padding: 0;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

/* Onglets */
.auth-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
}

.auth-tab {
  flex: 1;
  padding: 16px;
  text-align: center;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  font-weight: 600;
  color: var(--text-color-light);
  cursor: pointer;
  transition: all 0.2s ease;
}

.auth-tab.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.auth-tab:hover:not(.active) {
  background-color: rgba(0, 0, 0, 0.02);
  color: var(--text-color);
}

.auth-tab i {
  margin-right: 8px;
}

/* Contenu des onglets */
.auth-tab-content {
  padding: 32px;
  display: none;
  overflow-y: auto;
}

.auth-tab-content.active {
  display: block;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.auth-tab-content h2 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1.8rem;
  color: var(--text-color);
}

.auth-intro {
  margin-bottom: 24px;
  color: var(--text-color-light);
  font-size: 0.95rem;
  line-height: 1.5;
}

/* Formulaire */
.auth-form {
  margin-bottom: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: var(--text-color);
}

.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.input-with-icon i {
  position: absolute;
  left: 14px;
  color: var(--text-color-light);
  transition: all 0.3s ease;
  font-size: 1rem;
}

.input-with-icon input,
.input-with-icon select {
  width: 100%;
  padding: 14px 14px 14px 42px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.input-with-icon input:hover,
.input-with-icon select:hover {
  border-color: rgba(52, 152, 219, 0.3);
  background-color: rgba(255, 255, 255, 0.95);
}

.input-with-icon input:focus,
.input-with-icon select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15);
  outline: none;
  background-color: white;
}

.input-with-icon input:focus + i,
.input-with-icon select:focus + i {
  color: var(--primary-color);
}

.toggle-password {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  color: var(--text-color-light);
  cursor: pointer;
  transition: color 0.2s ease;
}

.toggle-password:hover {
  color: var(--primary-color);
}

.remember-me {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.85rem;
  color: var(--text-color-light);
}

.remember-me input[type="checkbox"] {
  margin: 0;
  width: 14px;
  height: 14px;
  accent-color: var(--primary-color);
  cursor: pointer;
}

.btn-primary {
  width: 100%;
  padding: 14px;
  font-size: 1rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-color-dark)
  );
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  box-shadow: 0 4px 6px rgba(52, 152, 219, 0.2);
  position: relative;
  overflow: hidden;
}

.btn-primary::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover::after {
  opacity: 1;
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(52, 152, 219, 0.2);
}

/* Liens */
.auth-links {
  margin-top: 16px;
  text-align: center;
  font-size: 0.9rem;
}

.auth-links a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.2s ease;
}

.auth-links a:hover {
  color: var(--primary-color-dark);
  text-decoration: underline;
}

.auth-links .separator {
  margin: 0 8px;
  color: var(--text-color-light);
}

/* Séparateur */
.auth-separator {
  display: flex;
  align-items: center;
  margin: 24px 0;
  color: var(--text-color-light);
  font-size: 0.9rem;
}

.auth-separator::before,
.auth-separator::after {
  content: "";
  flex: 1;
  height: 1px;
  background-color: var(--border-color);
}

.auth-separator span {
  padding: 0 16px;
}

/* Connexion sociale */
.social-login {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.social-login-row {
  display: flex;
  gap: 12px;
  width: 100%;
}

.social-login button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: white;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.social-login button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.social-login button:active {
  transform: translateY(0);
}

.social-login button i {
  font-size: 1.2rem;
}

/* Couleurs spécifiques pour chaque service */
.btn-google {
  color: #ea4335;
  border-color: rgba(234, 67, 53, 0.3);
}

.btn-google:hover {
  background-color: rgba(234, 67, 53, 0.05);
}

.btn-facebook {
  color: #1877f2;
  border-color: rgba(24, 119, 242, 0.3);
}

.btn-facebook:hover {
  background-color: rgba(24, 119, 242, 0.05);
}

.btn-apple {
  color: #000000;
  border-color: rgba(0, 0, 0, 0.3);
}

.btn-apple:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.btn-microsoft {
  color: #0078d4;
  border-color: rgba(0, 120, 212, 0.3);
}

.btn-microsoft:hover {
  background-color: rgba(0, 120, 212, 0.05);
}

/* Sélecteur de type d'utilisateur */
.user-type-selector {
  display: flex;
  gap: 12px;
  margin-top: 8px;
}

.user-type-option {
  flex: 1;
}

.user-type-option input[type="radio"] {
  display: none;
}

.user-type-option label {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.user-type-option input[type="radio"]:checked + label {
  border-color: var(--primary-color);
  background-color: var(--primary-color-light);
  box-shadow: 0 2px 6px rgba(52, 152, 219, 0.1);
}

.user-type-option label i {
  font-size: 1.5rem;
  color: var(--primary-color);
}

/* Disposition en ligne pour les champs prénom/nom */
.form-row {
  display: flex;
  gap: 12px;
}

.form-row .form-group {
  flex: 1;
}

/* Indicateur de force du mot de passe */
.password-strength {
  margin-top: 8px;
  font-size: 0.8rem;
}

.strength-meter {
  display: flex;
  gap: 4px;
  margin-bottom: 4px;
}

.strength-segment {
  height: 4px;
  flex: 1;
  background-color: var(--border-color);
  border-radius: 2px;
}

.strength-segment.weak {
  background-color: var(--danger-color);
}

.strength-segment.medium {
  background-color: var(--warning-color);
}

.strength-segment.strong {
  background-color: var(--success-color);
}

.strength-text {
  color: var(--text-color-light);
}

/* Conseils pour le mot de passe */
.password-tips {
  margin-top: 4px;
  color: var(--text-color-light);
}

/* Feedback et validation */
.form-feedback {
  font-size: 0.8rem;
  margin-top: 4px;
  min-height: 16px;
}

.form-feedback.error {
  color: var(--danger-color);
}

.form-feedback.success {
  color: var(--success-color);
}

.auth-feedback {
  margin-top: 16px;
  padding: 12px;
  border-radius: 4px;
  font-size: 0.9rem;
  text-align: center;
}

.auth-feedback.error {
  background-color: rgba(231, 76, 60, 0.1);
  color: var(--danger-color);
  border: 1px solid rgba(231, 76, 60, 0.2);
}

.auth-feedback.success {
  background-color: rgba(46, 204, 113, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(46, 204, 113, 0.2);
}

/* Spinner pour les boutons */
.btn-auth-submit {
  position: relative;
}

.spinner {
  margin-left: 8px;
  font-size: 0.9em;
}

/* Conditions d'utilisation */
.terms {
  font-size: 0.85rem;
  display: flex;
  align-items: flex-start;
  gap: 6px;
  color: var(--text-color);
}

.terms input[type="checkbox"] {
  margin-top: 3px;
  width: 14px;
  height: 14px;
  accent-color: var(--primary-color);
  cursor: pointer;
}

/* Toast notifications */
.toast-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9999;
}

.toast {
  padding: 12px 16px;
  margin-bottom: 10px;
  border-radius: 4px;
  color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  max-width: 300px;
  transform: translateX(120%);
  transition: transform 0.3s ease;
}

.toast.show {
  transform: translateX(0);
}

.toast-success {
  background-color: var(--success-color);
}

.toast-error {
  background-color: var(--danger-color);
}

.toast-info {
  background-color: var(--primary-color);
}

.toast-warning {
  background-color: var(--warning-color);
}

/* Logo link */
.logo-link {
  color: inherit;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Responsive */
@media (max-width: 992px) {
  .auth-container {
    flex-direction: column;
    max-width: 600px;
  }

  .auth-info-panel {
    padding: 30px;
  }
}

@media (max-width: 576px) {
  .auth-tab-content {
    padding: 20px;
  }

  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .auth-tab {
    padding: 12px 8px;
    font-size: 0.9rem;
  }

  .user-type-option label {
    padding: 12px 8px;
  }
}

/* Classes utilitaires */
.hidden {
  display: none !important;
}
