<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1280 720" width="1280" height="720">
  <defs>
    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f4f6f7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1280" height="720" fill="url(#bgGrad)"/>
  
  <!-- Header -->
  <rect x="0" y="0" width="1280" height="80" fill="#3498db"/>
  <text x="40" y="50" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="white">smartDiv - Division Euclidienne Interactive</text>
  
  <!-- Main content area -->
  <rect x="40" y="120" width="600" height="560" rx="12" fill="white" stroke="#ddd" stroke-width="1"/>
  
  <!-- Form title -->
  <text x="60" y="160" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#2c3e50">Simulation de Division</text>
  
  <!-- Input fields -->
  <rect x="60" y="200" width="200" height="40" rx="6" fill="#f8f9fa" stroke="#ddd" stroke-width="1"/>
  <text x="70" y="185" font-family="Arial, sans-serif" font-size="14" fill="#6c757d">Dividende</text>
  <text x="70" y="225" font-family="Arial, sans-serif" font-size="16" fill="#2c3e50">1234</text>
  
  <rect x="300" y="200" width="200" height="40" rx="6" fill="#f8f9fa" stroke="#ddd" stroke-width="1"/>
  <text x="310" y="185" font-family="Arial, sans-serif" font-size="14" fill="#6c757d">Diviseur</text>
  <text x="310" y="225" font-family="Arial, sans-serif" font-size="16" fill="#2c3e50">56</text>
  
  <!-- Button -->
  <rect x="60" y="280" width="150" height="45" rx="6" fill="#3498db"/>
  <text x="135" y="308" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="white">Commencer</text>
  
  <!-- Excel table area -->
  <rect x="680" y="120" width="560" height="560" rx="12" fill="white" stroke="#ddd" stroke-width="1"/>
  <text x="700" y="160" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#2c3e50">Tableau de calcul</text>
  
  <!-- Table grid -->
  <g stroke="#ddd" stroke-width="1" fill="none">
    <line x1="700" y1="180" x2="1220" y2="180"/>
    <line x1="700" y1="220" x2="1220" y2="220"/>
    <line x1="700" y1="260" x2="1220" y2="260"/>
    <line x1="700" y1="300" x2="1220" y2="300"/>
    
    <line x1="740" y1="180" x2="740" y2="300"/>
    <line x1="780" y1="180" x2="780" y2="300"/>
    <line x1="820" y1="180" x2="820" y2="300"/>
    <line x1="860" y1="180" x2="860" y2="300"/>
  </g>
  
  <!-- Table headers -->
  <text x="720" y="205" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#6c757d">A</text>
  <text x="760" y="205" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#6c757d">B</text>
  <text x="800" y="205" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#6c757d">C</text>
  <text x="840" y="205" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#6c757d">D</text>
  
  <!-- Sample data -->
  <text x="720" y="245" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#2c3e50">1234</text>
  <text x="760" y="245" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#2c3e50">÷</text>
  <text x="800" y="245" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#2c3e50">56</text>
  <text x="840" y="245" font-family="Arial, sans-serif" font-size="12" text-anchor="middle" fill="#2c3e50">=</text>
</svg>
