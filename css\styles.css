/**
 * styles.css - Feuille de style principale pour smartDiv
 * Version 2.0.0 - Optimisée pour la performance, l'accessibilité et les standards modernes
 *
 * Ce fichier contient tous les styles principaux de l'application smartDiv,
 * organisés selon une architecture modulaire et utilisant des variables CSS
 * pour faciliter la personnalisation et la maintenance.
 *
 * Structure du fichier :
 * 1. Variables CSS (thème clair, sombre, contraste élevé)
 * 2. Styles de base et réinitialisation
 * 3. Composants d'interface (boutons, formulaires, cartes)
 * 4. Mise en page et grille (utilisant CSS Grid et Flexbox)
 * 5. Utilitaires et classes d'aide
 * 6. Animations et transitions (optimisées pour la performance)
 * 7. Styles spécifiques à la simulation de division
 * 8. Styles responsifs (media queries et container queries)
 * 9. Optimisations de performance (content-visibility, will-change, etc.)
 * 10. Améliorations d'accessibilité (WCAG 2.1 AAA)
 *
 * Le système de thèmes utilise des variables CSS et des attributs data-*
 * pour permettre de basculer facilement entre différents modes :
 * - Mode clair (par défaut)
 * - Mode sombre (respecte prefers-color-scheme)
 * - Mode contraste élevé (accessibilité WCAG AAA)
 * - Mode pour dyslexiques (police et espacement optimisés)
 * - Mode avec animations réduites (respecte prefers-reduced-motion)
 * - Mode économie d'énergie (optimisé pour les appareils à batterie)
 *
 * Techniques modernes utilisées :
 * - CSS Custom Properties (variables)
 * - CSS Grid et Flexbox pour la mise en page
 * - Container Queries pour les composants adaptatifs
 * - Animations optimisées avec will-change et GPU acceleration
 * - Support des écrans haute résolution (Retina/HiDPI)
 * - Optimisations pour le chargement (content-visibility, font-display)
 * - Support complet des technologies d'assistance
 *
 * <AUTHOR> MESSAOUDI
 * @copyright 2023-2024 Ali MESSAOUDI
 */

/* Variables CSS - Mode clair (par défaut) */
:root {
  /**
   * Couleurs principales - Palette optimisée pour l'accessibilité
   *
   * Cette palette de couleurs a été soigneusement sélectionnée pour :
   * - Respecter les ratios de contraste WCAG 2.1 AA (minimum 4.5:1)
   * - Être distinguable par les personnes atteintes de daltonisme
   * - Offrir une hiérarchie visuelle claire
   * - Maintenir une cohérence esthétique
   */
  --primary-color: #3498db; /* Bleu principal - Utilisé pour les éléments principaux et les actions */
  --primary-dark: #2980b9; /* Bleu foncé - Utilisé pour les états hover/active des éléments primaires */
  --primary-light: #5dade2; /* Bleu clair - Utilisé pour les arrière-plans et les accents subtils */
  --secondary-color: #2ecc71; /* Vert - Utilisé pour les actions secondaires et les indicateurs de succès */
  --secondary-dark: #27ae60; /* Vert foncé - Utilisé pour les états hover/active des éléments secondaires */
  --secondary-light: #58d68d; /* Vert clair - Utilisé pour les arrière-plans et les accents subtils */
  --accent-color: #e74c3c; /* Rouge - Utilisé pour les accents et attirer l'attention */
  --accent-dark: #c0392b; /* Rouge foncé - Utilisé pour les états hover/active des accents */
  --accent-light: #ec7063; /* Rouge clair - Utilisé pour les arrière-plans et les accents subtils */
  --danger-color: #e74c3c; /* Rouge - Utilisé pour les erreurs et les actions destructives */
  --success-color: #2ecc71; /* Vert - Utilisé pour les confirmations et les actions réussies */
  --warning-color: #f39c12; /* Orange - Utilisé pour les avertissements */
  --warning-dark: #d35400; /* Orange foncé - Utilisé pour les états hover/active des avertissements */
  --info-color: #3498db; /* Bleu - Utilisé pour les messages d'information */

  /**
   * Couleurs de fond et de texte
   *
   * Ces variables définissent les couleurs de base pour les arrière-plans,
   * le texte et les bordures dans toute l'application. Elles sont conçues
   * pour offrir un contraste optimal et une lisibilité maximale.
   */
  --background-color: #f4f6f7; /* Couleur de fond principale - Gris très clair avec une légère teinte bleue */
  --card-background: #ffffff; /* Couleur de fond des cartes et panneaux - Blanc pur */
  --text-color: #2c3e50; /* Couleur de texte principale - Bleu très foncé presque noir */
  --text-muted: #6c757d; /* Couleur de texte secondaire - Gris moyen pour les informations moins importantes */
  --border-color: #ddd; /* Couleur des bordures - Gris clair */
  --link-color: #2980b9; /* Couleur des liens - Bleu foncé */
  --link-hover-color: #3498db; /* Couleur des liens au survol - Bleu plus vif */

  /**
   * Variables pour le tableau Excel
   *
   * Ces variables définissent l'apparence du tableau Excel utilisé pour
   * visualiser les étapes de la division. Elles sont conçues pour ressembler
   * à une feuille de calcul réelle tout en s'intégrant au design global.
   */
  --excel-border: #cfd8dc; /* Couleur des bordures du tableau - Gris bleuté clair */
  --excel-bg: #fafbfc; /* Couleur de fond du tableau - Blanc cassé très légèrement bleuté */
  --excel-cell-bg: #f7f9fa; /* Couleur de fond des cellules - Blanc légèrement grisé */
  --excel-header-bg: #e9ecef; /* Couleur de fond des en-têtes - Gris clair */

  /**
   * Effets visuels
   *
   * Ces variables définissent les ombres, dégradés et autres effets visuels
   * utilisés dans l'application pour créer une hiérarchie visuelle et un
   * sentiment de profondeur. Les ombres sont soigneusement calibrées pour
   * être subtiles mais efficaces.
   */
  /* Ombres portées avec différentes intensités */
  --soft-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Ombre légère pour les éléments subtils */
  --medium-shadow: 0 6px 12px rgba(0, 0, 0, 0.15); /* Ombre moyenne pour les cartes et panneaux */
  --large-shadow: 0 10px 20px rgba(0, 0, 0, 0.2); /* Ombre prononcée pour les éléments en avant-plan */

  /* Dégradés pour les boutons et éléments d'interface */
  --gradient-primary: linear-gradient(
    135deg,
    /* Direction du dégradé à 135 degrés */ var(--primary-color),
    /* Couleur de départ - Bleu principal */ var(--primary-dark)
      /* Couleur d'arrivée - Bleu foncé */
  );
  --gradient-success: linear-gradient(
    135deg,
    /* Direction du dégradé à 135 degrés */ var(--secondary-color),
    /* Couleur de départ - Vert */ var(--secondary-dark)
      /* Couleur d'arrivée - Vert foncé */
  );
  --gradient-danger: linear-gradient(
    135deg,
    /* Direction du dégradé à 135 degrés */ var(--danger-color),
    /* Couleur de départ - Rouge */ var(--accent-dark)
      /* Couleur d'arrivée - Rouge foncé */
  );
  --gradient-warning: linear-gradient(
    135deg,
    /* Direction du dégradé à 135 degrés */ var(--warning-color),
    /* Couleur de départ - Orange */ var(--warning-dark)
      /* Couleur d'arrivée - Orange foncé */
  );

  /**
   * Dimensions et transitions
   *
   * Ces variables définissent les rayons de bordure, les vitesses de transition
   * et autres propriétés dimensionnelles utilisées dans toute l'application.
   * Elles assurent une cohérence visuelle et un comportement prévisible des animations.
   */
  /* Rayons de bordure pour les coins arrondis */
  --border-radius-sm: 6px; /* Rayon de bordure petit - Pour les boutons et petits éléments */
  --border-radius: 12px; /* Rayon de bordure moyen - Pour les cartes et panneaux */
  --border-radius-lg: 18px; /* Rayon de bordure grand - Pour les modales et grands éléments */

  /* Durées de transition pour les animations */
  --transition-speed: 0.3s; /* Vitesse de transition standard - Équilibre entre réactivité et fluidité */
  --transition-speed-fast: 0.15s; /* Vitesse de transition rapide - Pour les interactions immédiates */
  --transition-speed-slow: 0.5s; /* Vitesse de transition lente - Pour les animations plus élaborées */

  /* Effet visuel pour les éléments ayant le focus */
  --focus-ring: 0 0 0 3px rgba(52, 152, 219, 0.4); /* Anneau bleu semi-transparent pour indiquer le focus */

  /**
   * Footer et modals
   *
   * Ces variables définissent l'apparence du pied de page et des boîtes de dialogue
   * modales. Le pied de page utilise un fond sombre pour créer un contraste avec
   * le contenu principal, tandis que les modales utilisent un overlay semi-transparent.
   */
  --footer-bg: #2c3e50; /* Couleur de fond du pied de page - Bleu très foncé */
  --footer-text: #ecf0f1; /* Couleur du texte du pied de page - Blanc cassé */
  --modal-overlay: rgba(
    0,
    0,
    0,
    0.7
  ); /* Couleur de l'overlay des modales - Noir semi-transparent */
  --tab-active-bg: rgba(
    52,
    152,
    219,
    0.1
  ); /* Couleur de fond des onglets actifs - Bleu très clair */
  --tab-hover-bg: rgba(
    52,
    152,
    219,
    0.05
  ); /* Couleur de fond des onglets au survol - Bleu très pâle */

  /**
   * Accessibilité
   *
   * Ces variables sont spécifiquement conçues pour améliorer l'accessibilité
   * de l'application. Elles respectent les recommandations WCAG 2.1 et
   * permettent aux utilisateurs ayant des besoins spécifiques de naviguer
   * plus facilement dans l'application.
   */
  --focus-visible-color: #3498db; /* Couleur pour indiquer le focus clavier - Bleu vif */
  --contrast-ratio: 4.5; /* Ratio de contraste minimum recommandé par WCAG AA */

  /**
   * Animations
   *
   * Ces variables définissent les durées et les courbes d'accélération
   * des animations dans l'application. Elles sont conçues pour offrir
   * un équilibre entre réactivité et fluidité, tout en respectant les
   * préférences des utilisateurs qui souhaitent réduire les animations.
   */
  --animation-duration: 0.3s; /* Durée standard des animations - Équilibre entre réactivité et fluidité */
  --animation-timing: ease; /* Courbe d'accélération standard - Accélération et décélération douces */
  --animation-timing-bounce: cubic-bezier(
    0.175,
    0.885,
    0.32,
    1.275
  ); /* Courbe d'accélération avec rebond - Pour les animations ludiques */

  /**
   * Typographie
   *
   * Ces variables définissent les polices, tailles, poids et hauteurs de ligne
   * utilisés dans l'application. La police principale est Inter, une police
   * moderne optimisée pour la lisibilité sur écran, avec des alternatives
   * pour assurer la compatibilité sur tous les systèmes.
   */
  /* Famille de polices */
  --font-family: "Inter", "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; /* Police moderne optimisée pour la lisibilité */

  /* Tailles de police */
  --font-size-base: 16px; /* Taille de base - Utilisée pour le texte courant */
  --font-size-sm: 0.875rem; /* Petite taille - 14px - Pour les textes secondaires */
  --font-size-lg: 1.125rem; /* Grande taille - 18px - Pour les sous-titres */
  --font-size-xl: 1.25rem; /* Très grande taille - 20px - Pour les titres */

  /* Poids de police */
  --font-weight-normal: 400; /* Poids normal - Pour le texte courant */
  --font-weight-medium: 500; /* Poids moyen - Pour mettre légèrement en évidence */
  --font-weight-semibold: 600; /* Poids semi-gras - Pour les sous-titres */
  --font-weight-bold: 700; /* Poids gras - Pour les titres et éléments importants */

  /* Hauteurs de ligne */
  --line-height-base: 1.6; /* Hauteur de ligne standard - Pour une bonne lisibilité du texte courant */
  --line-height-tight: 1.2; /* Hauteur de ligne serrée - Pour les titres */
  --line-height-loose: 2; /* Hauteur de ligne large - Pour améliorer la lisibilité des paragraphes longs */

  /**
   * Espacement
   *
   * Ces variables définissent les espacements utilisés dans l'application
   * pour les marges, les rembourrages et les écarts entre les éléments.
   * Elles sont basées sur l'unité rem pour s'adapter à la taille de police
   * de l'utilisateur et maintenir une cohérence visuelle.
   */
  --spacing-xs: 0.25rem; /* Très petit espacement - 4px - Pour les éléments très proches */
  --spacing-sm: 0.5rem; /* Petit espacement - 8px - Pour les éléments rapprochés */
  --spacing-md: 1rem; /* Espacement moyen - 16px - Pour l'espacement standard */
  --spacing-lg: 1.5rem; /* Grand espacement - 24px - Pour séparer les sections */
  --spacing-xl: 2rem; /* Très grand espacement - 32px - Pour les marges importantes */
  --spacing-2xl: 3rem; /* Espacement extra large - 48px - Pour les grandes séparations */
  --spacing-3xl: 4rem; /* Espacement triple extra large - 64px - Pour les séparations majeures */

  /**
   * Points de rupture (breakpoints) pour le responsive design
   *
   * Ces variables définissent les largeurs d'écran auxquelles la mise en page
   * s'adapte. Elles sont utilisées dans les media queries pour créer une
   * expérience cohérente sur tous les appareils.
   */
  --breakpoint-xs: 320px; /* Très petits mobiles */
  --breakpoint-sm: 576px; /* Petits mobiles et mobiles en mode paysage */
  --breakpoint-md: 768px; /* Tablettes et grands mobiles */
  --breakpoint-lg: 992px; /* Petits ordinateurs portables et tablettes en mode paysage */
  --breakpoint-xl: 1200px; /* Ordinateurs de bureau et grands portables */
  --breakpoint-2xl: 1400px; /* Grands écrans et moniteurs haute résolution */

  /**
   * Conteneurs et largeurs maximales
   *
   * Ces variables définissent les largeurs maximales pour différents types
   * de conteneurs, assurant une mise en page cohérente et lisible sur
   * différentes tailles d'écran.
   */
  --container-max-width: 1400px; /* Largeur maximale du conteneur principal */
  --container-narrow: 800px; /* Conteneur étroit pour le contenu centré */
  --container-wide: 1600px; /* Conteneur large pour les mises en page expansives */
  --content-width: 65ch; /* Largeur optimale pour la lisibilité du texte (environ 65 caractères) */

  /**
   * Variables pour les médias queries et préférences utilisateur
   *
   * Ces variables permettent de détecter les préférences de l'utilisateur
   * et d'adapter l'interface en conséquence, sans avoir à répéter les
   * media queries dans tout le code.
   */
  --prefers-dark: 0; /* Sera 1 si l'utilisateur préfère le mode sombre */
  --prefers-reduced-motion: 0; /* Sera 1 si l'utilisateur préfère réduire les animations */
  --prefers-reduced-data: 0; /* Sera 1 si l'utilisateur préfère économiser les données */
  --prefers-contrast: 0; /* Sera 1 si l'utilisateur préfère un contraste élevé */
  --is-touch-device: 0; /* Sera 1 si l'appareil utilise un écran tactile */
  --viewport-width: 100vw; /* Largeur de la fenêtre d'affichage */
  --viewport-height: 100vh; /* Hauteur de la fenêtre d'affichage */
  --safe-area-inset-top: env(
    safe-area-inset-top,
    0px
  ); /* Zone sûre pour les appareils avec encoche */
  --safe-area-inset-bottom: env(
    safe-area-inset-bottom,
    0px
  ); /* Zone sûre pour les appareils avec barre de navigation */
}

/**
 * Variables CSS - Mode sombre
 *
 * Ces variables redéfinissent les couleurs et certains effets pour le mode sombre.
 * Le mode sombre est activé soit par préférence de l'utilisateur via son système,
 * soit manuellement via le bouton de basculement de thème.
 *
 * Les couleurs sont ajustées pour :
 * - Réduire la fatigue oculaire en diminuant la luminosité globale
 * - Maintenir un contraste suffisant pour la lisibilité (WCAG AA)
 * - Créer une expérience cohérente avec le mode clair
 * - Réduire la consommation d'énergie sur les écrans OLED/AMOLED
 */
@media (prefers-color-scheme: dark) {
  :root {
    --prefers-dark: 1; /* Indique que l'utilisateur préfère le mode sombre */
  }
}

/* Application du mode sombre basé sur les préférences système ou le choix utilisateur */
:root[data-theme="dark"],
:root:not([data-theme="light"]):where([data-auto-theme="true"]):where(
    :root[style*="--prefers-dark:1"]
  ) {
  /* Couleurs principales - Palette optimisée pour l'accessibilité en mode sombre */
  --primary-color: #4dabf7;
  --primary-dark: #3b8fd7;
  --primary-light: #6fc2ff;
  --secondary-color: #40c057;
  --secondary-dark: #2f9e44;
  --secondary-light: #69db7c;
  --accent-color: #fa5252;
  --accent-dark: #e03131;
  --accent-light: #ff8787;
  --danger-color: #fa5252;
  --success-color: #40c057;
  --warning-color: #fd7e14;
  --warning-dark: #e8590c;
  --info-color: #4dabf7;

  /* Couleurs de fond et de texte */
  --background-color: #121212;
  --card-background: #1e1e1e;
  --text-color: #e9ecef;
  --text-muted: #adb5bd;
  --border-color: #343a40;
  --link-color: #4dabf7;
  --link-hover-color: #6fc2ff;

  /* Excel */
  --excel-border: #343a40;
  --excel-bg: #1a1a1a;
  --excel-cell-bg: #2a2a2a;
  --excel-header-bg: #212529;

  /* Effets */
  --soft-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
  --medium-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
  --large-shadow: 0 10px 20px rgba(0, 0, 0, 0.5);
  --gradient-primary: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  --gradient-success: linear-gradient(
    135deg,
    var(--secondary-color),
    var(--secondary-dark)
  );
  --gradient-danger: linear-gradient(
    135deg,
    var(--danger-color),
    var(--accent-dark)
  );
  --gradient-warning: linear-gradient(
    135deg,
    var(--warning-color),
    var(--warning-dark)
  );

  /* Footer et modals */
  --footer-bg: #1a1a1a;
  --footer-text: #e9ecef;
  --modal-overlay: rgba(0, 0, 0, 0.8);
  --tab-active-bg: rgba(77, 171, 247, 0.15);
  --tab-hover-bg: rgba(77, 171, 247, 0.1);

  /* Accessibilité */
  --focus-visible-color: #4dabf7;
  --focus-ring: 0 0 0 3px rgba(77, 171, 247, 0.5);
}

/**
 * Mode contraste élevé - Optimisé pour l'accessibilité WCAG AAA
 *
 * Ce mode est spécialement conçu pour les utilisateurs ayant des déficiences visuelles
 * ou des difficultés à percevoir les contrastes. Il respecte les critères WCAG AAA
 * qui exigent un ratio de contraste d'au moins 7:1 pour le texte normal.
 *
 * Caractéristiques principales :
 * - Contraste maximal entre le texte et l'arrière-plan
 * - Suppression des effets subtils comme les ombres et les dégradés
 * - Bordures plus épaisses et plus visibles
 * - Simplification visuelle générale
 */
[data-contrast="high"] {
  /* Couleurs principales avec contraste élevé */
  --primary-color: #0066cc;
  --primary-dark: #004c99;
  --primary-light: #0080ff;
  --secondary-color: #008000;
  --secondary-dark: #006600;
  --secondary-light: #00a000;
  --accent-color: #cc0000;
  --accent-dark: #990000;
  --accent-light: #ff0000;
  --danger-color: #cc0000;
  --success-color: #008000;
  --warning-color: #cc6600;
  --warning-dark: #994c00;
  --info-color: #0066cc;

  /* Couleurs de fond et de texte avec contraste élevé */
  --background-color: #ffffff;
  --card-background: #ffffff;
  --text-color: #000000;
  --text-muted: #333333;
  --border-color: #000000;
  --link-color: #0000cc;
  --link-hover-color: #0000ff;

  /* Excel avec contraste élevé */
  --excel-border: #000000;
  --excel-bg: #ffffff;
  --excel-cell-bg: #ffffff;
  --excel-header-bg: #dddddd;

  /* Effets adaptés au contraste élevé */
  --soft-shadow: none;
  --medium-shadow: none;
  --large-shadow: none;
  --gradient-primary: none;
  --gradient-success: none;
  --gradient-danger: none;
  --gradient-warning: none;

  /* Footer et modals avec contraste élevé */
  --footer-bg: #000000;
  --footer-text: #ffffff;
  --modal-overlay: rgba(0, 0, 0, 0.9);
  --tab-active-bg: #dddddd;
  --tab-hover-bg: #eeeeee;

  /* Accessibilité renforcée */
  --focus-visible-color: #0066cc;
  --focus-ring: 0 0 0 3px #0066cc;
  --contrast-ratio: 7; /* Ratio de contraste élevé pour WCAG AAA */

  /* Bordures plus épaisses pour meilleure visibilité */
  --border-width: 2px;
}

/**
 * Police pour dyslexiques - Améliorations pour la lisibilité
 *
 * Ce mode est spécialement conçu pour les utilisateurs atteints de dyslexie
 * ou ayant des difficultés de lecture. Il utilise la police OpenDyslexic,
 * spécialement conçue pour faciliter la lecture des personnes dyslexiques.
 *
 * Caractéristiques principales :
 * - Police OpenDyslexic avec des caractères distinctifs
 * - Espacement accru entre les lettres et les mots
 * - Hauteur de ligne augmentée pour faciliter le suivi
 * - Taille de police légèrement plus grande
 * - Fond légèrement teinté pour réduire le contraste extrême
 */
[data-font="dyslexic"] {
  --font-family: "OpenDyslexic", "Comic Sans MS", "Verdana", sans-serif;
  letter-spacing: 0.05em;
  word-spacing: 0.1em;
  line-height: 1.8;

  /* Améliorations supplémentaires pour la lisibilité */
  --font-size-base: 18px; /* Taille de police légèrement plus grande */
  --line-height-base: 1.8;
  --line-height-loose: 2.2;

  /* Couleurs adaptées pour réduire la fatigue visuelle */
  --background-color: #f8f8f8;
  --text-color: #333333;

  /* Espacement des paragraphes pour meilleure lisibilité */
  --paragraph-spacing: 1.5em;
}

/**
 * Réduction des animations - Conforme aux préférences de réduction de mouvement
 *
 * Ce mode est activé automatiquement lorsque l'utilisateur a configuré
 * la préférence "prefers-reduced-motion" dans son système d'exploitation.
 * Il réduit ou élimine les animations qui pourraient causer de l'inconfort
 * ou des problèmes pour certains utilisateurs (troubles vestibulaires,
 * épilepsie photosensible, etc.).
 *
 * Caractéristiques principales :
 * - Désactivation de toutes les transitions et animations non essentielles
 * - Remplacement des animations par des changements instantanés
 * - Suppression des effets de parallaxe et de défilement animé
 * - Simplification des interactions visuelles
 */
[data-motion="reduced"] {
  /* Désactiver complètement les animations et transitions */
  --animation-duration: 0.001s;
  --transition-speed: 0.001s;
  --animation-timing: step-end;

  /* Désactiver les transformations */
  --transform-scale: none;
  --transform-translate: none;

  /* Désactiver les effets de survol */
  --hover-effect: none;

  /* Désactiver les ombres animées */
  --soft-shadow: none;
  --medium-shadow: none;
  --large-shadow: none;
}

/**
 * Reset et styles de base - Optimisés pour la performance et l'accessibilité
 *
 * Cette section définit les styles de base pour tous les éléments HTML.
 * Elle commence par un reset minimal qui normalise le comportement des
 * éléments entre les navigateurs, puis définit les styles fondamentaux
 * pour le document.
 *
 * Principes appliqués :
 * - Box-sizing border-box pour un dimensionnement prévisible
 * - Reset des marges et rembourrages pour éviter les comportements inattendus
 * - Optimisations de performance (will-change, etc.)
 * - Améliorations d'accessibilité (zoom, contraste, etc.)
 */
*,
*::before,
*::after {
  box-sizing: border-box; /* Inclut les bordures et le padding dans les dimensions */
  margin: 0; /* Supprime les marges par défaut */
  padding: 0; /* Supprime les rembourrages par défaut */
}

/**
 * Détection des préférences utilisateur via media queries
 * Ces media queries mettent à jour les variables CSS en fonction des préférences de l'utilisateur
 */
@media (prefers-reduced-motion: reduce) {
  :root {
    --prefers-reduced-motion: 1;
  }
}

@media (prefers-contrast: more) {
  :root {
    --prefers-contrast: 1;
  }
}

@media (prefers-reduced-data: reduce) {
  :root {
    --prefers-reduced-data: 1;
  }
}

@media (hover: none) and (pointer: coarse) {
  :root {
    --is-touch-device: 1;
  }
}

/**
 * Élément HTML - Configuration de base du document
 */
html {
  font-size: var(
    --font-size-base
  ); /* Définit la taille de base pour les unités rem */
  scroll-behavior: smooth; /* Active le défilement fluide pour les ancres */
  height: 100%; /* Assure que html prend toute la hauteur disponible */
  text-size-adjust: 100%; /* Empêche les navigateurs mobiles d'ajuster automatiquement la taille du texte */
  -webkit-text-size-adjust: 100%; /* Pour Safari */
  -webkit-tap-highlight-color: transparent; /* Supprime la surbrillance au toucher sur iOS */

  /* Optimisations pour le rendu des polices */
  text-rendering: optimizeSpeed; /* Privilégie la vitesse de rendu sur la précision */
  -webkit-font-smoothing: antialiased; /* Lissage des polices sur WebKit */
  -moz-osx-font-smoothing: grayscale; /* Lissage des polices sur Firefox/macOS */

  /* Support des écrans haute résolution */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    -webkit-font-smoothing: subpixel-antialiased;
  }

  /* Désactiver le défilement fluide pour les utilisateurs qui préfèrent réduire les animations */
  @media (prefers-reduced-motion: reduce) {
    scroll-behavior: auto; /* Remplace le défilement fluide par un défilement instantané */
  }
}

/**
 * Élément Body - Styles principaux du document
 *
 * Ces styles définissent l'apparence globale du document et appliquent
 * plusieurs optimisations pour la performance et l'accessibilité.
 */
body {
  /* Typographie */
  font-family: var(--font-family); /* Police principale */
  color: var(--text-color); /* Couleur du texte */
  line-height: var(--line-height-base); /* Hauteur de ligne */
  font-size: 100%; /* Permet le zoom du navigateur */

  /* Arrière-plan et dimensions */
  background: var(--background-color); /* Couleur d'arrière-plan */
  min-height: 100vh; /* Hauteur minimale de 100% de la fenêtre */
  min-height: 100dvh; /* Utilisation de dvh pour une meilleure prise en charge mobile */
  overflow-y: auto; /* Permet le défilement vertical si nécessaire */
  overflow-x: hidden; /* Évite le défilement horizontal indésirable */

  /* Mise en page moderne avec CSS Grid */
  display: grid;
  grid-template-rows: auto 1fr auto; /* En-tête, contenu principal, pied de page */
  grid-template-areas:
    "header"
    "main"
    "footer";

  /* Transitions pour le changement de thème */
  transition: background-color var(--transition-speed) ease,
    color var(--transition-speed) ease;

  /* Optimisations de rendu du texte */
  -webkit-font-smoothing: antialiased; /* Lissage des polices sur WebKit */
  -moz-osx-font-smoothing: grayscale; /* Lissage des polices sur Firefox/macOS */
  text-rendering: optimizeLegibility; /* Améliore la lisibilité des polices */

  /* Optimisations de performance */
  will-change: background-color, color; /* Prépare le navigateur aux changements de ces propriétés */

  /* Optimisations modernes pour le rendu */
  content-visibility: auto; /* Optimise le rendu des éléments hors écran */
  contain: layout paint; /* Isole les éléments pour optimiser le rendu */

  /* Support des écrans haute résolution */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    font-smooth: always; /* Améliore le rendu des polices sur les écrans haute résolution */
  }

  /* Améliorations d'accessibilité */
  accent-color: var(
    --primary-color
  ); /* Couleur d'accentuation pour les éléments de formulaire */

  /* Support des appareils tactiles */
  @media (hover: none) and (pointer: coarse) {
    cursor: default; /* Désactive le curseur sur les appareils tactiles */
    touch-action: manipulation; /* Optimise les interactions tactiles */
    -webkit-tap-highlight-color: transparent; /* Supprime la surbrillance au toucher sur iOS */
  }

  /* Adaptation aux préférences de réduction de mouvement */
  @media (prefers-reduced-motion: reduce) {
    transition: none; /* Désactive les transitions si l'utilisateur préfère réduire les animations */
  }
}

/**
 * Classe pour masquer visuellement les éléments tout en les gardant accessibles
 *
 * Cette technique permet de cacher des éléments visuellement tout en les maintenant
 * accessibles aux technologies d'assistance comme les lecteurs d'écran. C'est
 * particulièrement utile pour les liens d'évitement, les labels cachés, etc.
 *
 * Cette implémentation est basée sur les meilleures pratiques actuelles et
 * a été testée avec différentes technologies d'assistance.
 */
.visually-hidden {
  position: absolute !important; /* Retire l'élément du flux normal */
  width: 1px !important; /* Dimensions minimales mais non nulles */
  height: 1px !important; /* pour que l'élément existe toujours */
  padding: 0 !important; /* Supprime tout espacement interne */
  margin: -1px !important; /* Déplace légèrement hors écran */
  overflow: hidden !important; /* Cache tout contenu débordant */
  clip: rect(0, 0, 0, 0) !important; /* Découpe l'élément pour le cacher */
  white-space: nowrap !important; /* Empêche le retour à la ligne */
  border: 0 !important; /* Supprime toute bordure */

  /* Améliorations pour s'assurer que l'élément reste accessible */
  display: block !important; /* Assure que l'élément est traité comme un bloc */
  visibility: hidden !important; /* Cache visuellement mais reste dans l'arbre d'accessibilité */
}

/**
 * Rendre visible les éléments masqués lorsqu'ils reçoivent le focus
 *
 * Cette règle permet aux éléments masqués de devenir visibles lorsqu'ils
 * reçoivent le focus clavier, ce qui est essentiel pour l'accessibilité.
 * Par exemple, les liens d'évitement deviennent visibles lorsque l'utilisateur
 * navigue au clavier, mais restent cachés pour les utilisateurs de souris.
 */
.visually-hidden:focus,
.visually-hidden:active {
  position: static !important; /* Remet l'élément dans le flux normal */
  width: auto !important; /* Dimensions automatiques basées sur le contenu */
  height: auto !important; /* pour que l'élément soit entièrement visible */
  overflow: visible !important; /* Montre tout le contenu */
  clip: auto !important; /* Annule le découpage */
  white-space: normal !important; /* Permet le retour à la ligne normal */
  visibility: visible !important; /* Rend l'élément visible */
  display: block !important; /* Assure que l'élément est traité comme un bloc */

  /* Styles visuels pour rendre l'élément bien visible et attrayant */
  text-align: center !important; /* Centre le texte */
  padding: 10px !important; /* Ajoute de l'espace interne */
  margin: 10px !important; /* Ajoute de l'espace externe */
  background-color: var(--primary-color) !important; /* Fond coloré */
  color: white !important; /* Texte blanc pour contraste */
  font-weight: bold !important; /* Texte en gras pour emphase */
  border-radius: var(--border-radius) !important; /* Coins arrondis */
  box-shadow: var(
    --medium-shadow
  ) !important; /* Ombre portée pour effet de profondeur */
}

/**
 * En-tête principal de l'application
 *
 * L'en-tête contient le logo, le titre de l'application et les contrôles
 * principaux comme le basculement de thème et les options d'accessibilité.
 * Il est conçu pour être visuellement distinctif tout en restant accessible.
 */
header {
  background: var(--gradient-primary); /* Dégradé bleu pour l'arrière-plan */
  color: white; /* Texte blanc pour contraste maximal */
  box-shadow: var(--medium-shadow); /* Ombre portée pour effet de profondeur */
  position: sticky; /* Position collante pour rester visible lors du défilement */
  top: 0; /* Colle à partir du haut de la fenêtre */
  z-index: 100; /* Valeur élevée pour s'assurer qu'il est au-dessus des autres éléments */
  border-bottom: 1px solid rgba(255, 255, 255, 0.1); /* Bordure subtile pour délimiter l'en-tête */
  padding: 0 0 var(--spacing-sm) 0; /* Padding top à 0, léger padding bottom */
  grid-area: header; /* Placement dans la grille CSS du body */

  /* Support des appareils avec encoche (comme l'iPhone X et plus récents) */
  padding-top: var(
    --safe-area-inset-top
  ); /* Ajoute un padding pour éviter l'encoche */

  /* Optimisation de performance */
  will-change: transform; /* Prépare le navigateur aux animations potentielles */
  contain: layout paint; /* Isole l'en-tête pour optimiser le rendu */

  /* Amélioration de l'accessibilité */
  color: #ffffff; /* Blanc pur pour un contraste maximal (WCAG AAA) */

  /* Animation subtile lors du défilement */
  transition: box-shadow 0.3s ease, transform 0.3s ease;

  /* Effet de réduction lors du défilement vers le bas */
  &.scrolled-down {
    box-shadow: var(--large-shadow);
    transform: translateY(-10px); /* Réduit légèrement la hauteur visible */
  }

  /* Désactivation des animations si l'utilisateur préfère réduire les animations */
  @media (prefers-reduced-motion: reduce) {
    transition: none;
    &.scrolled-down {
      transform: none;
    }
  }
}

/**
 * Contenu de l'en-tête
 *
 * Cette classe gère la disposition des éléments à l'intérieur de l'en-tête,
 * en utilisant flexbox pour aligner le titre à gauche et les contrôles à droite.
 */
.header-content {
  display: flex; /* Disposition flexible */
  justify-content: space-between; /* Espace les éléments aux extrémités */
  align-items: center; /* Centre verticalement les éléments */
  padding: 0 var(--spacing-xl); /* Suppression du padding vertical, conservation du padding horizontal */
  max-width: 1400px; /* Largeur maximale pour les grands écrans */
  margin: 0 auto; /* Centrage horizontal */
  height: 70px; /* Hauteur ajustée pour le logo de 60px */
}

/**
 * Titre principal de l'application
 *
 * Le titre utilise la police et la taille définies dans les variables,
 * avec des ajustements pour améliorer la lisibilité sur le fond coloré.
 */
header h1 {
  font-size: var(
    --font-size-xl
  ); /* Taille de police plus grande pour le titre */
  margin: 0; /* Supprime les marges par défaut */
  display: flex; /* Disposition flexible pour aligner avec l'icône */
  align-items: center; /* Centre verticalement avec l'icône */
  gap: var(--spacing-md); /* Espace augmenté entre l'icône et le texte */
  font-weight: var(--font-weight-bold); /* Police en gras pour emphase */
  min-height: 90px; /* Hauteur minimale pour accommoder le logo plus grand */

  /* Améliorations de lisibilité */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); /* Ombre légère pour améliorer la lisibilité */
  letter-spacing: 0.01em; /* Espacement légèrement accru entre les lettres */
}

/**
 * Logo de l'application
 *
 * Styles pour l'image du logo dans l'en-tête.
 * Le logo est dimensionné, arrondi et optimisé pour s'intégrer
 * harmonieusement dans l'en-tête de l'application.
 */
.logo-image {
  border-radius: 8%; /* Réduit l'arrondi pour éviter de couper les coins du logo */
  object-fit: contain; /* Assure que l'image est entièrement visible sans être coupée */
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.25); /* Ombre plus prononcée */
  border: 3px solid rgba(255, 255, 255, 0.8); /* Bordure blanche plus visible */
  transition: transform 0.3s ease, box-shadow 0.3s ease; /* Animation au survol */
  background-color: white; /* Fond blanc pour les logos avec transparence */
  display: block; /* Assure que l'image est traitée comme un bloc */
  max-width: 100%; /* S'assure que l'image ne dépasse pas son conteneur */
  height: 60px; /* Hauteur fixe de 60px comme demandé */
  width: auto; /* Largeur automatique pour maintenir le ratio d'aspect */
}

/* Animation au survol du logo */
.logo-image:hover {
  transform: scale(1.08); /* Augmentation de taille plus visible */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.35); /* Ombre plus prononcée */
  border-color: rgba(255, 255, 255, 1); /* Bordure blanche plus intense */
}

/**
 * Styles pour les boutons de navigation
 *
 * Ces styles définissent l'apparence des boutons utilisés dans la navigation,
 * notamment pour les actions d'authentification (connexion, inscription).
 */
.nav-button {
  background: none;
  border: none;
  color: inherit;
  font: inherit;
  cursor: pointer;
  padding: var(--spacing-sm) var(--spacing-md);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  border-radius: var(--border-radius-sm);
  font-weight: var(--font-weight-medium);
  position: relative;
  overflow: hidden;

  /* Effet de ripple au clic */
  &::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
  }

  &:active::after {
    animation: ripple 0.6s ease-out;
  }

  /* Icônes dans les boutons */
  i {
    font-size: 1.1em;
    margin-right: 6px;
    transition: transform 0.3s ease;
  }

  /* États de survol et focus */
  &:hover,
  &:focus-visible {
    background-color: rgba(255, 255, 255, 0.15);
    color: white;
    transform: translateY(-2px);
    outline: none;

    i {
      transform: translateY(-1px);
    }
  }

  &:active {
    transform: translateY(0);
  }

  /* Adaptation aux écrans mobiles */
  @media (max-width: 991px) {
    width: 100%;
    justify-content: center;
    padding: var(--spacing-md);
    border-radius: 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Désactivation des animations si l'utilisateur préfère réduire les animations */
  @media (prefers-reduced-motion: reduce) {
    transition: none;

    &:hover {
      transform: none;
    }

    &:active::after {
      animation: none;
    }

    i {
      transition: none;
    }
  }
}

/**
 * Styles pour le lien du logo
 */
.logo-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: inherit;
  transition: opacity 0.3s ease, transform 0.3s ease;

  &:hover {
    opacity: 0.9; /* Légère réduction d'opacité au survol */
    transform: scale(1.02); /* Léger agrandissement au survol */
    text-decoration: none;
  }

  /* Adaptation aux écrans mobiles */
  @media (max-width: 991px) {
    margin-right: auto; /* Pousse le logo à gauche */
  }
}

/**
 * Groupe de navigation d'authentification
 *
 * Cette section contient les styles pour les boutons de connexion et d'inscription,
 * ainsi que pour le menu utilisateur une fois connecté.
 */
.auth-nav-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-left: auto;

  /* Adaptation aux écrans mobiles */
  @media (max-width: 991px) {
    flex-direction: column;
    width: 100%;
    gap: 0;
    margin: 0;

    .mobile-open & {
      border-top: 1px solid rgba(255, 255, 255, 0.2);
    }
  }
}

.auth-nav {
  position: relative;

  /* Adaptation aux écrans mobiles */
  @media (max-width: 991px) {
    width: 100%;
  }
}

/* Menu utilisateur avec dropdown */
.user-nav {
  position: relative;

  > a {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: var(--spacing-sm) var(--spacing-md);
    color: white;
    text-decoration: none;
    border-radius: var(--border-radius-sm);
    transition: background-color 0.3s ease;

    i {
      font-size: 1.2rem;
    }

    &:hover,
    &:focus-visible {
      background-color: rgba(255, 255, 255, 0.15);
      outline: none;
    }
  }

  /* Dropdown du menu utilisateur */
  .user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 250px;
    background: var(--card-background);
    border-radius: var(--border-radius);
    box-shadow: var(--large-shadow);
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
    z-index: 100;
    overflow: hidden;
    border: 1px solid var(--border-color);

    /* En-tête du dropdown avec avatar et infos utilisateur */
    &-header {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      padding: var(--spacing-md);
      background: rgba(0, 0, 0, 0.03);
      border-bottom: 1px solid var(--border-color);
    }

    /* Avatar utilisateur */
    .user-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: var(--primary-color);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 1.2rem;
    }

    /* Informations utilisateur */
    .user-info {
      flex: 1;

      .user-name {
        font-weight: var(--font-weight-semibold);
        color: var(--text-color);
      }

      .user-role {
        font-size: var(--font-size-sm);
        color: var(--text-muted);
      }
    }

    /* Menu du dropdown */
    &-menu {
      list-style: none;
      margin: 0;
      padding: 0;

      li {
        margin: 0;

        a {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: var(--spacing-sm) var(--spacing-md);
          color: var(--text-color);
          text-decoration: none;
          transition: background-color 0.2s ease;

          i {
            color: var(--primary-color);
            width: 20px;
            text-align: center;
          }

          &:hover,
          &:focus-visible {
            background-color: rgba(0, 0, 0, 0.05);
            color: var(--primary-color);
            outline: none;
          }
        }
      }
    }

    /* Séparateur dans le menu */
    &-divider {
      height: 1px;
      background-color: var(--border-color);
      margin: var(--spacing-xs) 0;
    }
  }

  /* Affichage du dropdown au survol ou au focus */
  &:hover .user-dropdown,
  &:focus-within .user-dropdown,
  .user-dropdown:hover {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  /* Adaptation aux écrans mobiles */
  @media (max-width: 991px) {
    width: 100%;

    > a {
      width: 100%;
      justify-content: center;
      border-radius: 0;
    }

    .user-dropdown {
      position: static;
      width: 100%;
      box-shadow: none;
      border-radius: 0;
      border: none;
      border-top: 1px solid var(--border-color);
      transform: none;
      max-height: 0;
      padding: 0;

      &-header {
        padding: var(--spacing-md) var(--spacing-lg);
      }
    }

    &:hover .user-dropdown,
    &:focus-within .user-dropdown {
      max-height: 500px;
    }
  }
}

/**
 * Contrôles de l'en-tête
 *
 * Cette section contient les boutons pour les fonctionnalités globales
 * comme le basculement de thème et les options d'accessibilité.
 */
.header-controls {
  display: flex; /* Disposition flexible */
  gap: var(--spacing-sm); /* Espace entre les boutons */
  align-items: center; /* Centre verticalement les boutons */
}

/**
 * Navigation principale - Design moderne et accessible
 *
 * Cette section définit les styles pour la barre de navigation principale.
 * Elle utilise des techniques modernes comme CSS Grid, les variables CSS,
 * et des animations optimisées pour offrir une expérience utilisateur fluide
 * tout en restant accessible et performante.
 */
.main-nav {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 0;
  position: relative;
  z-index: 90; /* Juste en dessous du header mais au-dessus du contenu */

  /* Conteneur pour le contenu de la navigation */
  &-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    display: grid;
    grid-template-columns: 1fr auto;
    align-items: center;

    @media (max-width: 992px) {
      grid-template-columns: 1fr;
    }
  }

  /* Optimisations de performance */
  contain: layout style; /* Isole la navigation pour optimiser le rendu */
  will-change: transform, opacity; /* Prépare le navigateur aux animations */

  /* Support du défilement horizontal sur petits écrans */
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
  scroll-behavior: smooth; /* Défilement fluide */
  scroll-snap-type: x mandatory; /* Snap aux éléments lors du défilement */

  /* Masquer la barre de défilement tout en gardant la fonctionnalité */
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
  }

  /* Indicateurs de défilement pour montrer qu'il y a plus de contenu */
  &::before,
  &::after {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    width: 30px;
    pointer-events: none;
    z-index: 1;
    opacity: 0;
    transition: opacity var(--transition-speed) ease;
  }

  &::before {
    left: 0;
    background: linear-gradient(to right, rgba(0, 0, 0, 0.2), transparent);
  }

  &::after {
    right: 0;
    background: linear-gradient(to left, rgba(0, 0, 0, 0.2), transparent);
  }

  &.scroll-left::before {
    opacity: 1;
  }

  &.scroll-right::after {
    opacity: 1;
  }

  /* Liste principale des liens de navigation */
  ul {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: var(--spacing-xs); /* Espacement entre les éléments */

    /* Adaptation aux écrans larges */
    @media (min-width: 992px) {
      justify-content: center;
    }

    /* Adaptation aux écrans mobiles */
    @media (max-width: 991px) {
      flex-direction: column;
      width: 100%;
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.5s cubic-bezier(0.4, 0, 0.2, 1);

      .mobile-open & {
        max-height: 500px; /* Hauteur suffisante pour le contenu */
      }
    }
  }

  /* Éléments de liste */
  li {
    position: relative;
    margin: 0;

    /* Effet de survol pour indiquer les sous-menus */
    &.has-submenu > a::after {
      content: "▾";
      margin-left: 6px;
      font-size: 0.8em;
      transition: transform 0.3s ease;
    }

    &.has-submenu:hover > a::after {
      transform: rotate(180deg);
    }

    /* Adaptation aux écrans mobiles */
    @media (max-width: 991px) {
      width: 100%;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);

      &:last-child {
        border-bottom: none;
      }
    }
  }

  /* Liens de navigation */
  a {
    display: flex;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    color: white;
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-speed) ease;
    position: relative;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius-sm);
    white-space: nowrap;

    /* Icônes dans les liens */
    i {
      margin-right: 8px;
      font-size: 1.1em;
      transition: transform 0.3s ease;
    }

    /* États de survol et focus */
    &:hover,
    &:focus-visible {
      background-color: rgba(255, 255, 255, 0.15);
      color: white;
      outline: none;

      i {
        transform: translateY(-2px);
      }
    }

    /* État actif */
    &.active {
      font-weight: var(--font-weight-bold);
      background-color: rgba(255, 255, 255, 0.15);

      &::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 3px;
        background-color: white;
        animation: navIndicator 0.3s ease forwards;
        border-radius: 3px 3px 0 0;
      }
    }

    /* Adaptation aux écrans mobiles */
    @media (max-width: 991px) {
      padding: var(--spacing-md) var(--spacing-sm);
      width: 100%;
      border-radius: 0;

      &.active::after {
        left: 0;
        bottom: -1px;
        width: 4px;
        height: 100%;
        border-radius: 0;
        animation: none;
      }
    }
  }
}

/* Animation de l'indicateur de navigation active */
@keyframes navIndicator {
  from {
    transform: scaleX(0.7);
    opacity: 0.7;
  }
  to {
    transform: scaleX(1);
    opacity: 1;
  }
}

/**
 * Bouton de menu mobile
 */
.mobile-menu-toggle {
  display: none;
  background: transparent;
  border: none;
  width: 44px;
  height: 44px;
  padding: 8px;
  cursor: pointer;
  position: relative;
  z-index: 100;

  /* Adaptation aux écrans mobiles */
  @media (max-width: 991px) {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: auto;
  }

  /* Hamburger icon */
  .hamburger {
    width: 28px;
    height: 20px;
    position: relative;

    span {
      display: block;
      position: absolute;
      height: 3px;
      width: 100%;
      background: white;
      border-radius: 3px;
      opacity: 1;
      left: 0;
      transform: rotate(0deg);
      transition: 0.25s ease-in-out;

      &:nth-child(1) {
        top: 0px;
      }

      &:nth-child(2),
      &:nth-child(3) {
        top: 9px;
      }

      &:nth-child(4) {
        top: 18px;
      }
    }
  }

  /* Animation du hamburger en X */
  &[aria-expanded="true"] .hamburger {
    span {
      &:nth-child(1) {
        top: 9px;
        width: 0%;
        left: 50%;
      }

      &:nth-child(2) {
        transform: rotate(45deg);
      }

      &:nth-child(3) {
        transform: rotate(-45deg);
      }

      &:nth-child(4) {
        top: 9px;
        width: 0%;
        left: 50%;
      }
    }
  }
}

/* Zone de contenu principal améliorée */
main {
  padding: var(--spacing-xl);
  max-width: var(--container-max-width);
  width: 100%;
  margin: 0 auto;
  min-height: 70vh; /* Assurer une hauteur minimale pour le contenu principal */
  min-height: 70dvh; /* Utilisation de dvh pour une meilleure prise en charge mobile */
  grid-area: main; /* Placement dans la grille CSS du body */

  /* Amélioration de l'accessibilité - focus visible */
  outline: none;

  /* Optimisations de performance modernes */
  contain: content; /* Isolation du contenu pour optimiser le rendu */
  content-visibility: auto; /* Optimise le rendu des éléments hors écran */

  /* Mise en page moderne avec CSS Grid */
  display: grid;
  grid-template-columns: minmax(0, 1fr);
  justify-items: center;

  /* Espacement entre les sections */
  gap: var(--spacing-xl);

  /* Adaptation aux appareils mobiles */
  @media (max-width: 768px) {
    padding: var(--spacing-lg) var(--spacing-md);
    gap: var(--spacing-lg);
  }

  /* Support des écrans larges */
  @container (min-width: 1200px) {
    grid-template-columns: minmax(0, 1fr) minmax(0, 1fr);
    gap: var(--spacing-2xl);
  }

  /* Adaptation aux préférences de réduction de données */
  @media (prefers-reduced-data: reduce) {
    & img {
      content-visibility: auto; /* Charge les images uniquement lorsqu'elles sont visibles */
    }
  }
}

/* Écrans améliorés */
.screen {
  display: none;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--medium-shadow);
  margin-bottom: var(--spacing-lg);
  transition: background-color var(--transition-speed) ease,
    transform var(--transition-speed) ease,
    box-shadow var(--transition-speed) ease;
  /* Amélioration de la performance */
  will-change: transform, opacity;
  /* Largeur fixe pour tous les écrans */
  width: 1000px;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
  /* Amélioration visuelle */
  border: 1px solid var(--border-color);
  overflow: hidden; /* Pour s'assurer que les éléments enfants respectent le border-radius */
  /* Assurer que le contenu respecte les dimensions */
  box-sizing: border-box;
}

/* Assurer que tous les éléments enfants directs des écrans respectent la largeur */
.screen > * {
  width: 1000px;
  max-width: 1000px;
  box-sizing: border-box;
}

/* Ajustement spécifique pour la description d'introduction */
.intro-description {
  width: 1000px;
  max-width: 1000px;
  box-sizing: border-box;
}

/* Styles spécifiques pour chaque écran */
#introduction-screen,
#simulation-screen,
#results-screen {
  width: 100%;
  max-width: 100%;
  padding: 12px;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

/* Style spécifique pour le titre de bienvenue */
#introduction-screen h2 {
  margin: 12px;
  color: var(--primary-color);
  font-size: 1.8rem;
  text-align: center;
  font-weight: var(--font-weight-bold);
  padding-bottom: var(--spacing-sm);
  position: relative;
}

/* Ligne décorative sous le titre */
#introduction-screen h2::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background-color: var(--primary-color);
  border-radius: 2px;
}

/* Assurer que les conteneurs principaux dans les écrans respectent la largeur */
.main-container {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  display: flex;
  gap: var(--spacing-xl);
}

.simulation-steps {
  width: 400px;
  flex-shrink: 0;
  box-sizing: border-box;
}

.excel-sheet-wrapper {
  flex: 1;
  min-width: 0;
  box-sizing: border-box;
}

/* ===== ÉCRAN DE SIMULATION ===== */

.simulation-header {
  background-color: var(--color-primary);
  color: white;
  padding: var(--spacing-md) var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  transition: all var(--transition-normal);
  border-radius: var(--radius-md);
}

.simulation-header h2 {
  margin: 0;
  color: white;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.simulation-info {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xs);
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item strong {
  color: black;
  font-weight: var(--font-weight-medium);
  margin-right: var(--spacing-xs);
  font-size: var(--font-size-sm);
}

.info-item span {
  font-weight: var(--font-weight-bold);
  color: black;
  font-size: var(--font-size-base);
}

@media (max-width: 992px) {
  .main-container {
    flex-direction: column;
  }

  .simulation-steps {
    width: 70%;
  }
}

.card-step {
  opacity: 0.8;
  transition: all var(--transition-normal);
  width: 100%;
}

.card-step:not(.hidden) {
  animation: slideUp 0.5s ease forwards;
  opacity: 1;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card-step h3 {
  color: var(--color-primary);
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.card-step h3::before {
  content: "";
  display: inline-block;
  width: 24px;
  height: 24px;
  background-color: var(--color-primary);
  border-radius: 50%;
  color: white;
  text-align: center;
  line-height: 24px;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
}

.instruction-text {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-md);
  line-height: var(--line-height-relaxed);
}

.highlight-dividend,
.highlight-divisor {
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  padding: 0 var(--spacing-xs);
  background-color: rgba(67, 97, 238, 0.1);
  border-radius: var(--radius-sm);
}

.highlight-divisor {
  color: var(--color-secondary-dark);
  background-color: rgba(60, 207, 207, 0.1);
}

.input-action-group {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.input-action-group input {
  flex: 1;
  padding: var(--spacing-md);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-md);
  font-size: var(--font-size-lg);
  transition: all var(--transition-fast);
}

.input-action-group input:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
  outline: none;
}

.digit-selection {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.digit {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  background-color: var(--color-background-light);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.digit.selected {
  background-color: var(--color-primary);
  color: white;
}

.arrow-lowering {
  font-size: var(--font-size-2xl);
  color: var(--color-accent);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
}

/* ===== RÉSULTAT FINAL ===== */
#final-summary {
  border-left-color: var(--color-success);
}

#final-summary h3 {
  color: var(--color-success);
}

.final-formula {
  font-size: var(--font-size-xl);
  text-align: center;
  margin: var(--spacing-lg) 0;
  padding: var(--spacing-md);
  background-color: var(--color-background-light);
  border-radius: var(--radius-md);
  font-family: var(--font-mono);
}

.final-congrats {
  text-align: center;
  font-size: var(--font-size-lg);
  color: var(--color-success);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-lg);
}

.screen:not(.hidden) {
  display: flex;
  /* Animation d'entrée subtile */
  animation: screenFadeIn 0.4s var(--animation-timing) forwards;
}

@keyframes screenFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Désactiver l'animation pour les utilisateurs qui préfèrent réduire les animations */
@media (prefers-reduced-motion: reduce) {
  .screen:not(.hidden) {
    animation: none;
  }
}

.intro-description {
  background: rgba(52, 152, 219, 0.1);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius);
  border-left: 4px solid var(--primary-color);
  /* Amélioration de l'accessibilité - lisibilité */
  font-size: 17px;
  line-height: var(--line-height-relaxed);
  /* Amélioration visuelle */
  box-shadow: var(--soft-shadow);
  /* Amélioration du contraste */
  color: var(--text-color);
  font-weight: bolder;
  /* Largeur cohérente avec .screen */
  width: 100%;
  max-width: 100%;
  /* Styles de modern-simulation.css */
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

/* Boutons améliorés pour l'accessibilité et l'expérience utilisateur */
button {
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: var(--font-weight-semibold);
  transition: transform var(--transition-speed) ease,
    box-shadow var(--transition-speed) ease,
    background-color var(--transition-speed) ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  /* Amélioration de l'accessibilité */
  min-height: 44px; /* Taille minimale pour les cibles tactiles */
  position: relative;
  overflow: hidden;
  /* Amélioration visuelle */
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
  /* Amélioration de la performance */
  will-change: transform, box-shadow;
}

button:hover {
  transform: translateY(-3px);
  box-shadow: var(--medium-shadow);
  background-color: var(--primary-light);
}

button:active {
  transform: translateY(-1px);
  box-shadow: var(--soft-shadow);
}

button:focus {
  outline: none;
  box-shadow: var(--focus-ring);
}

/* Effet de ripple pour les boutons */
button::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%, -50%);
  transform-origin: 50% 50%;
}

button:active::after {
  animation: ripple 0.6s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0) translate(-50%, -50%);
    opacity: 0.5;
  }
  100% {
    transform: scale(20, 20) translate(-50%, -50%);
    opacity: 0;
  }
}

/* Désactiver l'animation pour les utilisateurs qui préfèrent réduire les animations */
@media (prefers-reduced-motion: reduce) {
  button:active::after {
    animation: none;
  }
}

.btn-primary {
  background: var(--gradient-primary);
}

.btn-primary:hover {
  background: linear-gradient(
    135deg,
    var(--primary-light),
    var(--primary-color)
  );
}

.btn-secondary {
  background: #f8f9fa;
  color: var(--text-color);
  border: 1px solid var(--border-color);
  text-shadow: none;
}

.btn-secondary:hover {
  background: #ffffff;
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.btn-success {
  background: var(--gradient-success);
}

.btn-success:hover {
  background: linear-gradient(
    135deg,
    var(--secondary-light),
    var(--secondary-color)
  );
}

.btn-danger {
  background: var(--gradient-danger);
}

.btn-danger:hover {
  background: linear-gradient(135deg, var(--accent-light), var(--accent-color));
}

.btn-restart {
  background: var(--gradient-danger);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-sm);
  min-height: 36px;
}

.btn-icon {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  padding: 0;
  min-width: auto;
  background: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-color);
  text-shadow: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon:hover {
  background-color: rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.btn-icon i {
  font-size: 1.2rem;
}

/* Bouton désactivé */
button:disabled,
button[disabled] {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* Inputs et formulaires améliorés */
/* Styles pour le formulaire de division */
.division-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  width: 100%; /* Légèrement plus petit que .screen pour avoir des marges */
  max-width: 100%;
  margin: 12px auto; /* Ajout de marges verticales */
  padding: 12px; /* Ajout de padding */
  /* Styles de modern-simulation.css */
  background-color: var(--color-background);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  margin-bottom: var(--spacing-xl);
}

/* Mise en page en deux colonnes pour le formulaire */
.form-columns {
  display: flex;
  gap: var(--spacing-xl);
  align-items: flex-start;
  width: 100%;
}

.form-column-left {
  flex: 1;
  min-width: 0;
}

.form-column-right {
  flex: 1;
  min-width: 0;
}

/* Responsive : une seule colonne sur mobile */
@media (max-width: 768px) {
  .form-columns {
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  .form-column-left,
  .form-column-right {
    width: 100%;
  }
}

.form-section {
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  padding: var(--spacing-sm) var(--spacing-md);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: box-shadow 0.3s ease, transform 0.2s ease;
  /* Styles de modern-simulation.css */
  margin-bottom: var(--spacing-xl);
}

.form-section:hover {
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.section-title {
  font-size: 1rem;
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
  padding-bottom: 6px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  position: relative;
  font-weight: 600;
  letter-spacing: 0.3px;
  /* Styles de modern-simulation.css */
  display: flex;
  align-items: center;
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-md);
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  width: 100%;
  margin: var(--spacing-sm) 0;
}

@media (min-width: 768px) {
  .input-group {
    flex-direction: row;
    flex-wrap: wrap;
    align-items: flex-end;
  }
}

.input-field {
  flex: 1;
  min-width: 180px;
  position: relative;
}

.input-field input {
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.15);
  background-color: #fff;
  font-size: 0.95rem;
  transition: all 0.2s ease;
  width: 100%;
}

.input-field input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
  outline: none;
}

.input-field label {
  font-size: 0.9rem;
  margin-bottom: 4px;
  color: #555;
}

.generate-numbers-container {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-sm);
  width: 100%;
  padding-top: var(--spacing-sm);
  border-top: 1px dashed rgba(0, 0, 0, 0.08);
}

.generate-numbers-container .btn-secondary {
  padding: 6px 16px;
  font-size: 0.9rem;
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  transition: all 0.3s ease;
  border-radius: 4px;
  font-weight: 500;
}

.generate-numbers-container .btn-secondary:hover {
  background-color: var(--accent-color-dark);
  transform: translateY(-2px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

/* Styles pour les groupes de boutons radio */
.radio-group {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-xs);
  padding: 4px;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  background-color: var(--card-background);
  border: none; /* Suppression de la bordure */
  transition: background-color var(--transition-speed) ease,
    box-shadow var(--transition-speed) ease;
  margin: 2px;
  font-size: 0.9rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); /* Légère ombre pour maintenir la distinction visuelle */
}

.radio-option:hover {
  background-color: rgba(52, 152, 219, 0.1);
  box-shadow: 0 2px 5px rgba(52, 152, 219, 0.15);
}

.radio-option input[type="radio"] {
  margin: 0;
  cursor: pointer;
  width: 14px;
  height: 14px;
}

.radio-option input[type="radio"]:checked + label {
  font-weight: var(--font-weight-semibold);
  color: var(--primary-color);
}

/* Style pour les options radio sélectionnées */
.radio-option input[type="radio"]:checked ~ label,
.radio-option:has(input[type="radio"]:checked) {
  background-color: rgba(52, 152, 219, 0.1);
  box-shadow: 0 2px 5px rgba(52, 152, 219, 0.15);
  /* Styles de modern-simulation.css */
  background-color: rgba(67, 97, 238, 0.1);
  border-color: var(--color-primary);
  color: var(--color-primary);
  font-weight: var(--font-weight-semibold);
  transform: translateY(-2px);
}

.radio-option label {
  cursor: pointer;
  user-select: none;
  font-size: 0.9rem;
  padding: 2px 0;
  /* Styles de modern-simulation.css */
  width: 100%;
  text-align: center;
  padding: var(--spacing-md);
  transition: all var(--transition-fast);
}

/* Ajustement pour les types de division et options de difficulté */
.division-types,
.difficulty-options {
  flex-wrap: wrap;
  justify-content: center;
  gap: 6px; /* Réduit l'espacement entre les options */
  width: 1000px;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
  /* Styles de modern-simulation.css */
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.division-types .radio-option {
  flex: 0 0 auto;
  min-width: 130px;
  margin-bottom: 4px;
}

/* Ajustement pour les options de difficulté */
.difficulty-options {
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-xs);
}

.difficulty-options .radio-option {
  flex: 0 0 auto;
  min-width: 80px;
  margin-bottom: 4px;
}

/* Styles spécifiques pour les options de difficulté */
.difficulty-options .radio-option {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  outline: none !important;
  background-color: var(--card-background);
}

/* Style spécifique pour le focus et le hover */
.difficulty-options .radio-option:focus,
.difficulty-options .radio-option:focus-within,
.difficulty-options .radio-option:hover {
  background-color: rgba(52, 152, 219, 0.1);
  box-shadow: 0 2px 5px rgba(52, 152, 219, 0.15);
}

/* Couleurs pour les niveaux de difficulté */
#difficulty-very-easy:checked + label {
  color: #2ecc71; /* Vert clair */
}

#difficulty-easy:checked + label {
  color: var(--success-color);
}

#difficulty-medium:checked + label {
  color: var(--warning-color);
}

#difficulty-hard:checked + label {
  color: var(--danger-color);
}

#difficulty-expert:checked + label {
  color: #8e44ad; /* Violet */
}

/* Styles pour les actions du formulaire */
.preview-row {
  text-align: center;
  margin: var(--spacing-sm) 0;
  font-size: 0.9rem;
  color: #666;
  background-color: rgba(0, 0, 0, 0.02);
  padding: 8px;
  border-radius: 4px;
  border: 1px dashed rgba(0, 0, 0, 0.1);
}

.preview-row span {
  font-weight: 600;
  color: var(--primary-color);
}

.preview-separator {
  margin: 0 6px;
  color: #888;
}

.form-actions {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-md);
}

.form-actions .btn-primary {
  min-width: 180px;
  font-size: 1rem;
  padding: 8px 20px;
  background-color: var(--primary-color);
  border: none;
  border-radius: 4px;
  color: white;
  font-weight: 500;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(52, 152, 219, 0.3);
}

.form-actions .btn-primary:hover {
  background-color: #2980b9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(52, 152, 219, 0.4);
}

.form-actions .btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);
}

label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
  /* Amélioration de l'accessibilité */
  font-size: var(--font-size-base);
}

input {
  width: 100%;
  padding: var(--spacing-md);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  background-color: var(--card-background);
  color: var(--text-color);
  transition: border-color var(--transition-speed) ease,
    box-shadow var(--transition-speed) ease;
  /* Amélioration de l'accessibilité */
  font-size: var(--font-size-base);
  min-height: 44px; /* Taille minimale pour les cibles tactiles */
  /* Amélioration visuelle */
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

input:hover {
  border-color: var(--primary-light);
}

input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: var(--focus-ring);
}

/* Validation des champs */
input:valid {
  border-color: var(--border-color);
}

input:invalid {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 1px rgba(231, 76, 60, 0.2);
}

.input-action-group {
  display: flex;
  gap: var(--spacing-md);
  margin: var(--spacing-md) 0;
  align-items: center;
  flex-wrap: wrap;
}

.preview-row {
  margin: var(--spacing-md) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-md);
  background-color: rgba(52, 152, 219, 0.05);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  text-align: center;
}

.preview-separator {
  margin: 0 var(--spacing-sm);
  color: var(--text-muted);
  font-weight: var(--font-weight-bold);
}

/* Simulation */
.main-container {
  display: flex;
  flex-direction: row;
  gap: 20px;
  width: 100%;
}

.simulation-steps {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.simulation-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
  width: 100%;
  position: relative;
}

.simulation-info {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 10px;
}

.info-item {
  background: var(--card-background);
  padding: 8px 12px;
  border-radius: 6px;
  border-left: 3px solid var(--primary-color);
  box-shadow: var(--soft-shadow);
}

.card-step {
  background: var(--card-background);
  border-radius: var(--border-radius);
  padding: 20px;
  box-shadow: var(--soft-shadow);
  transition: all 0.3s ease;
}

.card-step:hover {
  box-shadow: var(--medium-shadow);
}

.card-step h3 {
  margin-bottom: 15px;
  color: var(--primary-color);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 8px;
}

.instruction-text {
  background: rgba(52, 152, 219, 0.1);
  padding: 12px;
  border-radius: 8px;
  margin: 15px 0;
  text-align: center;
  font-weight: 500;
}

.highlight-dividend {
  color: #ff851b;
  font-weight: bold;
}

.highlight-divisor {
  color: #0074d9;
  font-weight: bold;
}

.digit-selection {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin: 15px 0;
}

.digit-selection button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  padding: 0;
  font-size: 1.2rem;
  font-weight: bold;
}

.final-formula {
  font-size: 1.2em;
  margin: 1em 0;
  background: rgba(52, 152, 219, 0.1);
  padding: 15px;
  border-radius: 8px;
  text-align: center;
}

.final-congrats {
  font-size: 1.1em;
  color: var(--success-color);
  margin: 1em 0;
}

/* Animation de la flèche d'abaissement */
.arrow-lowering {
  display: inline-block;
  font-size: 1.5em;
  color: var(--primary-color);
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(5px);
  }
}

/* Excel sheet styling */
.excel-sheet-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 500px;
  background: var(--excel-bg);
  border: 1px solid var(--excel-border);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--soft-shadow);
  transition: all 0.3s ease;
  position: relative;
  min-width: 0;
}

.excel-sheet-wrapper.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  border-radius: 0;
}

.excel-sheet-header {
  background: var(--gradient-primary);
  padding: 12px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.excel-sheet-header h3 {
  margin: 0;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.excel-actions {
  display: flex;
  gap: 8px;
}

/* Excel toolbar */
.excel-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 15px;
  background-color: rgba(0, 0, 0, 0.03);
  border-bottom: 1px solid var(--excel-border);
}

.excel-tools {
  display: flex;
  gap: 10px;
}

.btn-tool {
  background-color: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 0.85rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-tool:hover {
  background-color: rgba(52, 152, 219, 0.1);
  border-color: var(--primary-color);
}

.btn-tool i {
  font-size: 0.9rem;
  color: var(--primary-color);
}

.excel-select {
  background-color: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 6px 10px;
  font-size: 0.85rem;
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.excel-select:hover {
  border-color: var(--primary-color);
}

.excel-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Excel legend */
.excel-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  padding: 10px 15px;
  background-color: rgba(0, 0, 0, 0.02);
  border-bottom: 1px solid var(--excel-border);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.85rem;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 3px;
  display: inline-block;
}

.legend-text {
  color: var(--text-color);
}

.excel-container {
  flex: 1;
  overflow: auto;
  position: relative;
  scrollbar-width: thin;
  scrollbar-color: var(--primary-color) var(--excel-bg);
}

.excel-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.excel-container::-webkit-scrollbar-track {
  background: var(--excel-bg);
  border-radius: 4px;
}

.excel-container::-webkit-scrollbar-thumb {
  background-color: var(--primary-color);
  border-radius: 4px;
}

.excel-footer {
  padding: 10px 15px;
  border-top: 1px solid var(--excel-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--card-background);
}

.excel-status {
  display: flex;
  align-items: center;
  font-size: 0.85rem;
  color: var(--text-muted);
  gap: 8px;
}

.excel-status-separator {
  color: var(--border-color);
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

#zoom-level {
  font-size: 0.9rem;
  font-weight: 500;
  min-width: 50px;
  text-align: center;
}

/* Tableau Excel */
#excel-table {
  border-collapse: collapse;
  width: 100%;
  font-size: 0.9rem;
  table-layout: fixed;
  transition: all 0.3s ease;
}

#excel-table.no-grid td {
  border-color: transparent;
}

#excel-table.compact-view td {
  padding: 4px 6px;
  height: 28px;
  font-size: 0.85rem;
}

#excel-table.detailed-view td {
  padding: 8px 10px;
  height: 36px;
  font-size: 0.95rem;
}

#excel-table th {
  background-color: var(--card-background);
  color: var(--text-color);
  font-weight: 600;
  text-align: center;
  padding: 8px 4px;
  position: sticky;
  top: 0;
  z-index: 1;
  border-bottom: 1px solid var(--excel-border);
  transition: all 0.2s ease;
}

#excel-table td {
  border: 1px solid var(--excel-border);
  padding: 6px 8px;
  text-align: left;
  height: 32px;
  min-width: 40px;
  background: var(--excel-cell-bg);
  transition: all 0.2s ease;
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

#excel-table td:hover {
  background-color: rgba(52, 152, 219, 0.05);
}

#excel-table td:focus-within {
  background-color: rgba(52, 152, 219, 0.1);
  box-shadow: inset 0 0 0 2px var(--primary-color);
  outline: none;
}

#excel-table tr:nth-child(even) {
  background-color: rgba(0, 0, 0, 0.02);
}

#excel-table tr:hover {
  background-color: rgba(52, 152, 219, 0.03);
}

/* Styles pour les différents types d'opérations */
#excel-table td span.quotient-cell {
  border-left: 3px solid var(--success-color);
  padding-left: 8px;
  display: inline-block;
  margin: 2px 0;
  background-color: rgba(46, 204, 113, 0.05);
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
  color: var(--success-color);
}

#excel-table td span.multiplication-cell {
  border-left: 3px solid var(--danger-color);
  padding-left: 8px;
  display: inline-block;
  margin: 2px 0;
  background-color: rgba(231, 76, 60, 0.05);
  padding: 4px 8px;
  border-radius: 4px;
  color: var(--danger-color);
}

#excel-table td span.subtraction-cell {
  border-left: 3px solid var(--primary-color);
  padding-left: 8px;
  display: inline-block;
  margin: 2px 0;
  background-color: rgba(52, 152, 219, 0.05);
  padding: 4px 8px;
  border-radius: 4px;
  color: var(--primary-color);
}

#excel-table td span.lowering-cell {
  border-left: 3px solid var(--warning-color);
  padding-left: 8px;
  display: inline-block;
  margin: 2px 0;
  background-color: rgba(243, 156, 18, 0.05);
  padding: 4px 8px;
  border-radius: 4px;
  color: var(--warning-color);
}

#excel-table td span.result-cell {
  border-left: 3px solid var(--info-color);
  padding-left: 8px;
  display: inline-block;
  margin: 2px 0;
  background-color: rgba(52, 152, 219, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: bold;
  color: var(--info-color);
}

/* Styles pour la structure de division */
#excel-table td span.divisor-value {
  font-size: 1.1rem;
  padding: 2px 8px;
  border-radius: 4px;
  background-color: rgba(52, 152, 219, 0.05);
}

#excel-table td span.dividend-value {
  font-size: 1.1rem;
  padding: 2px 8px;
  border-radius: 4px;
  background-color: rgba(52, 152, 219, 0.05);
}

#excel-table td span.operation-label {
  font-size: 0.9rem;
  color: var(--text-muted);
  white-space: nowrap;
}

#excel-table td span.result-label {
  font-size: 0.9rem;
  font-weight: 500;
  white-space: nowrap;
}

#excel-table td span.verification-cell {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: rgba(46, 204, 113, 0.1);
  color: var(--text-color);
  font-weight: 500;
}

#excel-table td span.arrow-lowering {
  font-size: 1.2rem;
  color: var(--warning-color);
  display: inline-block;
  animation: arrowPulse 1.5s infinite;
}

@keyframes arrowPulse {
  0% {
    transform: translateY(0);
    opacity: 0.7;
  }
  50% {
    transform: translateY(3px);
    opacity: 1;
  }
  100% {
    transform: translateY(0);
    opacity: 0.7;
  }
}

/* Highlight mode */
#excel-table.highlight-mode td span.quotient-cell {
  background-color: rgba(46, 204, 113, 0.2);
  box-shadow: 0 2px 5px rgba(46, 204, 113, 0.2);
}

#excel-table.highlight-mode td span.multiplication-cell {
  background-color: rgba(231, 76, 60, 0.2);
  box-shadow: 0 2px 5px rgba(231, 76, 60, 0.2);
}

#excel-table.highlight-mode td span.subtraction-cell {
  background-color: rgba(52, 152, 219, 0.2);
  box-shadow: 0 2px 5px rgba(52, 152, 219, 0.2);
}

#excel-table.highlight-mode td span.lowering-cell {
  background-color: rgba(243, 156, 18, 0.2);
  box-shadow: 0 2px 5px rgba(243, 156, 18, 0.2);
}

#excel-table.highlight-mode td span.result-cell {
  background-color: rgba(52, 152, 219, 0.25);
  box-shadow: 0 2px 5px rgba(52, 152, 219, 0.25);
}

/* Animation pour les nouvelles opérations */
@keyframes highlightOperation {
  0% {
    transform: scale(1.05);
    box-shadow: 0 0 10px rgba(52, 152, 219, 0.4);
    background-color: rgba(52, 152, 219, 0.2);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.2);
    background-color: rgba(52, 152, 219, 0.1);
  }
  100% {
    transform: scale(1);
    box-shadow: none;
  }
}

#excel-table td span.new-operation {
  animation: highlightOperation 1.5s ease-out;
}

/* Toast notifications */
.toast-container {
  position: fixed;
  bottom: 20px;
  left: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.toast {
  min-width: 250px;
  max-width: 350px;
  padding: 15px 20px;
  border-radius: var(--border-radius);
  background: var(--card-background);
  color: var(--text-color);
  box-shadow: var(--medium-shadow);
  display: flex;
  align-items: center;
  gap: 10px;
  transform: translateX(-100%);
  opacity: 0;
  animation: slideIn 0.3s forwards, fadeOut 0.5s 3s forwards;
}

.toast.success {
  border-left: 5px solid var(--success-color);
}

.toast.error {
  border-left: 5px solid var(--danger-color);
}

.toast.info {
  border-left: 5px solid var(--info-color);
}

.toast.warning {
  border-left: 5px solid var(--warning-color);
}

@keyframes slideIn {
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeOut {
  to {
    opacity: 0;
    transform: translateX(-10px);
  }
}

/* Aide Excel */
.excel-help-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
}

.excel-help-content {
  background: var(--card-background);
  border-radius: var(--border-radius);
  padding: 25px;
  max-width: 500px;
  width: 90%;
  box-shadow: var(--medium-shadow);
  animation: scaleIn 0.3s ease;
}

.excel-help-content h3 {
  color: var(--primary-color);
  margin-top: 0;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 10px;
  margin-bottom: 15px;
}

.excel-help-content ul {
  padding-left: 20px;
  margin-bottom: 20px;
}

.excel-help-content li {
  margin-bottom: 8px;
}

.excel-help-content button {
  margin-top: 15px;
  margin-left: auto;
  margin-right: auto;
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Panneau d'accessibilité */
.panel {
  position: fixed;
  top: 0;
  right: -350px;
  width: 350px;
  height: 100vh;
  background-color: var(--card-background);
  box-shadow: var(--medium-shadow);
  z-index: 1000;
  transition: right var(--transition-speed) ease;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel:not(.hidden) {
  right: 0;
}

.panel-header {
  background: var(--gradient-primary);
  color: white;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  margin: 0;
  font-size: 1.2rem;
}

.panel-close {
  background: transparent;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color var(--transition-speed) ease;
}

.panel-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.panel-close:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);
}

.panel-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.option-group {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.range-control {
  display: flex;
  align-items: center;
  gap: 10px;
}

.btn-small {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 5px 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-speed) ease;
}

.btn-small:hover {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Switch toggle */
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--border-color);
  transition: var(--transition-speed);
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: var(--transition-speed);
  border-radius: 50%;
}

input:checked + .slider {
  background-color: var(--primary-color);
}

input:focus + .slider {
  box-shadow: var(--focus-ring);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* Footer styles */
footer {
  background-color: var(--footer-bg);
  color: var(--footer-text);
  padding: 40px 20px 20px;
  margin-top: 40px;
  position: relative;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  gap: 30px;
}

.footer-section {
  flex: 1;
  min-width: 250px;
}

.footer-section h3 {
  font-size: 1.2rem;
  margin-bottom: 15px;
  position: relative;
  padding-bottom: 10px;
}

.footer-section h3::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background-color: var(--primary-color);
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section li {
  margin-bottom: 10px;
}

.footer-section a {
  color: var(--footer-text);
  text-decoration: none;
  transition: color var(--transition-speed) ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.footer-section a:hover {
  color: var(--primary-color);
}

.footer-section a:focus {
  outline: none;
  color: var(--primary-color);
  text-decoration: underline;
}

.social-links {
  display: flex;
  gap: 15px;
  margin-top: 15px;
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transition: all var(--transition-speed) ease;
}

.social-links a:hover {
  background-color: var(--primary-color);
  transform: translateY(-3px);
}

.footer-preferences {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.footer-btn {
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  color: var(--footer-text);
  padding: 8px 12px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all var(--transition-speed) ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
}

.footer-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.footer-btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--primary-color);
}

.footer-bottom {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.footer-version {
  font-size: 0.9rem;
  color: var(--text-muted);
}

.footer-version a {
  color: var(--footer-text);
  text-decoration: none;
  transition: color var(--transition-speed) ease;
}

.footer-version a:hover {
  color: var(--primary-color);
  text-decoration: underline;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--modal-overlay);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.modal-overlay:not(.hidden) {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: var(--card-background);
  border-radius: var(--border-radius);
  padding: 25px;
  max-width: 700px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--medium-shadow);
  position: relative;
  animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-close {
  position: absolute;
  top: 15px;
  right: 15px;
  background: transparent;
  border: none;
  color: var(--text-muted);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.modal-close:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: var(--text-color);
}

.modal-footer {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* Tabs */
.tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 20px;
  overflow-x: auto;
  scrollbar-width: none;
}

.tabs::-webkit-scrollbar {
  display: none;
}

.tab-btn {
  padding: 10px 20px;
  background: transparent;
  border: none;
  border-bottom: 3px solid transparent;
  color: var(--text-color);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.tab-btn:hover {
  background-color: var(--tab-hover-bg);
}

.tab-btn.active {
  border-bottom-color: var(--primary-color);
  color: var(--primary-color);
  background-color: var(--tab-active-bg);
}

.tab-content {
  margin-bottom: 20px;
}

.tab-pane {
  display: none;
  animation: fadeIn 0.3s ease;
}

.tab-pane.active {
  display: block;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.about-content {
  margin-bottom: 20px;
}

.about-content h3 {
  margin-top: 20px;
  margin-bottom: 10px;
  color: var(--primary-color);
}

/* Utilitaires */
.hidden {
  display: none !important;
}

/* Responsive design */
@media (max-width: 900px) {
  .main-container {
    flex-direction: column;
  }

  .excel-sheet-wrapper {
    height: 400px;
  }

  .footer-content {
    flex-direction: column;
    gap: 20px;
  }
}

/* Boutons de défilement pour la navigation */
.nav-scroll-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;
  opacity: 0;
  transition: opacity var(--transition-speed) ease,
    background-color var(--transition-speed) ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.nav-scroll-btn.visible {
  opacity: 1;
}

.nav-scroll-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-50%);
}

.nav-scroll-btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);
}

.nav-scroll-left {
  left: 5px;
}

.nav-scroll-right {
  right: 5px;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
    padding: var(--spacing-md);
  }

  header h1 {
    font-size: 1.3rem;
  }

  .main-nav {
    position: relative;
    padding: 0 var(--spacing-md);
  }

  .main-nav ul {
    justify-content: flex-start;
    padding: var(--spacing-xs) var(--spacing-md);
  }

  .main-nav a {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
  }
}

@media (max-width: 600px) {
  header h1 {
    font-size: 1.1rem;
  }

  main {
    padding: var(--spacing-md) var(--spacing-sm);
  }

  .screen {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
  }

  .simulation-info {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .input-action-group {
    flex-direction: column;
    align-items: stretch;
  }

  .input-action-group button {
    width: 100%;
  }

  .excel-container {
    margin: var(--spacing-md) 0;
  }

  .excel-footer {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: flex-start;
  }

  .zoom-controls {
    width: 100%;
    justify-content: center;
  }

  .modal-content {
    padding: var(--spacing-md);
    width: 95%;
    max-height: 85vh;
  }

  .tabs {
    gap: 0;
    flex-wrap: wrap;
  }

  .tab-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    flex-grow: 1;
  }

  .footer-content {
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .footer-section h3 {
    margin-bottom: var(--spacing-sm);
  }

  .footer-bottom {
    padding: var(--spacing-md);
  }

  /* Amélioration de la lisibilité sur petits écrans */
  p,
  li {
    font-size: var(--font-size-base);
    line-height: var(--line-height-base);
  }

  /* Amélioration des boutons sur petits écrans */
  button {
    min-height: 44px; /* Taille minimale pour les cibles tactiles */
  }
}

/* Optimisations pour les très petits écrans */
@media (max-width: 480px) {
  .header-controls {
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--spacing-sm);
  }

  .input-group {
    max-width: 100%;
  }

  .preview-row {
    font-size: var(--font-size-base);
    padding: var(--spacing-sm);
  }

  .excel-table {
    font-size: 0.9rem;
  }

  .btn-icon {
    width: 40px;
    height: 40px;
  }

  .modal-title {
    font-size: var(--font-size-lg);
  }

  .modal-content {
    padding: var(--spacing-sm);
  }
}
