/*
 * smartDiv - Styles de simulation moderne
 * Ce fichier contient les styles pour l'interface de simulation modernisée
 */

/* ===== ÉCRAN D'INTRODUCTION ===== */
#introduction-screen {
  max-width: 1000px;
  margin: 0 auto;
  padding: var(--space-xl) var(--space-lg);
}

#introduction-screen h2 {
  text-align: center;
  color: var(--color-primary);
  margin-bottom: var(--space-lg);
  position: relative;
  padding-bottom: var(--space-sm);
}

#introduction-screen h2::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background-color: var(--color-primary);
  border-radius: 2px;
}

.intro-description {
  text-align: center;
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-xl);
}

/* ===== FORMULAIRE DE DIVISION ===== */
.division-form {
  background-color: var(--color-background);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--space-xl);
  margin-bottom: var(--space-xl);
}

/* Mise en page en deux colonnes */
.form-columns {
  display: flex;
  gap: var(--space-xl);
  align-items: flex-start;
  width: 100%;
}

.form-column-left {
  flex: 1;
  min-width: 0;
}

.form-column-right {
  flex: 1;
  min-width: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .form-columns {
    flex-direction: column;
    gap: var(--space-lg);
  }

  .form-column-left,
  .form-column-right {
    width: 100%;
  }
}

.form-section {
  margin-bottom: var(--space-xl);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-md);
  color: var(--color-primary);
}

/* Styles pour les boutons checkradio */
.checkradio-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
}

.checkradio-option {
  position: relative;
}

.checkradio-option input[type="radio"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.checkradio-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-md) var(--space-lg);
  background-color: var(--color-background);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-weight: var(--font-weight-medium);
  text-align: center;
  min-height: 60px;
  user-select: none;
  box-shadow: var(--shadow-sm);
}

.checkradio-btn:hover {
  border-color: var(--color-primary);
  background-color: rgba(67, 97, 238, 0.05);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.checkradio-option input[type="radio"]:checked + .checkradio-btn {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  color: white;
  box-shadow: var(--shadow-lg);
}

.checkradio-option input[type="radio"]:focus + .checkradio-btn {
  outline: none;
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
}

.division-types,
.difficulty-options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
}

.radio-option label {
  width: 100%;
  text-align: center;
  padding: var(--space-md);
  transition: all var(--transition-fast);
}

.radio-option input[type="radio"]:checked + label {
  background-color: rgba(67, 97, 238, 0.1);
  border-color: var(--color-primary);
  color: var(--color-primary);
  font-weight: var(--font-weight-semibold);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.generate-numbers-container {
  display: flex;
  justify-content: center;
  margin-top: var(--space-md);
}

.input-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-lg);
  margin-bottom: var(--space-md);
}

.input-field {
  display: flex;
  flex-direction: column;
}

.input-field label {
  margin-bottom: var(--space-xs);
  font-weight: var(--font-weight-medium);
}

.input-field input {
  padding: var(--space-md);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-md);
  font-size: var(--font-size-lg);
  transition: all var(--transition-fast);
}

.input-field input:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
  outline: none;
}

.preview-row {
  text-align: center;
  font-size: var(--font-size-xl);
  margin: var(--space-lg) 0;
  padding: var(--space-md);
  background-color: var(--color-background-light);
  border-radius: var(--radius-md);
}

.preview-separator {
  margin: 0 var(--space-sm);
  color: var(--color-text-secondary);
}

.form-actions {
  display: flex;
  justify-content: center;
  margin-top: var(--space-xl);
}

/* ===== ÉCRAN DE SIMULATION ===== */
#simulation-screen {
  padding: var(--space-xl) var(--space-lg);
}

.simulation-header {
  background-color: var(--color-primary);
  color: white;
  padding: var(--space-md) var(--space-lg);
  margin-bottom: var(--space-xl);
  transition: all var(--transition-normal);
}

.simulation-header h2 {
  margin: 0;
  color: white;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.simulation-info {
  display: flex;
  gap: var(--space-md);
  margin-top: var(--space-xs);
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item strong {
  color: rgba(255, 255, 255, 0.8);
  font-weight: var(--font-weight-medium);
  margin-right: var(--space-xs);
  font-size: var(--font-size-sm);
}

.info-item span {
  font-weight: var(--font-weight-bold);
  color: white;
  font-size: var(--font-size-base);
}

.btn-restart {
  width: 36px;
  height: 36px;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  margin-left: auto;
}

.btn-restart:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.btn-restart i {
  font-size: var(--font-size-base);
}

.main-container {
  display: flex;
  gap: var(--space-xl);
  width: 100%;
  max-width: 100%;
}

@media (max-width: 992px) {
  .main-container {
    flex-direction: column;
  }

  .simulation-steps {
    width: 100%;
  }
}

.simulation-steps {
  display: flex;
  flex-direction: column;
  width: 400px;
  flex-shrink: 0;
}

.card-step {
  opacity: 0.8;
  transition: all var(--transition-normal);
  width: 100%;
}

.card-step:not(.hidden) {
  animation: slideUp 0.5s ease forwards;
  opacity: 1;
}

.card-step h3 {
  color: var(--color-primary);
  margin-top: 0;
  margin-bottom: var(--space-md);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.card-step h3::before {
  content: "";
  display: inline-block;
  width: 24px;
  height: 24px;
  background-color: var(--color-primary);
  border-radius: 50%;
  color: white;
  text-align: center;
  line-height: 24px;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
}

.instruction-text {
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-md);
  line-height: var(--line-height-relaxed);
}

.highlight-dividend,
.highlight-divisor {
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  padding: 0 var(--space-xs);
  background-color: rgba(67, 97, 238, 0.1);
  border-radius: var(--radius-sm);
}

.highlight-divisor {
  color: var(--color-secondary-dark);
  background-color: rgba(60, 207, 207, 0.1);
}

.input-action-group {
  display: flex;
  gap: var(--space-sm);
  margin-bottom: var(--space-md);
}

.input-action-group input {
  flex: 1;
  padding: var(--space-md);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-md);
  font-size: var(--font-size-lg);
  transition: all var(--transition-fast);
}

.input-action-group input:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
  outline: none;
}

.digit-selection {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-sm);
  margin-bottom: var(--space-md);
}

.digit {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  background-color: var(--color-background-light);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.digit.selected {
  background-color: var(--color-primary);
  color: white;
}

.arrow-lowering {
  font-size: var(--font-size-2xl);
  color: var(--color-accent);
  animation: pulse 1.5s infinite;
}

/* ===== RÉSULTAT FINAL ===== */
#final-summary {
  border-left-color: var(--color-success);
}

#final-summary h3 {
  color: var(--color-success);
}

.final-formula {
  font-size: var(--font-size-xl);
  text-align: center;
  margin: var(--space-lg) 0;
  padding: var(--space-md);
  background-color: var(--color-background-light);
  border-radius: var(--radius-md);
  font-family: var(--font-mono);
}

.final-congrats {
  text-align: center;
  font-size: var(--font-size-lg);
  color: var(--color-success);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--space-lg);
}
