/*
 * smartDiv - Styles de simulation moderne
 * Ce fichier contient les styles pour l'interface de simulation modernisée
 */

/* ===== ÉCRAN D'INTRODUCTION ===== */
#introduction-screen {
  max-width: 1000px;
  margin: 0 auto;
  padding: var(--space-xl) var(--space-lg);
}

#introduction-screen h2 {
  text-align: center;
  color: var(--color-primary);
  margin-bottom: var(--space-lg);
  position: relative;
  padding-bottom: var(--space-sm);
}

#introduction-screen h2::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background-color: var(--color-primary);
  border-radius: 2px;
}

.intro-description {
  text-align: center;
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin-bottom: var(--space-xl);
}

/* ===== FORMULAIRE DE DIVISION ===== */
.division-form {
  background-color: var(--color-background);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--space-xl);
  margin-bottom: var(--space-xl);
}

.form-section {
  margin-bottom: var(--space-xl);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-md);
  color: var(--color-primary);
}

.division-types,
.difficulty-options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
}

.radio-option label {
  width: 100%;
  text-align: center;
  padding: var(--space-md);
  transition: all var(--transition-fast);
}

.radio-option input[type="radio"]:checked + label {
  background-color: rgba(67, 97, 238, 0.1);
  border-color: var(--color-primary);
  color: var(--color-primary);
  font-weight: var(--font-weight-semibold);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.generate-numbers-container {
  display: flex;
  justify-content: center;
  margin-top: var(--space-md);
}

.input-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-lg);
  margin-bottom: var(--space-md);
}

.input-field {
  display: flex;
  flex-direction: column;
}

.input-field label {
  margin-bottom: var(--space-xs);
  font-weight: var(--font-weight-medium);
}

.input-field input {
  padding: var(--space-md);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-md);
  font-size: var(--font-size-lg);
  transition: all var(--transition-fast);
}

.input-field input:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
  outline: none;
}

.preview-row {
  text-align: center;
  font-size: var(--font-size-xl);
  margin: var(--space-lg) 0;
  padding: var(--space-md);
  background-color: var(--color-background-light);
  border-radius: var(--radius-md);
}

.preview-separator {
  margin: 0 var(--space-sm);
  color: var(--color-text-secondary);
}

.form-actions {
  display: flex;
  justify-content: center;
  margin-top: var(--space-xl);
}

/* ===== ÉCRAN DE SIMULATION ===== */
#simulation-screen {
  padding: var(--space-xl) var(--space-lg);
}

.simulation-header {
  background-color: var(--color-primary);
  color: white;
  padding: var(--space-md) var(--space-lg);
  margin-bottom: var(--space-xl);
  transition: all var(--transition-normal);
}

.simulation-header h2 {
  margin: 0;
  color: white;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.simulation-info {
  display: flex;
  gap: var(--space-md);
  margin-top: var(--space-xs);
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item strong {
  color: rgba(255, 255, 255, 0.8);
  font-weight: var(--font-weight-medium);
  margin-right: var(--space-xs);
  font-size: var(--font-size-sm);
}

.info-item span {
  font-weight: var(--font-weight-bold);
  color: white;
  font-size: var(--font-size-base);
}

.btn-restart {
  width: 36px;
  height: 36px;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  margin-left: auto;
}

.btn-restart:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.btn-restart i {
  font-size: var(--font-size-base);
}

.main-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-xl);
}

@media (max-width: 992px) {
  .main-container {
    grid-template-columns: 1fr;
  }
}

.simulation-steps {
  display: flex;
  flex-direction: column;
  width: 500px;
}

.card-step {
  opacity: 0.8;
  transition: all var(--transition-normal);
  width: 100%;
}

.card-step:not(.hidden) {
  animation: slideUp 0.5s ease forwards;
  opacity: 1;
}

.card-step h3 {
  color: var(--color-primary);
  margin-top: 0;
  margin-bottom: var(--space-md);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.card-step h3::before {
  content: "";
  display: inline-block;
  width: 24px;
  height: 24px;
  background-color: var(--color-primary);
  border-radius: 50%;
  color: white;
  text-align: center;
  line-height: 24px;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
}

.instruction-text {
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-md);
  line-height: var(--line-height-relaxed);
}

.highlight-dividend,
.highlight-divisor {
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  padding: 0 var(--space-xs);
  background-color: rgba(67, 97, 238, 0.1);
  border-radius: var(--radius-sm);
}

.highlight-divisor {
  color: var(--color-secondary-dark);
  background-color: rgba(60, 207, 207, 0.1);
}

.input-action-group {
  display: flex;
  gap: var(--space-sm);
  margin-bottom: var(--space-md);
}

.input-action-group input {
  flex: 1;
  padding: var(--space-md);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-md);
  font-size: var(--font-size-lg);
  transition: all var(--transition-fast);
}

.input-action-group input:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
  outline: none;
}

.digit-selection {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-sm);
  margin-bottom: var(--space-md);
}

.digit {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  background-color: var(--color-background-light);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.digit.selected {
  background-color: var(--color-primary);
  color: white;
}

.arrow-lowering {
  font-size: var(--font-size-2xl);
  color: var(--color-accent);
  animation: pulse 1.5s infinite;
}

/* ===== RÉSULTAT FINAL ===== */
#final-summary {
  border-left-color: var(--color-success);
}

#final-summary h3 {
  color: var(--color-success);
}

.final-formula {
  font-size: var(--font-size-xl);
  text-align: center;
  margin: var(--space-lg) 0;
  padding: var(--space-md);
  background-color: var(--color-background-light);
  border-radius: var(--radius-md);
  font-family: var(--font-mono);
}

.final-congrats {
  text-align: center;
  font-size: var(--font-size-lg);
  color: var(--color-success);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--space-lg);
}
