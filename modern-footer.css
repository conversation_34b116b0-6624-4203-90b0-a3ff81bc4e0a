/* 
 * smartDiv - Styles du pied de page et des sections secondaires modernes
 * Ce fichier contient les styles pour le pied de page et les sections secondaires modernisés
 */

/* ===== PIED DE PAGE ===== */
footer {
  background-color: var(--color-primary-dark);
  color: rgba(255, 255, 255, 0.9);
  padding: var(--space-2xl) 0 var(--space-lg);
  margin-top: var(--space-3xl);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-xl);
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-lg);
}

.footer-section h3 {
  color: white;
  font-size: var(--font-size-lg);
  margin-top: 0;
  margin-bottom: var(--space-md);
  position: relative;
  padding-bottom: var(--space-sm);
}

.footer-section h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: var(--color-accent);
  border-radius: 1.5px;
}

.footer-section p {
  color: rgba(255, 255, 255, 0.7);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-md);
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section li {
  margin-bottom: var(--space-sm);
}

.footer-section a {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.footer-section a:hover {
  color: white;
  text-decoration: none;
  transform: translateX(3px);
}

.footer-section a i {
  color: var(--color-accent);
}

.social-links {
  display: flex;
  gap: var(--space-md);
  margin-top: var(--space-md);
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transition: all var(--transition-fast);
}

.social-links a:hover {
  background-color: var(--color-accent);
  transform: translateY(-3px);
}

.social-links a i {
  font-size: var(--font-size-lg);
  color: white;
}

.footer-preferences {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.footer-btn {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  border: none;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.footer-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.footer-btn i {
  font-size: var(--font-size-md);
}

.footer-bottom {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-lg);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: var(--space-xl);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--space-md);
}

.footer-bottom p {
  margin: 0;
  font-size: var(--font-size-sm);
  color: rgba(255, 255, 255, 0.6);
}

.footer-version {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  flex-wrap: wrap;
}

.footer-version a {
  color: rgba(255, 255, 255, 0.6);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.footer-version a:hover {
  color: white;
}

@media (max-width: 768px) {
  .footer-bottom {
    flex-direction: column;
    align-items: flex-start;
  }
}

/* ===== SECTIONS SECONDAIRES ===== */
.about-content {
  margin-bottom: var(--space-xl);
}

.about-content h3 {
  color: var(--color-primary);
  margin-top: var(--space-lg);
  margin-bottom: var(--space-md);
}

.about-content ul {
  padding-left: var(--space-lg);
  margin-bottom: var(--space-lg);
}

.about-content li {
  margin-bottom: var(--space-sm);
  position: relative;
}

.about-content li::before {
  content: '•';
  color: var(--color-primary);
  font-weight: bold;
  display: inline-block;
  width: 1em;
  margin-left: -1em;
}

/* ===== DARK MODE ===== */
body.dark-mode {
  --color-background: #1a1a2e;
  --color-background-light: #242444;
  --color-text-primary: #e6e6e6;
  --color-text-secondary: #b0b0b0;
  --color-border: #3a3a5a;
  
  background-color: var(--color-background);
  color: var(--color-text-primary);
}

body.dark-mode .card,
body.dark-mode .modal-content,
body.dark-mode .panel,
body.dark-mode .toast {
  background-color: var(--color-background-light);
}

body.dark-mode .excel-sheet-wrapper {
  background-color: var(--color-background-light);
}

body.dark-mode #excel-table {
  background-color: var(--color-background);
}

body.dark-mode .btn-tool,
body.dark-mode .excel-select {
  background-color: var(--color-background);
  color: var(--color-text-primary);
}

body.dark-mode .form-control,
body.dark-mode .input-action-group input {
  background-color: var(--color-background);
  color: var(--color-text-primary);
  border-color: var(--color-border);
}

body.dark-mode .form-control::placeholder {
  color: var(--color-text-secondary);
}

body.dark-mode header {
  background-color: var(--color-background-light);
}

body.dark-mode .excel-toolbar,
body.dark-mode .excel-footer,
body.dark-mode .excel-container {
  background-color: var(--color-background);
}

body.dark-mode #excel-table th {
  background-color: var(--color-background-light);
}

/* ===== ANIMATIONS SUPPLÉMENTAIRES ===== */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {transform: translateY(0);}
  40% {transform: translateY(-10px);}
  60% {transform: translateY(-5px);}
}

.animate-bounce {
  animation: bounce 2s infinite;
}

@keyframes spin {
  0% {transform: rotate(0deg);}
  100% {transform: rotate(360deg);}
}

.animate-spin {
  animation: spin 2s linear infinite;
}

/* ===== UTILITAIRES SUPPLÉMENTAIRES ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--color-primary); }
.text-secondary { color: var(--color-secondary); }
.text-accent { color: var(--color-accent); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }
.text-info { color: var(--color-info); }

.bg-primary { background-color: var(--color-primary); color: white; }
.bg-secondary { background-color: var(--color-secondary); color: white; }
.bg-accent { background-color: var(--color-accent); color: white; }
.bg-success { background-color: var(--color-success); color: white; }
.bg-warning { background-color: var(--color-warning); color: var(--color-text-primary); }
.bg-error { background-color: var(--color-error); color: white; }
.bg-info { background-color: var(--color-info); color: white; }
.bg-light { background-color: var(--color-background-light); }

.font-bold { font-weight: var(--font-weight-bold); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-regular { font-weight: var(--font-weight-regular); }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

.hidden { display: none !important; }
.invisible { visibility: hidden !important; }
.visible { visibility: visible !important; }

.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }

.position-relative { position: relative; }
.position-absolute { position: absolute; }
.position-fixed { position: fixed; }
.position-sticky { position: sticky; }
