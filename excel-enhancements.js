/**
 * Améliorations pour la feuille Excel
 * Ce script ajoute des fonctionnalités interactives à la feuille Excel
 */

class ExcelEnhancer {
  constructor() {
    // Éléments DOM
    this.excelContainer = document.querySelector('.excel-container');
    this.excelTable = document.getElementById('excel-table');
    this.excelToolbar = document.querySelector('.excel-toolbar');
    this.excelFooter = document.querySelector('.excel-footer');
    this.excelStatus = document.querySelector('.excel-status');

    // Variables d'état
    this.zoomLevel = 100;
    this.isGridVisible = false; // Désactivé par défaut pour éviter la double grille
    this.isHighlightMode = true;
    this.viewMode = 'standard'; // 'standard', 'compact', 'detailed'

    // Initialisation
    this.init();
  }

  /**
   * Initialise les améliorations de la feuille Excel
   */
  init() {
    // Charger les styles CSS
    this.loadStyles();

    // Créer la barre d'outils améliorée
    this.createEnhancedToolbar();

    // Créer le pied de page amélioré
    this.createEnhancedFooter();

    // Ajouter les événements
    this.setupEvents();

    // Initialiser les tooltips
    this.setupTooltips();

    // Appliquer les préférences sauvegardées
    this.loadPreferences();

    // Désactiver la grille par défaut pour éviter la double grille
    if (this.isGridVisible) {
      // Appliquer immédiatement la visibilité de la grille
      this.applyGridVisibility();
    }
  }

  /**
   * Charge les styles CSS pour les améliorations
   */
  loadStyles() {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = 'excel-improvements.css';
    document.head.appendChild(link);
  }

  /**
   * Crée une barre d'outils améliorée
   */
  createEnhancedToolbar() {
    if (!this.excelToolbar) return;

    // Sauvegarder le contenu original
    const originalContent = this.excelToolbar.innerHTML;

    // Créer la nouvelle barre d'outils
    this.excelToolbar.innerHTML = `
      <div class="excel-toolbar-left">
        <div class="excel-toolbar-title">
          <i class="fas fa-table"></i> Feuille de calcul
        </div>

        <div class="excel-toolbar-group">
          <button class="btn-tool" id="excel-export-image" title="Exporter en image">
            <i class="fas fa-image"></i> Image
          </button>
          <button class="btn-tool" id="excel-export-pdf" title="Exporter en PDF">
            <i class="fas fa-file-pdf"></i> PDF
          </button>
          <button class="btn-tool" id="excel-print" title="Imprimer">
            <i class="fas fa-print"></i> Imprimer
          </button>
        </div>

        <div class="excel-toolbar-group">
          <button class="btn-tool" id="excel-fullscreen" title="Plein écran">
            <i class="fas fa-expand"></i> Plein écran
          </button>
          <button class="btn-tool" id="excel-reset-zoom" title="Réinitialiser le zoom">
            <i class="fas fa-sync-alt"></i> Réinitialiser
          </button>
        </div>
      </div>

      <div class="excel-toolbar-right">
        <div class="excel-toolbar-group">
          <select class="excel-select" id="excel-view-mode" title="Mode d'affichage">
            <option value="standard">Affichage standard</option>
            <option value="compact">Affichage compact</option>
            <option value="detailed">Affichage détaillé</option>
          </select>
        </div>

        <div class="excel-toolbar-group">
          <button class="btn-tool ${this.isGridVisible ? 'active' : ''}" id="excel-toggle-grid" title="Afficher/Masquer la grille">
            <i class="fas fa-border-all"></i> Grille
          </button>
          <button class="btn-tool ${this.isHighlightMode ? 'active' : ''}" id="excel-toggle-highlight" title="Activer/Désactiver le surlignage">
            <i class="fas fa-highlighter"></i> Surlignage
          </button>
        </div>
      </div>
    `;
  }

  /**
   * Fonction de création de légende supprimée
   */

  /**
   * Crée un pied de page amélioré
   */
  createEnhancedFooter() {
    if (!this.excelFooter) return;

    // Sauvegarder le contenu original
    const originalStatus = this.excelStatus ? this.excelStatus.innerHTML : '';

    // Créer le nouveau pied de page
    this.excelFooter.innerHTML = `
      <div class="excel-status">
        <div class="excel-status-item">
          <i class="fas fa-info-circle"></i>
          <span id="excel-status-text">Prêt</span>
        </div>
        <div class="excel-status-item">
          <i class="fas fa-calculator"></i>
          <span id="excel-operation-count">0 opérations</span>
        </div>
      </div>

      <div class="zoom-controls">
        <button class="zoom-btn" id="excel-zoom-out" title="Zoom arrière">
          <i class="fas fa-search-minus"></i>
        </button>
        <span id="zoom-level">${this.zoomLevel}%</span>
        <button class="zoom-btn" id="excel-zoom-in" title="Zoom avant">
          <i class="fas fa-search-plus"></i>
        </button>
      </div>
    `;
  }

  /**
   * Configure les événements pour les éléments interactifs
   */
  setupEvents() {
    // Boutons de la barre d'outils
    document.getElementById('excel-export-image')?.addEventListener('click', () => this.exportAsImage());
    document.getElementById('excel-export-pdf')?.addEventListener('click', () => this.exportAsPDF());
    document.getElementById('excel-print')?.addEventListener('click', () => this.printExcel());
    document.getElementById('excel-fullscreen')?.addEventListener('click', () => this.toggleFullscreen());
    document.getElementById('excel-reset-zoom')?.addEventListener('click', () => this.resetZoom());
    document.getElementById('excel-toggle-grid')?.addEventListener('click', () => this.toggleGrid());
    document.getElementById('excel-toggle-highlight')?.addEventListener('click', () => this.toggleHighlight());

    // Sélecteur de mode d'affichage
    document.getElementById('excel-view-mode')?.addEventListener('change', (e) => this.changeViewMode(e.target.value));

    // Contrôles de zoom
    document.getElementById('excel-zoom-in')?.addEventListener('click', () => this.zoomIn());
    document.getElementById('excel-zoom-out')?.addEventListener('click', () => this.zoomOut());

    // Événements clavier
    document.addEventListener('keydown', (e) => {
      // Ctrl + Plus pour zoomer
      if (e.ctrlKey && e.key === '+') {
        e.preventDefault();
        this.zoomIn();
      }
      // Ctrl + Moins pour dézoomer
      else if (e.ctrlKey && e.key === '-') {
        e.preventDefault();
        this.zoomOut();
      }
      // Ctrl + 0 pour réinitialiser le zoom
      else if (e.ctrlKey && e.key === '0') {
        e.preventDefault();
        this.resetZoom();
      }
      // Ctrl + G pour activer/désactiver la grille
      else if (e.ctrlKey && e.key === 'g') {
        e.preventDefault();
        this.toggleGrid();
      }
      // Échap pour quitter le mode plein écran
      else if (e.key === 'Escape' && document.fullscreenElement) {
        document.exitFullscreen();
      }
    });
  }

  /**
   * Configure les tooltips pour les cellules
   */
  setupTooltips() {
    // Créer l'élément tooltip
    const tooltip = document.createElement('div');
    tooltip.className = 'excel-tooltip';
    document.body.appendChild(tooltip);

    // Ajouter les événements pour les cellules
    if (this.excelTable) {
      this.excelTable.addEventListener('mouseover', (e) => {
        const cell = e.target.closest('td');
        if (cell && cell.textContent.trim()) {
          const rect = cell.getBoundingClientRect();
          tooltip.textContent = this.getCellDescription(cell);
          tooltip.style.top = `${rect.bottom + 10}px`;
          tooltip.style.left = `${rect.left + rect.width / 2 - tooltip.offsetWidth / 2}px`;
          tooltip.classList.add('visible');
        }
      });

      this.excelTable.addEventListener('mouseout', () => {
        tooltip.classList.remove('visible');
      });
    }
  }

  /**
   * Obtient une description pour une cellule
   * @param {HTMLElement} cell - La cellule
   * @returns {string} - La description
   */
  getCellDescription(cell) {
    // Déterminer le type de cellule
    if (cell.querySelector('.quotient-cell')) {
      return 'Chiffre du quotient';
    } else if (cell.querySelector('.multiplication-cell')) {
      return 'Résultat de la multiplication';
    } else if (cell.querySelector('.subtraction-cell')) {
      return 'Résultat de la soustraction';
    } else if (cell.querySelector('.lowering-cell')) {
      return 'Chiffre abaissé';
    } else if (cell.querySelector('.result-cell')) {
      return 'Résultat final';
    } else if (cell.querySelector('.divisor-value')) {
      return 'Chiffre du diviseur';
    } else if (cell.querySelector('.dividend-value')) {
      return 'Chiffre du dividende';
    }

    return cell.textContent.trim();
  }

  /**
   * Charge les préférences sauvegardées
   */
  loadPreferences() {
    try {
      const preferences = JSON.parse(localStorage.getItem('excelPreferences')) || {};

      // Appliquer les préférences
      this.zoomLevel = preferences.zoomLevel || 100;
      this.isGridVisible = preferences.isGridVisible !== undefined ? preferences.isGridVisible : true;
      this.isHighlightMode = preferences.isHighlightMode !== undefined ? preferences.isHighlightMode : true;
      this.viewMode = preferences.viewMode || 'standard';

      // Mettre à jour l'interface
      this.applyZoom();
      this.applyGridVisibility();
      this.applyHighlightMode();
      this.applyViewMode();

      // Mettre à jour les contrôles
      document.getElementById('zoom-level').textContent = `${this.zoomLevel}%`;
      document.getElementById('excel-view-mode').value = this.viewMode;
      document.getElementById('excel-toggle-grid').classList.toggle('active', this.isGridVisible);
      document.getElementById('excel-toggle-highlight').classList.toggle('active', this.isHighlightMode);
    } catch (error) {
      console.error('Erreur lors du chargement des préférences Excel:', error);
    }
  }

  /**
   * Sauvegarde les préférences
   */
  savePreferences() {
    try {
      const preferences = {
        zoomLevel: this.zoomLevel,
        isGridVisible: this.isGridVisible,
        isHighlightMode: this.isHighlightMode,
        viewMode: this.viewMode
      };

      localStorage.setItem('excelPreferences', JSON.stringify(preferences));
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des préférences Excel:', error);
    }
  }

  /**
   * Exporte la feuille Excel en image
   */
  exportAsImage() {
    // Simuler l'exportation en image
    this.showToast('Exportation en image en cours...', 'info');

    setTimeout(() => {
      this.showToast('Feuille Excel exportée en image avec succès!', 'success');
    }, 1500);
  }

  /**
   * Exporte la feuille Excel en PDF
   */
  exportAsPDF() {
    // Simuler l'exportation en PDF
    this.showToast('Exportation en PDF en cours...', 'info');

    setTimeout(() => {
      this.showToast('Feuille Excel exportée en PDF avec succès!', 'success');
    }, 1500);
  }

  /**
   * Imprime la feuille Excel
   */
  printExcel() {
    // Préparer l'impression
    this.showToast('Préparation de l\'impression...', 'info');

    // Créer une feuille de style pour l'impression
    const printStyle = document.createElement('style');
    printStyle.innerHTML = `
      @media print {
        body * {
          visibility: hidden;
        }
        #excel-table, #excel-table * {
          visibility: visible;
        }
        #excel-table {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
        }
      }
    `;
    document.head.appendChild(printStyle);

    // Imprimer
    setTimeout(() => {
      window.print();
      document.head.removeChild(printStyle);
    }, 500);
  }

  /**
   * Bascule en mode plein écran
   */
  toggleFullscreen() {
    const excelWrapper = document.querySelector('.excel-sheet-wrapper');
    if (!excelWrapper) return;

    if (!document.fullscreenElement) {
      excelWrapper.requestFullscreen().catch(err => {
        this.showToast(`Erreur lors du passage en plein écran: ${err.message}`, 'error');
      });

      // Mettre à jour le bouton
      const fullscreenBtn = document.getElementById('excel-fullscreen');
      if (fullscreenBtn) {
        fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i> Quitter';
        fullscreenBtn.title = 'Quitter le plein écran';
      }
    } else {
      document.exitFullscreen();

      // Mettre à jour le bouton
      const fullscreenBtn = document.getElementById('excel-fullscreen');
      if (fullscreenBtn) {
        fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i> Plein écran';
        fullscreenBtn.title = 'Plein écran';
      }
    }
  }

  /**
   * Réinitialise le zoom
   */
  resetZoom() {
    this.zoomLevel = 100;
    this.applyZoom();
    document.getElementById('zoom-level').textContent = '100%';
    this.savePreferences();
    this.showToast('Zoom réinitialisé', 'info');
  }

  /**
   * Applique le niveau de zoom
   */
  applyZoom() {
    if (this.excelTable) {
      this.excelTable.style.transform = `scale(${this.zoomLevel / 100})`;
    }
  }

  /**
   * Augmente le zoom
   */
  zoomIn() {
    if (this.zoomLevel < 200) {
      this.zoomLevel += 10;
      this.applyZoom();
      document.getElementById('zoom-level').textContent = `${this.zoomLevel}%`;
      this.savePreferences();
    }
  }

  /**
   * Diminue le zoom
   */
  zoomOut() {
    if (this.zoomLevel > 50) {
      this.zoomLevel -= 10;
      this.applyZoom();
      document.getElementById('zoom-level').textContent = `${this.zoomLevel}%`;
      this.savePreferences();
    }
  }

  /**
   * Bascule l'affichage de la grille
   */
  toggleGrid() {
    this.isGridVisible = !this.isGridVisible;
    this.applyGridVisibility();

    // Mettre à jour le bouton
    const gridBtn = document.getElementById('excel-toggle-grid');
    if (gridBtn) {
      gridBtn.classList.toggle('active', this.isGridVisible);
    }

    this.savePreferences();
    this.showToast(`Grille ${this.isGridVisible ? 'activée' : 'désactivée'}`, 'info');
  }

  /**
   * Applique la visibilité de la grille
   */
  applyGridVisibility() {
    if (this.excelTable) {
      // Ajouter ou supprimer la classe no-grid
      this.excelTable.classList.toggle('no-grid', !this.isGridVisible);

      // Appliquer un style personnalisé pour les bordures des cellules
      const cellBorderStyle = this.isGridVisible ? '1px solid var(--excel-border)' : 'none';

      // Appliquer le style à toutes les cellules
      const cells = this.excelTable.querySelectorAll('td');
      cells.forEach(cell => {
        cell.style.border = cellBorderStyle;
      });

      // Appliquer le style aux en-têtes
      const headers = this.excelTable.querySelectorAll('th');
      headers.forEach(header => {
        header.style.border = cellBorderStyle;
      });
    }
  }

  /**
   * Bascule le mode de surlignage
   */
  toggleHighlight() {
    this.isHighlightMode = !this.isHighlightMode;
    this.applyHighlightMode();

    // Mettre à jour le bouton
    const highlightBtn = document.getElementById('excel-toggle-highlight');
    if (highlightBtn) {
      highlightBtn.classList.toggle('active', this.isHighlightMode);
    }

    this.savePreferences();
    this.showToast(`Surlignage ${this.isHighlightMode ? 'activé' : 'désactivé'}`, 'info');
  }

  /**
   * Applique le mode de surlignage
   */
  applyHighlightMode() {
    if (this.excelTable) {
      this.excelTable.classList.toggle('highlight-mode', this.isHighlightMode);
    }
  }

  /**
   * Change le mode d'affichage
   * @param {string} mode - Le mode d'affichage ('standard', 'compact', 'detailed')
   */
  changeViewMode(mode) {
    this.viewMode = mode;
    this.applyViewMode();
    this.savePreferences();
    this.showToast(`Mode d'affichage: ${this.getViewModeName(mode)}`, 'info');
  }

  /**
   * Applique le mode d'affichage
   */
  applyViewMode() {
    if (this.excelTable) {
      // Supprimer les classes existantes
      this.excelTable.classList.remove('compact-view', 'detailed-view');

      // Ajouter la classe appropriée
      if (this.viewMode === 'compact') {
        this.excelTable.classList.add('compact-view');
      } else if (this.viewMode === 'detailed') {
        this.excelTable.classList.add('detailed-view');
      }
    }
  }

  /**
   * Obtient le nom du mode d'affichage
   * @param {string} mode - Le mode d'affichage
   * @returns {string} - Le nom du mode
   */
  getViewModeName(mode) {
    switch (mode) {
      case 'compact': return 'Compact';
      case 'detailed': return 'Détaillé';
      default: return 'Standard';
    }
  }

  /**
   * Affiche un toast de notification
   * @param {string} message - Le message à afficher
   * @param {string} type - Le type de notification ('info', 'success', 'error', 'warning')
   */
  showToast(message, type = 'info') {
    // Vérifier si le conteneur de toast existe
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
      toastContainer = document.createElement('div');
      toastContainer.className = 'toast-container';
      document.body.appendChild(toastContainer);
    }

    // Créer le toast
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;

    // Ajouter l'icône appropriée
    let icon = '';
    switch (type) {
      case 'success': icon = '<i class="fas fa-check-circle"></i>'; break;
      case 'error': icon = '<i class="fas fa-exclamation-circle"></i>'; break;
      case 'warning': icon = '<i class="fas fa-exclamation-triangle"></i>'; break;
      default: icon = '<i class="fas fa-info-circle"></i>';
    }

    toast.innerHTML = `${icon} ${message}`;
    toastContainer.appendChild(toast);

    // Supprimer le toast après l'animation
    setTimeout(() => {
      toast.remove();
    }, 3500);
  }

  /**
   * Met à jour le compteur d'opérations
   * @param {number} count - Le nombre d'opérations
   */
  updateOperationCount(count) {
    const operationCount = document.getElementById('excel-operation-count');
    if (operationCount) {
      operationCount.textContent = `${count} opération${count !== 1 ? 's' : ''}`;
    }
  }

  /**
   * Met à jour le statut Excel
   * @param {string} status - Le statut à afficher
   */
  updateStatus(status) {
    const statusText = document.getElementById('excel-status-text');
    if (statusText) {
      statusText.textContent = status;
    }
  }
}

// Initialiser les améliorations Excel lorsque le DOM est chargé
document.addEventListener('DOMContentLoaded', () => {
  // Attendre un peu pour s'assurer que tous les éléments sont chargés
  setTimeout(() => {
    const excelEnhancer = new ExcelEnhancer();

    // Exposer l'instance pour une utilisation ultérieure
    window.excelEnhancer = excelEnhancer;
  }, 500);
});
